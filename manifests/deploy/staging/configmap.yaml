apiVersion: v1
kind: ConfigMap
metadata:
  name: mms-product-configmap
  namespace: mms
data:
  active: dev
  mms_db_port: "30001"
  mms_db_schema: "mms"
  mms_service_db_url: "db-rancher-staging.nat.hkmpcl.com.hk"
  image_upload_url: https://cdn-media-upload-staging.hkmpcl.com.hk/oapi/v1/images/mms/upload
  image_status_url: https://cdn-media-upload-staging.hkmpcl.com.hk/oapi/v1/images/status
  image_delete_url: https://cdn-media-upload-staging.hkmpcl.com.hk/oapi/v1/images/
  image_batch_delete_url: https://cdn-media-upload-staging.hkmpcl.com.hk/oapi/v1/images/delete
  image_domain: cdn-media-staging.hkmpcl.com.hk,images.hktvmall.com,images.hktv-img.com,images.hokobuy.com,mmstest-images.hkmpcl.com.hk,dyn-image-server-dev.hkmpcl.com.hk,dyn-image-server-dev2.hkmpcl.com.hk,eese-dyn-image-server-staging.hkmpcl.com.hk,mms-dyn-image-server-staging.hkmpcl.com.hk
  image_domain_modify: images.hktv-img.com,images.hktvmall.com,images.hkmpcl.com.hk,hktv-img.hkmpcl.com.hk,www.hkmpcl.com.hk
  video_upload_url: https://vid-upload-staging.hkmpcl.com.hk/s2s/v1/videos
  video_status_url: https://vid-upload-staging.hkmpcl.com.hk/s2s/v1/videos/status
  glowroot_collector_address: http://hktv-glowroot-svc.kube-logging.svc.cluster.local:8181
  product_master_api_url: http://shoalter-product-master-service-svc.shoalter-see-product-master.svc.cluster.local:8080
  hktv_image_url: https://hktv-img.hkmpcl.com.hk/images
  inventory_api_url: http://mms-inventory-api-svc.mms.svc.cluster.local:8080/inventory
  mms_store_api_url: http://mms-store-api-svc.mms.svc.cluster.local:8080/store
  little_mall_base_url: 'https://littlemall-internal-staging.hkmpcl.com.hk'
  mms_gw_url: "http://hktv-mmsgw-svc:8080"
  feature_toggle_save_product_mutithread: "true"
  feature_toggle_send_mpps_queue: "true"
  hybris_url: "https://www.hkmpcl.com.hk/hktvwebservices"
  db_hikari_maximum_pool_size: "50"
  hktvmall_external_url: "https://www.hkmpcl.com.hk"
  promotion_api_url: http://mms-promotion-api-svc.mms.svc.cluster.local:8080/promotion
  rabbitmq_hktv_single_queue_name: "mms_product_single-product-info-hktvmall_queue"
  rabbitmq_hktv_batch_mq_queue_name: "mms_product_product-info-hktvmall_mq_queue"
  rabbitmq_hybris_product_update_result_queue_name: "mms_product_update_product_result_from_hktvmall_hybris_to_mms_product_queue"
  product_process_size_limit: "500"
  rabbitmq_hktv_address: "rabbitmq-staging1.hkmpcl.com.hk:5672,rabbitmq-staging2.hkmpcl.com.hk:5672,rabbitmq-staging3.hkmpcl.com.hk:5672"
  rabbitmq_hktv_port: "5672"
  product_master_rabbit_mq_address: "rabbitmq-staging1.hkmpcl.com.hk:5672,rabbitmq-staging2.hkmpcl.com.hk:5672,rabbitmq-staging3.hkmpcl.com.hk:5672"
  # Http client config
  httpclient_pool_connection_default_max_route: "20"
  notification_url: "http://shoalter-merchant-notification-system-svc.shoalter-merchant-notification-system.svc.cluster.local"
  approval_daily_report_receiver: "<EMAIL>"
  product_price_alert_internal_user_receiver: "<EMAIL>"
  product_price_alert_onebound_threads: "4"
  mms_third_party_sku_url: "http://mms-third-party-sku-svc:8080"
  mms_save_product_record_expired_days: "90"
  mms_housekeeping_limit_counts: "5000"
  mms_setting_api_url: "http://mms-setting-svc.mms.svc.cluster.local:8080/setting"
  hybris_api_batch_size: "50"
  hybris_api_update_mainland_same_price_size: "3"
  mms_db_price_alert_data_size: "5"
  tmall_cronjob_processing_variant_limit: "2"
  product_master_update_visibility_size: "5"
  async_ecom_engine_pool_size: "2"
  async_ecom_engine_update_invisible_fixed_thread_pool: "4"

  xss_protection_enabled: "true"

  #cornjob
  job-command.sh: |
    #!/bin/sh
    url=$JOB_URL
    echo "Start job $JOB_NAME"
    response=$(curl -m 600 --write-out '\nHTTP %{http_code}\n' --silent --output - --request POST "$url")
    curl_exit_code=$?
    http_status=$(echo "$response" | sed -n 's/HTTP \([0-9]*\)/\1/p')
    response_body=$(echo "$response" | sed '1,/^$/d')
    echo "curl_exit_code: $curl_exit_code"
    if [[ $curl_exit_code -ne 0 ]]; then
      echo "cURL command failed with exit code: $curl_exit_code"
      exit 1
    fi
    echo "cURL command output: $response_body"
    if [[ "$http_status" -eq 200 ]]; then
      echo "Job succeeded: $http_status"
      exit 0
    else
      echo "Job failed: $http_status"
      exit 1
    fi

  # odd-week-job-command: weekly job command script that runs only on odd weeks
  # This script is a wrapper around the original job command script, which is sourced and executed only if the current week number is odd
  odd-week-job-command.sh: |
    #!/bin/sh
    # Get the current minute in two-digit format (e.g., "07" or "23")
    current_minute=$(date +%M)
    # Extract the tens digit from the current minute.
    tens_digit=${current_minute%${current_minute#?}}
    # Convert the tens digit from a string to a number.
    tens_digit_value=$((10#$tens_digit))

    # Check if the tens digit is odd. If tens_digit_value modulo 2 equals 1, then it is odd
    if [ $((tens_digit_value % 2)) -eq 1 ]; then
      echo "tens_digit minute $tens_digit_value is odd. Proceeding with job execution."
      # Execute the original job command script while preserving its logic and exit code
      source /etc/config/command-config/job-command.sh
    else
      echo "tens_digit minute $tens_digit_value is even. Skipping job execution."
      exit 0
    fi
