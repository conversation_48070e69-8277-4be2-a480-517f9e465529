apiVersion: batch/v1
kind: CronJob
metadata:
  name: mms-product-cronjob-partner-product-price-check
  namespace: mms
spec:
  schedule: "TZ=Asia/Hong_Kong 0 * * * *" # every 60 minutes
  startingDeadlineSeconds: 600
  concurrencyPolicy: Forbid
  jobTemplate:
    spec:
      suspend: true
      activeDeadlineSeconds: 600
      backoffLimit: 3
      parallelism: 1
      completions: 1
      template:
        spec:
          containers:
            - name: mms-product-cronjob
              image: alpine/curl:3.14
              imagePullPolicy: IfNotPresent
              volumeMounts:
                - name: command-config-volume
                  mountPath: /etc/config/command-config
              env:
                - name: JOB_NAME
                  value: "mms-product-cronjob-partner-product-price-check"
                - name: JOB_URL
                  value: "http://mms-product-api-svc.mms.svc.cluster.local:8080/product/api/s2s/cronjob/partner-product-price/save-record"
              command:
                - /bin/sh
                - -c
                - |
                  source /etc/config/command-config/job-command.sh
          volumes:
            - name: command-config-volume
              configMap:
                name: mms-product-configmap
          restartPolicy: Never
