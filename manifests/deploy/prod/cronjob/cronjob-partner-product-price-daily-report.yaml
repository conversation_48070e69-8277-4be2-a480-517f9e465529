apiVersion: batch/v1
kind: CronJob
metadata:
  name: mms-product-cronjob-partner-product-price-report
  namespace: mms
spec:
  schedule: "TZ=Asia/Hong_Kong 30 9 * * *"
  startingDeadlineSeconds: 600
  concurrencyPolicy: Forbid
  jobTemplate:
    spec:
      activeDeadlineSeconds: 600
      backoffLimit: 3
      parallelism: 1
      completions: 1
      template:
        spec:
          containers:
            - name: mms-product-cronjob
              image: alpine/curl:3.14
              imagePullPolicy: IfNotPresent
              volumeMounts:
                - name: command-config-volume
                  mountPath: /etc/config/command-config
              env:
                - name: JOB_NAME
                  value: "mms-product-cronjob-partner-product-price-report"
                - name: JOB_URL
                  value: "http://mms-product-api-svc.mms.svc.cluster.local:8080/product/api/s2s/cronjob/partner-product-price/daily-report"
              command:
                - /bin/sh
                - -c
                - |
                  source /etc/config/command-config/job-command.sh
          volumes:
            - name: command-config-volume
              configMap:
                name: mms-product-configmap
          restartPolicy: Never
