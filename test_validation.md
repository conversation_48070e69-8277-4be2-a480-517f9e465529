# Unit Tests for groupRmInfoByMonitorId Method

## Summary

I have successfully generated comprehensive unit tests for the `groupRmInfoByMonitorId` method in `ProductPriceMonitorCheckService`. The tests cover all important scenarios and edge cases.

## Test Cases Created

### 1. Normal Case - Valid Mapping Data
- **Test**: `groupRmInfoByMonitorId_WithValidMappingData_ShouldReturnCorrectGrouping`
- **Purpose**: Tests the main functionality with valid input data
- **Scenario**: Multiple monitor IDs with different RM codes
- **Expected**: Correct grouping by monitor ID with proper RM info mapping

### 2. Empty Mapping Data
- **Test**: `groupRmInfoByMonitorId_WithEmptyMappingData_ShouldReturnEmptyMap`
- **Purpose**: Tests behavior with empty input list
- **Scenario**: Empty list of mapping data
- **Expected**: Returns empty map (not null)

### 3. Null Mapping Data
- **Test**: `groupRmInfoByMonitorId_WithNullMappingData_ShouldReturnEmptyMap`
- **Purpose**: Tests null safety
- **Scenario**: Null input for mapping data
- **Expected**: Returns empty map (not null)

### 4. Missing RM Info
- **Test**: `groupRmInfoByMonitorId_WithMissingRmInfo_ShouldFilterOutNullValues`
- **Purpose**: Tests filtering of null values when RM codes don't exist in info map
- **Scenario**: Some RM codes in mapping data don't have corresponding info
- **Expected**: Filters out null values, only returns valid RM info

### 5. Empty RM Info Map
- **Test**: `groupRmInfoByMonitorId_WithEmptyRmInfoMap_ShouldReturnEmptyLists`
- **Purpose**: Tests behavior when RM info map is empty
- **Scenario**: Valid mapping data but empty RM info map
- **Expected**: Returns map with monitor IDs but empty lists

### 6. Multiple Monitor IDs with Same RM Codes
- **Test**: `groupRmInfoByMonitorId_WithMultipleMonitorIdsSameRmCodes_ShouldGroupCorrectly`
- **Purpose**: Tests reuse of same RM info objects across different monitor IDs
- **Scenario**: Different monitor IDs sharing the same RM codes
- **Expected**: Same RmTeamUserInfo objects are reused correctly

## Key Testing Features

### Mock Objects
- Uses Mockito to create mock `RmTeamUserInfo` objects
- Helper method `createMockRmTeamUserInfo()` for consistent mock creation

### Comprehensive Assertions
- Tests return value is not null
- Verifies correct map size
- Checks presence of expected keys
- Validates list sizes and contents
- Uses `assertSame()` to verify object reuse

### Edge Case Coverage
- Null input handling
- Empty collections
- Missing data scenarios
- Data filtering behavior

## Method Under Test

<augment_code_snippet path="src/main/java/com/shoalter/mms_product_api/service/price_alert/ProductPriceMonitorCheckService.java" mode="EXCERPT">
````java
Map<Integer, List<RmTeamUserInfo>> groupRmInfoByMonitorId(List<ProductPriceMonitorProductRmMappingDo> rmMappingData, Map<String, RmTeamUserInfo> rmCodeToInfoMap) {
    // early return if no mapping data
    if (CollectionUtils.isEmpty(rmMappingData)) {
        log.warn("[price-monitor/check-price] No RM mapping data found for grouping");
        return Collections.emptyMap();
    }

    // 1. group rmMappingData by monitorProductId , value is List<String> rmCodes
    Map<Integer, List<String>> monitorIdRmCodeMap = rmMappingData.stream()
        .collect(Collectors.groupingBy(ProductPriceMonitorProductRmMappingDo::getProductPriceMonitorProductId,
            Collectors.mapping(ProductPriceMonitorProductRmMappingDo::getRmCode, Collectors.toList())));
    // 2. convert rmCodes to RmTeamUserInfo
    return monitorIdRmCodeMap.entrySet().stream()
        .collect(Collectors.toMap(
            Map.Entry::getKey,
            entry -> entry.getValue().stream()
                .map(rmCodeToInfoMap::get)
                .filter(Objects::nonNull)
                .collect(Collectors.toList())));
}
````
</augment_code_snippet>

## Test File Location

The tests have been added to:
`src/test/java/com/shoalter/mms_product_api/service/price_alert/ProductPriceMonitorCheckServiceTest.java`

## Running the Tests

To run these specific tests, use:
```bash
mvn test -Dtest=ProductPriceMonitorCheckServiceTest#groupRmInfoByMonitorId*
```

Or run all tests in the class:
```bash
mvn test -Dtest=ProductPriceMonitorCheckServiceTest
```

## Benefits

1. **Complete Coverage**: All logical branches and edge cases are tested
2. **Maintainable**: Clear test names and good structure
3. **Reliable**: Uses proper mocking and assertions
4. **Documentation**: Tests serve as living documentation of expected behavior
5. **Regression Prevention**: Will catch any future changes that break the method
