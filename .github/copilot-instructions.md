---
description: AI rules derived by <PERSON><PERSON><PERSON><PERSON> from the project AI interaction history
globs: *
---

## HEADERS

This file defines all project rules, coding standards, workflow guidelines, references, documentation structures, and best practices for the AI coding assistant. It is a living document that evolves with the project.

## TECH STACK

*   Java
*   Spring Boot
*   Junit 5
*   Mockito
*   Lombok
*   Gson
*   CompletableFuture
*   Threads
*   PowerMockito (consider alternatives due to static mocking issues)

## PROJECT DOCUMENTATION & CONTEXT SYSTEM

*   All architectural decisions, API documentation, and important context should be documented in markdown files in the project root.
*   When explaining code, reference the relevant documentation files.
*   Document file names should clearly indicate their purpose (e.g., `batch_edit_record_process.md`, `single_edit_product_service_process.md`, `checkEditProductHandler_analysis.md`, `complete_edit_product_flow.md`).
*   Documentation should include:
    *   Clear explanations of processes and logic.
    *   Diagrams illustrating complex flows.
    *   Code snippets demonstrating usage.
    *   Explanations of hidden logic and potential pitfalls.
    *   Troubleshooting tips and debugging strategies.
*   When generating documentation:
    *   Use clear and concise language.
    *   Provide step-by-step instructions.
    *   Include diagrams for complex processes.
    *   Offer practical guidance for developers.

## CODING STANDARDS

*   Follow established Java coding conventions.
*   Use meaningful names for variables, methods, and classes.
*   Write clear and concise comments.
*   Adhere to the project's architectural principles.
*   Ensure code is well-tested with comprehensive unit tests.
*   Always use the builder pattern for creating DTO objects.
*   When setting up mocks in unit tests, ensure all dependencies of the class under test are mocked using `@Mock` and `@InjectMocks`.
*   Handle null values carefully to prevent NullPointerExceptions.
*   Use Optional for return values to enhance code robustness.
*   Log at the appropriate level (e.g., `warn` or `info` instead of `error` for expected business conditions).
*   When using `CompletableFuture`, handle exceptions and ensure proper resource cleanup.

## GIT WORKFLOW

*   Branch naming convention: `feature/MS-XXXX` or `bugfix/MS-XXXX`.
*   Commit messages should be concise and descriptive. A good format is: `MS-XXXX refactor: Improve product category handling with offline SKU support`. A detailed example:
```
MS-7356 refactor: Improve product category handling with offline SKU support

- Skip category validation checks for offline/soon-to-be-offline products
- Extract validation logic to new BuProductCategoryHelper class for better maintainability
- Enhance helper with more explicit handling of inactive categories
- Use Optional for return values to improve code robustness
- Eliminate code duplication between classes
- Add comprehensive unit tests for ProductCheckDto and BuProductCategoryHelper
```
*   Before merging, conduct thorough code reviews, paying attention to code quality, test coverage, and adherence to coding standards.

## DEBUGGING

*   When debugging NullPointerExceptions, examine the stack trace and identify the point of failure.
*   Analyze the data flow and identify potential sources of null values.
*   Use logging to track variable values and execution flow.
*   Use the debugger to step through the code and inspect variable values.
*   When facing deadlocks, analyze thread states and identify potential thread pool exhaustion.
*   Reproduce issues with unit tests to ensure they are fixed correctly.

## TESTING

*   Write unit tests to cover all critical functionality.
*   Use mocking frameworks like Mockito to isolate units of code.
*   Aim for high test coverage.
*   Ensure tests are reproducible and reliable.
*   When creating unit tests:
    *   Use `@Mock` for external dependencies.
    *   Use `@Spy` for the service under test if partial mocking is needed, and use `doReturn()` instead of `when()` for stubbing.
    *   Ensure helper methods within the test class correctly refer to their respective `SaveProductHelper` instances.
    *   Use meaningful test method names.
    *   Assert all relevant conditions.
    *   Test both positive and negative scenarios.
*   When testing methods with many dependencies, mock all external dependencies.
*   When using a real instance of Gson, remove any mock call to gson.toJson().

## WORKFLOW & RELEASE RULES

*   All code changes must be reviewed and approved before merging.
*   Ensure all tests pass before releasing.
*   Follow the established release process.

## DATABASE

*   When querying data with a set of codes as parameters, use `@Param` annotation to bind parameters.
*   If the query returns a custom set of columns instead of a complete entity, create a projection interface to receive the results for better type safety and readability.

## ERROR HANDLING

*   Implement robust error handling to prevent application crashes.
*   Log errors and exceptions with sufficient detail for debugging.
*   Provide informative error messages to the user.

## BATCH EDITING

### General Flow:

1.  **Data validation:** Verify request data and quantity limits.
2.  **Store validation:** Check for the existence of all store codes.
3.  **Duplication check:** Prevent duplicate processing of the same product.
4.  **Data classification:** Distinguish between valid and invalid product data.
5.  **Product information retrieval:** Obtain product details and group by merchant.
6.  **Permission validation:** Ensure the user has the necessary permissions.
7.  **Record creation:** Handle both successful and failed records separately.

### Key Features:

*   **Generic design:** Supports multiple batch editing types.
*   **Merchant isolation:** Processes data grouped by merchant, supporting permission control.
*   **Error handling:** Comprehensive validation and error collection mechanisms.
*   **Special logic:** Provides special support for translation types.
*   **Asynchronous processing:** Creates PROCESSING status records for subsequent processing.

### Single Edit Product Service:

#### Architecture:

*   **Two-Stage Processing:**
    *   `processing()`: Handles business logic, data validation, and record creation.
    *   `callProductMasterAndUpdateRecordRow()`: Integrates with the ProductMaster system.

#### Core Steps:

1.  **Permission Check:** Ensure the user has the necessary permissions.
2.  **Variant Product Validation:** Check for the existence of Matrix product SKUs.
3.  **Photo Format Standardization:** Process product photo URL formats.
4.  **Record Creation:** Create edit records and record rows.
5.  **Data Check and Generation:** The most complex core logic.
6.  **Variant Product Synchronization:** Handle product variant relationships.

#### Data Check and Generation Details:

*   **Product Existence Validation:** Query original product data by UUID.
*   **Data Preprocessing:** HKTV product preprocessing and IIDS data generation.
*   **Promotion Activity Check:** Check for membership price activities.
*   **Business Rule Check:** Execute different check logic based on the edit type.
*   **Approval Process Handling:** Handle changes requiring approval (e.g., commission rates).

#### Design Features:

*   **Multiple Edit Type Support:**
    *   General product edits
    *   Promotion contract edits (special logic)
    *   Variant product edits
*   **Complete Error Handling:**
    *   Early validation and fail-fast approach
    *   Phased checks and detailed error messages
    *   Transaction rollback to ensure data consistency
*   **Asynchronous Processing:**
    *   ProductMaster calls use asynchronous mode
    *   Processing results are tracked via record status
*   **Business Logic Integration:**
    *   Price monitoring is triggered automatically
    *   Approval processes are automatically checked
    *   Exchange rate calculations are supported
    *   Promotion activities are checked
*   **State Management:**
    *   Complete state transition: `WAIT_START` → `PROCESSING` → `REQUESTING_PM` → `SUCCESS/FAIL`
    *   Each state has corresponding processing logic

### `callProductMasterAndUpdateRecordRow` Flow:

#### 1. ProductMaster Message Queue Processing
*   `ProductMasterSingleProductHandler`'s MQ listening mechanism.
*   Manual ACK to ensure message reliability.
*   Categorized processing by action type (CREATE/UPDATE/OFFLINE).

#### 2. Database Update and Hybris Synchronization
*   `SaveProductHelper`'s core processing logic.
*   Parallel processing of variant SKU combinations.
*   Synchronizing data to the Hybris e-commerce system.

#### 3. Complete Process Architecture
Consists of six main stages:
1. **Record Creation** - Handled by `SingleEditProductService`.
2. **Synchronization to ProductMaster** - `CheckRequestPMRecordProductTask`.
3. **ProductMaster Processing and MQ** - Central system processing.
4. **MQ Processing** - `ProductMasterSingleProductHandler`.
5. **Database Update and Hybris Synchronization** - `SaveProductHelper`.
6. **Response to ProductMaster** - Result feedback.

#### 4. Key Technical Features
*   **Asynchronous Parallel Processing** - Using `CompletableFuture` and thread pools.
*   **Group Batch Processing** - Processing by variant SKU groups.
*   **Transaction Management** - Ensuring data consistency.
*   **Error Handling** - Multi-level error handling and recovery mechanisms.
*   **Performance Optimization** - Caching strategies and batch operations.

#### 5. State Transition Management
Complete state machine from `WAIT_START` → `PROCESSING` → `REQUESTING_PM` → `CHECKING_PM` → `SUCCESS/FAIL`.

### `CheckProductHelper.checkEditProductHandler` Method

#### Overview

This method handles product check flows, mainly responsible for batch checking the status of product record rows and performing corresponding processing.

#### Main Features

*   Clear business logic: Explains why each validation step is needed.
*   Easy issue location: Provides a systematic approach to issue tracking.
*   Developer-friendly: Includes specific code examples and development patterns.
*   Maintenance-oriented: Considers the needs for long-term maintenance and expansion.

#### Document Highlights

*   **Complete Flow Analysis**
    *   Detailed description of execution steps (from data parsing to result return).
    *   Flowchart showing the execution logic of the method.
    *   Purpose and logic of each validation stage.
*   **In-Depth Technical Details**
    *   The role and meaning of method parameters.
    *   Classification of various validation logic (by editing type).
    *   Specific problems corresponding to error codes.
    *   Importance of data consistency checks.
*   **Practical Engineer Guide**
    *   Development steps for adding new validation logic.
    *   Systematic methods for problem tracking.
    *   Common problems and solutions.
    *   Performance optimization recommendations.
*   **Monitoring and Maintenance**
    *   Key log point suggestions.
    *   Setting monitoring indicators.
    *   Debugging tips.

#### Key Steps

1.  **Initialization and User Data Preparation**
```java
CheckTimeSchedule.addProductRecordId(record.getId());
UserDto userDto = userHelper.generateUserDtoByRecord(record);
```
    *   Add record ID to the time check schedule.
    *   Generate user data transfer object based on the record.

2.  **Collecting Product UUIDs**
```java
List<String> skuUuid = waitCheckRowList.stream()
    .map(SaveProductRecordRowDo::getUuid)
    .filter(Objects::nonNull)
    .collect(Collectors.toList());
```
    *   Extract all non-null product UUIDs from the record rows to be checked.

3.  **Establishing Product Data Mapping**
```java
// Get product data from the database
Map<String, ProductDo> productFromDbMap = ...
// Get product data from Product Master
Map<String, ProductMasterResultDto> productFromPmMap = ...
```
    *   Get product data from the local database and Product Master system.
    *   Establish a mapping relationship from UUID to product objects.

4.  **Conditional Data Preparation**
Prepare different auxiliary data according to different upload types:

    *   **Approval-Related Data**
```java
if (SaveProductType.CHECKING_APPROVAL_TYPE_SET.contains(record.getUploadType())) {
    waitingApprovalTempRowIdMap = approvalDealHelper.findWaitingCreateApprovalTempByRecordRows(...);
}
```

    *   **Membership Pricing Activity Data**
```java
if (SaveProductType.HKTV_PROMOTION_MEMBERSHIP_PRICING_TYPE_SET.contains(record.getUploadType())) {
    membershipPricingEventMap = requestMembershipPricingEventSet(...);
}
```

    *   **Small Mall Variant Check Data**
```java
if (SaveProductType.CHECK_LITTLE_MALL_VARIANT_SET.contains(record.getUploadType())) {
    ckeckLittleMallVariantMap = checkLittleMallProductHelper.generateCheckVariantProductMap(...);
}
```

5.  **Exchange Rate Data Acquisition**
```java
BigDecimal rmbRate = exchangeRateHelper.getExchangeRateByCurrency(CurrencyEnum.RMB);
```
    *   Get the RMB exchange rate.

6.  **Creating Check Data Object**
```java
CheckRecordRowProductData checkRowData = CheckRecordRowProductData.builder()
    .userDto(userDto)
    .productFromPmMap(productFromPmMap)
    // ... other data
    .build();
```
    *   Encapsulate all prepared data into a check data object.

7.  **Parallel Processing of Record Rows**
```java
CompletableFuture.allOf(waitCheckRowList.stream()
    .map(row -> isQueue ? 
        checkProductRecordRowTask.startWithCheckQueueProtocolRecordRowProductTaskExecutor(...) :
        checkProductRecordRowTask.startWithCheckRecordRowProductTaskExecutor(...))
    .toArray(CompletableFuture[]::new))
.join();
```
    *   Select different executors based on the `isQueue` parameter.
    *   Process all record rows to be checked in parallel.
    *   Wait for all tasks to complete.

8.  **Cleaning and Updating**
```java
record.setCheckTime(null);
saveProductRecordRepository.save(record);
```
    *   Clear the check time.
    *   Save the record update.

9.  **Exception Handling and Resource Cleanup**
```java
} catch (Exception e) {
    taskExceptionHelper.start(record, waitCheckRowList, e);
} finally {
    CheckTimeSchedule.removeProductRecordId(record.getId());
}
```
    *   Start the exception handling process in case of exceptions.
    *   Remove the record ID from the schedule regardless of success or failure.

#### Key Features
*   **Conditional Data Loading**: Determines which auxiliary data needs to be loaded based on the upload type.
*   **Parallel Processing**: Uses `CompletableFuture` for parallel checking of multiple product record rows.
*   **Resource Management**: Ensures correct cleanup of scheduled resources.
*   **Exception Handling**: Provides a complete exception handling mechanism.

#### Default Check Content in `checkEditProduct`

The default checks in `checkEditProduct` encompass a wide range of validations to ensure data integrity and business rule compliance. They are divided into two main layers: basic product validations and specific checks.

##### **First Layer: Basic Product Validations (checkAllProduct)**

This layer performs over 30 basic checks, categorized into 10 main areas:

1.  **Product Identification Checks**:
    *   ID validation
    *   SKU validation
2.  **Product Basic Information Checks**:
    *   Name validation
    *   Brand validation
    *   Preparation method validation, etc.
3.  **Warehouse and Logistics Checks**:
    *   Warehouse validation
    *   Delivery validation
    *   Weight limit validation, etc.
4.  **Category and Product Attribute Checks**:
    *   Category validation
    *   Size validation
    *   Color validation, etc.
5.  **Price and Financial Checks**:
    *   Price validation
    *   Cost validation
    *   Shelf life validation, etc.
6.  **Multimedia Content Checks**:
    *   Video validation
    *   Image validation
    *   Description validation, etc.
7.  **Option and Variant Checks**:
    *   Option field configuration
8.  **Service and Contract Checks**:
    *   Service removal
    *   Contract type validation, etc.
9.  **Merchant and Permission Checks**:
    *   Brand validation
    *   Permission validation
    *   Virtual store validation, etc.
10. **Storage and Preparation Checks**:
    *   Storage type validation
    *   Preparation days validation, etc.

##### **Second Layer: Specific Checks (14 items)**

This layer includes 14 specific checks performed in the default mode:

1.  **Packaging and Barcode Checks**:
    *   Completeness
    *   Format validation
    *   Uniqueness
2.  **Promotional Period Price Update Check**:
    *   Promotional logic restrictions
3.  **Main SKU Check**:
    *   Main SKU flag logic
4.  **Packaging Confirmation Check**:
    *   Confirmation status and modification restrictions
5.  **Barcode Lock Check**:
    *   Lock status and permissions
6.  **Contract Product Terms Check**:
    *   Insurance contract validation
7.  **SKU Name Emoji Check**:
    *   Special character restrictions
8.  **Mall Amount Edit Check**:
    *   Mall Dollar logic
9.  **EW SKU Edit Check**:
    *   Special SKU type restrictions
10. **Everuts Field Update Check**:
    *   Third-party system integration
11. **Immutable Field Check**:
    *   System protection mechanism
12. **Membership Pricing Event Check**:
    *   Membership price logic
13. **Plus Price Check**:
    *   Plus membership pricing
14. **Product Preparation Method Association Check**:
    *   Business logic consistency

##### **Key Design Concepts**

*   **Importance of Check Execution Order**: Basic checks first, dependency considerations, performance optimization.
*   **Accumulative Error Strategy**: Collect all errors and return them at once.
*   **Monitoring and Optimization Suggestions**: Performance monitoring, error statistics, caching strategies.

### `updateSaveProductRecordRowContent` Method

This method is used to update the content of a saved product record row, merging original product data with edited content based on the batch edit type.

#### Method Signature

```java
public void updateSaveProductRecordRowContent(
    SaveProductRecordDo productRecord, 
    SaveProductRecordRowDo row, 
    ProductMasterResultDto beforeProduct
)
```

#### Parameters

*   `productRecord`: Product record master file.
*   `row`: Product record row data.
*   `beforeProduct`: Original product data retrieved from Product Master.

#### Processing Flow

1.  **Initialize Data**

```java
SingleEditProductDto singleEditProductDto = gson.fromJson(row.getContent(), SingleEditProductDto.class);
ProductMasterDto productMasterDtoFromProductMaster = gson.fromJson(gson.toJson(beforeProduct, ProductMasterResultDto.class), ProductMasterDto.class);
productMasterDtoFromProductMaster.setRecordRowId(row.getId());
```

    *   Parse edit data from the record row content.
    *   Convert original product data to `ProductMasterDto`.
    *   Set the record row ID.

2.  **Handle Different Fields Based on Upload Type**

    *   **Packaging Dimension Edit** (`BATCH_EDIT_PRODUCT_PACKAGING_DIMENSION`)

Update packaging-related fields:

        *   Packaging specifications (multilingual)
        *   Weight and weight unit
        *   Packaging dimensions (length, width, height)
        *   Packaging box type
        *   Carton dimensions

    *   **Price Edit** (`BATCH_EDIT_PRODUCT_PRICE`)

Update price-related fields:

        *   Original price
        *   Selling price
        *   Discount style
        *   Discount text (multilingual)

    *   **Online Status Edit** (`BATCH_EDIT_PRODUCT_ONLINE_STATUS`)

Update product online status.

    *   **Visibility Edit** (`BATCH_EDIT_PRODUCT_VISIBILITY`)

Update product visibility settings.

    *   **Overseas Reserve Region Edit** (`BATCH_EDIT_PRODUCT_OVERSEA_RESERVE_REGION`)

```java
List<String> reserveRegion = Optional.ofNullable(batchEditOverseaReserveRegionDto.getReserveRegion())
    .orElse(Collections.emptyList())
    .stream()
    .filter(Optional.ofNullable(productMasterDtoFromProductMaster.getAdditional().getHktv().getDeliveryDistrict()).orElse(Collections.emptyList())::contains)
    .collect(Collectors.toList());
```

Filter and update delivery regions.

    *   **Excel Batch Edit** (`BATCH_EDIT_PRODUCT_FROM_EXCEL`)

Retain specific fields from the original data:

        *   Mall coins, VIP mall coins
        *   Contract number
        *   Video and image data (if new data is empty)

    *   **Little Mall Product Edit** (`BATCH_EDIT_LITTLE_MALL_PRODUCT`)

```java
boolean isPrimarySku = singleEditProductDto.getProduct().getAdditional().getLittleMall().getIsPrimarySku();
if (Boolean.TRUE.equals(isPrimarySku)) {
    // Update public fields
}
// Update private fields
```

Update scope is determined by whether it is the main SKU.

    *   **Translation Edit** (`BATCH_EDIT_HKTV_PRODUCT_TRANSLATE`)

Update Simplified Chinese-related fields:

        *   Product name
        *   Description, specifications, notes, and other Simplified Chinese fields

    *   **Preparation Days Edit** (`BATCH_EDIT_PRODUCT_READY_DAYS`)

Update product preparation days.

    *   **Force Offline Edit** (`BATCH_EDIT_PRODUCT_FORCE_OFFLINE`)

```java
if (productForceOfflineDto.isForceOffline()) {
    productMasterDtoFromProductMaster.getAdditional().getHktv().setOnlineStatus(OnlineStatusEnum.OFFLINE);
}
```

Set force offline status; if force offline, also set to offline status.

3.  **Final Processing**

```java
if (SaveProductType.BATCH_EDIT_PRODUCT_FROM_EXCEL != productRecord.getUploadType()) {
    singleEditProductDto.setProduct(productMasterDtoFromProductMaster);
}
row.setContent(gson.toJson(singleEditProductDto));
```

    *   For non-Excel batch edits, set the merged product data back to the edit object.
    *   Serialize and update the record row content with the final result.

#### Key Features

*   **Type-Specific Processing**: Different field update strategies are adopted for different edit types.
*   **Data Protection**: Important fields in the original data are retained to prevent accidental overwrites.
*   **Conditional Updates**: Decisions on whether to update specific fields are based on business logic.
*   **Multilingual Support**: Handles multiple languages, including Chinese, English, and Simplified Chinese.

## THREADING

*   Avoid using the same thread pool for both executing tasks and nested sub-tasks to prevent deadlocks.
*   When using thread pools, carefully configure the number of threads to avoid exhaustion.
*   Monitor thread pool usage to identify potential bottlenecks.

## SQL

*   When querying data with a set of codes as parameters, use `@Param` annotation to bind parameters.
*   If the query returns a custom set of columns instead of a complete entity, create a projection interface to receive the results for better type safety and readability.

### Projection Objects
#### Example Usage:
1. Create a projection interface for the returned values
```java
package com.shoalter.mms_product_api.dao.repository.rmteam.projection;

public interface RmTeamInfoProjection {
    String getRmCode();
    String getRmName();
    String getRmlName();
}
```

2. Update `RmTeamRepository` to use the projection
```java
package com.shoalter.mms_product_api.dao.repository;

import com.shoalter.mms_product_api.dao.repository.rmteam.projection.RmTeamInfoProjection;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import java.util.List;
import java.util.Set;

public interface RmTeamRepository extends JpaRepository<RmTeam, Long> {

    @Query(value = "SELECT " +
            "   RT.RM_CODE AS rmCode, " +
            "   (SELECT USER_NAME FROM SYS_USER WHERE ID = RT.USER_ID) AS rmName, " +
            "   (SELECT USER_NAME FROM SYS_USER WHERE ID = RT.TEAM_LEADER_ID) AS rmlName " +
            "FROM RM_TEAM RT " +
            "WHERE RT.RM_CODE IN (:rmCodes) " +
            "GROUP BY RT.RM_CODE", nativeQuery = true)
    List<RmTeamInfoProjection> findRmTeamInfoByRmCodes(@Param("rmCodes") Set<String> rmCodes);
}
```

3. Use the projection in your code
```java
Set<String> rmCodes = Set.of("HHK05", "RM232");
List<RmTeamInfoProjection> results = rmTeamRepository.findRmTeamInfoByRmCodes(rmCodes);

for (RmTeamInfoProjection info : results) {
    String rmCode = info.getRmCode();
    String rmName = info.getRmName();
    String rmlName = info.getRmlName();
    // Process data...
}
```