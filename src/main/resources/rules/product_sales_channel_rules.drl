import com.shoalter.mms_product_api.service.product.pojo.SaveHybrisProductDto;
import com.shoalter.mms_product_api.config.product.SalesChannelEnum;
import com.shoalter.mms_product_api.config.product.DeliveryRegionEnum;
import com.shoalter.mms_product_api.config.product.SysParmSegmentEnum;

global com.shoalter.mms_product_api.service.product.pojo.hybris.HybrisSalesChannelRuleDto salesChannelRule;
global java.util.List results;
global java.lang.Boolean wechatExclusive;
global java.lang.Boolean ctmExclusive;


//CTM Sales Channel Rule : "MO" delivery region & Category Exclusion
rule "CTM Sales Channel"
    when
        $region : SaveHybrisProductDto(deliverableRegionCodes != null && deliverableRegionCodes.contains(DeliveryRegionEnum.MO.name())) and
        $category : SaveHybrisProductDto(ctmExclusive == false)
    then
        results.add(SalesChannelEnum.CTM.name());
end


//MFood Sales Channel Rule : "MO" delivery region
rule "MFood Sales Channel"
    when
        $region : SaveHybrisProductDto(deliverableRegionCodes != null && deliverableRegionCodes.contains(DeliveryRegionEnum.MO.name()))
    then
        results.add(SalesChannelEnum.MFOOD.name());
end


//WeChat Rule : Product Ready Method Exclusion & Category Exclusion
rule "WeChat Sales Channel"
    when
        $productReadyMethod : SaveHybrisProductDto(productReadyMethod != null && !salesChannelRule.wechatExcludedProductReadyMethods.contains(productReadyMethod)) and
        $category : SaveHybrisProductDto(wechatExclusive == false)
    then
        results.add(SalesChannelEnum.WECHAT.name());
end
