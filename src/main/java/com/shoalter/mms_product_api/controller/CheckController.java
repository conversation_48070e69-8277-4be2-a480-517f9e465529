package com.shoalter.mms_product_api.controller;

import com.google.gson.Gson;
import com.shoalter.mms_product_api.dao.repository.business.pojo.BuProductCategoryDo;
import com.shoalter.mms_product_api.dao.repository.contract.pojo.ContractProdTermsDo;
import com.shoalter.mms_product_api.exception.SystemException;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.product.*;
import com.shoalter.mms_product_api.service.product.pojo.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Tag(name = "Check API")
@RequiredArgsConstructor
@RestController
@PreAuthorize("hasRole('ROLE_MMS_ROLE')")
@Slf4j
public class CheckController {

	private final Gson gson;

	private final CheckSkuIdService checkSkuIdService;

	@Operation(summary = "檢查skuId是否已存在該hktv store", description = "status 1代表檢查成功sku並不存在store -1代表失敗")
	@PostMapping("/checkSkuId")
	public ResponseDto<Void> checkSkuId(
		@AuthenticationPrincipal UserDto userDto,
		@RequestBody CheckSkuIdRequestDto checkSkuIdRequestDto) throws SystemException {
		return checkSkuIdService.start(userDto, checkSkuIdRequestDto);
	}

	private final CheckSkuIdByStoreService checkSkuIdByStoreService;

	@Operation(summary = "依據Store檢查skuId是否已存在該store", description = "status 1代表檢查成功sku並不存在merchant -1代表失敗")
	@PostMapping("/little-mall/checkSkuId")
	public ResponseDto<Void> checkSkuIdByStore(
		@AuthenticationPrincipal UserDto userDto,
		@RequestBody CheckSkuIdByLittleMallStoreRequestDto checkSkuIdByLittleMallStoreRequestDto) throws SystemException {
		return checkSkuIdByStoreService.start(userDto, checkSkuIdByLittleMallStoreRequestDto);
	}

	private final CheckContractService checkContractService;

	@Operation(summary = "檢查合約", description = "status 1代表成功-1代表失敗，檢查完合約會回傳Contract Prod Term")
	@PostMapping("/checkContract")
	public ResponseDto<ContractProdTermsDo> checkContract(
		@AuthenticationPrincipal UserDto userDto,
		@RequestBody CheckContractInputDto checkContractInputDto) {
		return checkContractService.start(userDto, checkContractInputDto);
	}

	private final CheckEnableBatchUploadService checkEnableBatchUploadService;

	@Operation(summary = "檢查商戶是否可以上傳批次檔", description = "status 1代表成功可以上傳檔案、0代表有檔案在處理中不能上傳、-1代表失敗")
	@GetMapping("/checkEnableBatchUpload")
	public ResponseDto<Void> checkEnableBatchUpload(@RequestParam Integer merchantId) {
		return checkEnableBatchUploadService.start(merchantId);
	}

	private final IsBuySellMerchantService isBuySellMerchantService;

	@Operation(summary = "是否是Buy Sell商戶", description = "status 1代表成功-1代表失敗")
	@GetMapping("/isBuySellMerchant")
	public ResponseDto<Boolean> isBuySellMerchant(
		@AuthenticationPrincipal UserDto userDto,
		@RequestParam Integer merchantId
	) {
		return isBuySellMerchantService.start(userDto, merchantId);
	}


	private final CheckCategoryEligiblePrimaryService checkCategoryEligiblePrimaryService;

	@Operation(summary = "檢查是否能為PrimaryCategory並回傳可用選項", description = "status 1代表成功 -1代表失敗")
	@GetMapping("/checkCategoryEligiblePrimary")
	public ResponseDto<List<BuProductCategoryDo>> checkCategoryEligiblePrimary(
		@RequestParam String buCode,
		@RequestParam List<String> categoryCode) {
		return checkCategoryEligiblePrimaryService.start(buCode, categoryCode);
	}

	private final IsNeedRemovalServices isNeedRemovalServices;

	@Operation(summary = "檢查商品類別是否能使用除舊服務", description = "status 1代表成功-1代表失敗")
	@GetMapping("/isNeedRemovalServices")
	public ResponseDto<Boolean> isNeedRemovalServices(
		@RequestParam String buCode,
		@RequestParam List<String> productTypeCodeList
	) {
		return isNeedRemovalServices.start(buCode, productTypeCodeList);
	}

	private final IsGoodTypeServices isGoodTypeServices;

	@Operation(summary = "檢查商品類別是否能填寫商品類型", description = "status 1代表成功-1代表失敗")
	@GetMapping("/isGoodType")
	public ResponseDto<Boolean> isGoodType(
		@RequestParam String buCode,
		@RequestParam List<String> productTypeCodeList
	) {
		return isGoodTypeServices.start(buCode, productTypeCodeList);
	}
	private final IsVirtualStoreService isVirtualStoreService;

	@Operation(summary = "是否為Virtual Store", description = "status 1代表成功 -1代表失敗，true代表是，false代表不是")
	@GetMapping("/isVirtualStore")
	public ResponseDto<Boolean> isVirtualStore(
		@AuthenticationPrincipal UserDto userDto,
		@RequestParam Integer merchantId
	) {
		return isVirtualStoreService.start(userDto, merchantId);
	}

	private final IsWhiteListMerchantService isWhiteListMerchantService;

	@Operation(summary = "is merchant in white list", description = "status 1代表成功 -1代表失敗，true代表是，false代表不是")
	@GetMapping("/isWhiteListMerchant")
	public ResponseDto<Boolean> isWhiteListMerchant(@RequestParam Integer merchantId) {
		return isWhiteListMerchantService.start(merchantId);
	}

	private final FindProductPromotionExistsService findProductPromotionExistsService;

	@Operation(summary = "check product has promotion", description = "status 1代表成功 -1代表失敗")
	@GetMapping(path = "/products/{uuid}/exists-promotion")
	public ResponseDto<Boolean> findProductPromotionExists(
		@AuthenticationPrincipal UserDto user, @PathVariable String uuid) {
		return findProductPromotionExistsService.start(uuid);
	}

	private final CheckProductMissingFieldService checkProductMissingFieldService;

	@Operation(summary = "check products have any missing field", description = "status 1代表成功-1代表失敗")
	@PostMapping("/checkProductMissingFields")
	public ResponseDto<List<ProductMissingFieldCheckErrorResponseDto>> checkProductMissingFields(
		@RequestBody ProductMissingFieldCheckRequestDto requestDto) {
		return checkProductMissingFieldService.start(requestDto);
	}

	private final LittleMallCheckProductMissingFieldService littleMallCheckProductMissingFieldService;

	@Operation(summary = "check little-mall products have any missing field", description = "status 1代表成功-1代表失敗")
	@PostMapping("/little-mall/checkProductMissingFields")
	public ResponseDto<List<ProductMissingFieldCheckErrorResponseDto>> batchUpload(
		@RequestBody List<LittleMallProductFieldDto> littleMallProductFieldDtoList) {
		return littleMallCheckProductMissingFieldService.start(littleMallProductFieldDtoList);
	}
}



