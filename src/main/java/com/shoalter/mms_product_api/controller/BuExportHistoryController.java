package com.shoalter.mms_product_api.controller;

import com.google.gson.Gson;
import com.shoalter.mms_product_api.config.product.BuExportHistoryEnum;
import com.shoalter.mms_product_api.service.base.pojo.PageDto;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.product.BuExportHistoryOverviewRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.ViewBuHistoryDto;
import com.shoalter.mms_product_api.service.bu_export_history.DownloadBuDataInExcelService;
import com.shoalter.mms_product_api.service.bu_export_history.ViewBuExportHistoryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Tag(name = "Bu")
@RequiredArgsConstructor
@RestController
@PreAuthorize("hasRole('ROLE_MMS_ROLE')")
@Slf4j
@RequestMapping("bu")
public class BuExportHistoryController {

	private final Gson gson;

	private final ViewBuExportHistoryService viewBuExportHistoryService;

	@Operation(summary = "query export history", description = "status 1代表成功-1代表失敗")
	@GetMapping("/export/history")
	public ResponseDto<PageDto<ViewBuHistoryDto>> viewThirdPartyExportHistory(
		@AuthenticationPrincipal UserDto userDto,
		@RequestParam(required = false, defaultValue = "1") Integer pageNumber,
		@RequestParam(required = false, defaultValue = "10") Integer pageSize,
		@RequestParam(required = false, defaultValue = "createTime:desc") String orderBy,
		@RequestParam(required = false) List<BuExportHistoryEnum> buCodes,
		@RequestParam(required = false) String startDate,
		@RequestParam(required = false) String endDate) {

		BuExportHistoryOverviewRequestDto buExportHistoryOverviewRequestDto =
				BuExportHistoryOverviewRequestDto.builder()
						.page(pageNumber)
						.size(pageSize)
						.orderBy(orderBy)
						.buCodes(buCodes == null ? List.of() : buCodes)
						.startDate(startDate)
						.endDate(endDate)
						.build();

		return viewBuExportHistoryService.start(userDto, buExportHistoryOverviewRequestDto);
	}

	private final DownloadBuDataInExcelService exportBuProductDataService;

	@Operation(summary = "export data in excel or zip", description = "status 1代表成功-1代表失敗")
	@GetMapping("/export/{historyId}/download")
	public HttpEntity<ByteArrayResource> downloadThirdPartyDataInExcel(
		@AuthenticationPrincipal UserDto userDto,
		@RequestParam(required = false) Integer storeId,
		@PathVariable Integer historyId) {
		return exportBuProductDataService.start(userDto, historyId, storeId);
	}
}



