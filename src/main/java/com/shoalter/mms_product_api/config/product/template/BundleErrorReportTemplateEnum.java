package com.shoalter.mms_product_api.config.product.template;

import lombok.Getter;

@Getter
public enum BundleErrorReportTemplateEnum {

	PARENT_PRODUCT_CODE(0,"Parent Product ID"),
    PARENT_SKU_CODE(1,"Parent SKU ID"),
	ERROR_REASON(2,"Error Reason"),
	ACTIVE(3,"Active"),
	ONLINE_STATUS(4,"Online Status"),
	VISIBILITY(5,"Visibility"),
	STORE_CODE(6,"Store ID"),
	STORE_NAME(7,"Store Name"),
	PRIORITY(8,"Priority"),
	PRODUCT_TYPE_CODE(9,"Product Type Code"),
	PRIMARY_CATEGORY_CODE(10,"Primary Category Code"),
	BRAND(11,"Brand Name"),
	PRODUCT_READY_METHOD(12,"Product Ready Method"),
	WAREHOUSE(13,"Warehouse"),
	IS_PRIMARY_SKU(14, "Is Primary SKU"),
	PARENT_SKU_NAME_EN(15, "Parent SKU Name (EN)"),
	PARENT_SKU_NAME_CH(16, "Parent SKU Name (CH)"),
	PARENT_SKU_NAME_SC(17, "Parent SKU Name (SC)"),
	SKU_SHORT_DESCRIPTION_TITLE_EN(18,"SKU Short Description Title (EN)"),
	SKU_SHORT_DESCRIPTION_TITLE_CH(19,"SKU Short Description Title (CH)"),
	MAIN_PHOTO(20,"Main photo"),
	MANUFACTURED_COUNTRY(21,"Manufactured Country"),
	CURRENCY(22,"Currency"),
	ORIGINAL_PRICE(23,"Original Price"),
	SELLING_PRICE(24,"Selling Price"),
	DISCOUNT_TEXT_STYLE(25,"Discount Text Style"),
	DISCOUNT_TEXT_EN(26,"Discount Text (EN)"),
	DISCOUNT_TEXT_CH(27,"Discount Text (CH)"),
	DISCOUNT_TEXT_SC(28,"Discount Text (SC)"),
	RETURN_DAYS(29,"Return Days"),
	PRODUCT_READY_DAYS(30,"Product Ready Days"),
	PICKUP_DAYS(31,"Pickup Days"),
	PICKUP_TIMESLOT(32,"Pickup Timeslot");
    private final Integer columnNumber;
    private final String colName;

    BundleErrorReportTemplateEnum(Integer colNum, String colName){
        this.columnNumber = colNum;
        this.colName = colName;
    }
}
