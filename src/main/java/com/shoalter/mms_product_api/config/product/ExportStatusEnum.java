package com.shoalter.mms_product_api.config.product;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;
import java.util.stream.Stream;

public enum ExportStatusEnum {
	PROCESSING("processing"),
	SUCCESS("success"),
	FAIL("fail");

	private final String exportStatus;

	ExportStatusEnum(String exportStatus){
		this.exportStatus= exportStatus;
	}

	public String getExportStatus() {
		return exportStatus;
	}
}

@Converter(autoApply = true)
class ExportStatusEnumConverter implements AttributeConverter<ExportStatusEnum, String>
{
	@Override
	public String convertToDatabaseColumn(ExportStatusEnum attribute) {
		if (attribute == null) {
			return "";
		}
		return attribute.getExportStatus();
	}

	@Override
	public ExportStatusEnum convertToEntityAttribute(String dbData) {
		if (dbData == null || "".equals(dbData)) {
			return null;
		}
		return Stream.of(ExportStatusEnum.values()).filter(c -> c.getExportStatus().equals(dbData)).findFirst()
			.orElseThrow(IllegalArgumentException::new);
	}
}


