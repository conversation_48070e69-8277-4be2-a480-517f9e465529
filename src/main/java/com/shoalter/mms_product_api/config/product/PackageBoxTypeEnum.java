package com.shoalter.mms_product_api.config.product;

import com.shoalter.mms_product_api.config.type.StorageTemperatureType;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Getter
public enum PackageBoxTypeEnum {
	FROZEN_N(StorageTemperatureType.FROZEN, false, new ArrayList<>(Stream.of("E", "T").collect(Collectors.toList()))),
	CHILLED_Y(StorageTemperatureType.CHILLED, true, new ArrayList<>(Stream.of("M").collect(Collectors.toList()))),
	CHILLED_N(StorageTemperatureType.CHILLED, false, new ArrayList<>(Stream.of("J","W", "N", "K", "U").collect(Collectors.toList()))),
	AIR_CON_Y(StorageTemperatureType.AIRCON, true, new ArrayList<>(Stream.of("O", "V").collect(Collectors.toList()))),
	AIR_CON_N(StorageTemperatureType.AIRCON, false, new ArrayList<>(Stream.of("P", "S").collect(Collectors.toList()))),
	ROOM_TEMPERATURE_Y(StorageTemperatureType.ROOM_TEMPERATURE, true, new ArrayList<>(Stream.of("G", "F", "Y").collect(Collectors.toList()))),
	ROOM_TEMPERATURE_N(StorageTemperatureType.ROOM_TEMPERATURE, false, new ArrayList<>(Stream.of("R", "Q", "H", "L", "X").collect(Collectors.toList())));

	private final String storageTemperature;
	private final boolean fragileItem;
	private final List<String> packBoxTypeCode;

	PackageBoxTypeEnum(String storageTemperature, boolean fragileItem, List<String> packBoxTypeCode) {
		this.storageTemperature = storageTemperature;
		this.fragileItem = fragileItem;
		this.packBoxTypeCode = packBoxTypeCode;
	}
}
