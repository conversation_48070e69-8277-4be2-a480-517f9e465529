package com.shoalter.mms_product_api.config.product.edit_column;

import org.apache.commons.lang3.StringUtils;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;
import java.util.Set;
import java.util.stream.Stream;

public enum SaveProductRecordRowTempStatusEnum {
	PENDING, //wait for process
	SUCCESS, //hybris response success
	FAIL, //hybris response fail
	ROLLBACK_FAIL, //hybris response success but product-api fail
	SENT_PM, //informed product master product result
	;

	public static final Set<SaveProductRecordRowTempStatusEnum> TRANSITIONAL_STATUS_SET = Set.of(SUCCESS, FAIL, ROLLBACK_FAIL);

}

@Converter(autoApply = true)
class SaveProductRecordRowTempStatusEnumConverter implements AttributeConverter<SaveProductRecordRowTempStatusEnum, String>
{
	@Override
	public String convertToDatabaseColumn(SaveProductRecordRowTempStatusEnum attribute) {
		if (attribute == null) {
			return StringUtils.EMPTY;
		}
		return attribute.name();
	}

	@Override
	public SaveProductRecordRowTempStatusEnum convertToEntityAttribute(String dbData) {
		if (dbData == null || dbData.isEmpty()) {
			return null;
		}
		return Stream.of(SaveProductRecordRowTempStatusEnum.values()).filter(c -> c.name().equals(dbData)).findFirst()
			.orElseThrow(IllegalArgumentException::new);
	}
}
