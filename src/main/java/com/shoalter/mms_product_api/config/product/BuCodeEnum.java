package com.shoalter.mms_product_api.config.product;

import java.util.Optional;
import java.util.Set;

public enum BuCodeEnum {
	HKTV,
	LITTLE_MALL;

	public static final Set<String> ALL_BU_LIST = Set.of(HKTV.name(), LITTLE_MALL.name());

	public static Optional<BuCodeEnum> getBuCodeEnum(String buCode) {
		for (BuCodeEnum buCodeEnum : BuCodeEnum.values()) {
			if (buCodeEnum.name().equals(buCode)) {
				return Optional.of(buCodeEnum);
			}
		}
		return Optional.empty();
	}
}
