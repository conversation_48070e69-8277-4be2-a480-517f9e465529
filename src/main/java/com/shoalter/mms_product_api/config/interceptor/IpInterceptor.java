package com.shoalter.mms_product_api.config.interceptor;

import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@Slf4j
@RequiredArgsConstructor
@Component
public class IpInterceptor implements HandlerInterceptor {

	@Override
	public boolean preHandle(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response, @NonNull Object handler) {
		String clientIp = getClientIp(request);
		ClientIpHolder.setClientIp(clientIp);
		return true;
	}

	@Override
	public void afterCompletion(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response, @NonNull Object handler, Exception ex) {
		ClientIpHolder.clear();
	}

	private String getClientIp(HttpServletRequest request) {
		String[] headerIps = {"X-REAL-IP", "X-Original-Forwarded-For", "X-Forwarded-For"};
		for (String headerIp : headerIps) {
			String clientIp = request.getHeader(headerIp);
			if (isValidClientIp(clientIp)) {
				return clientIp;
			}
		}
		return request.getRemoteAddr();
	}

	private boolean isValidClientIp(String clientIp) {
		return clientIp != null && !clientIp.isEmpty() && !"unknown".equalsIgnoreCase(clientIp);
	}
}
