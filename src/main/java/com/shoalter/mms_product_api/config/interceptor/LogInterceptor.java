package com.shoalter.mms_product_api.config.interceptor;

import com.google.gson.Gson;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@Slf4j
@RequiredArgsConstructor
@Component
public class LogInterceptor implements HandlerInterceptor {

	private final Gson gson;

	@Override
	public boolean preHandle(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response, @NonNull Object handler) {
		Object userInfo = SecurityContextHolder.getContext().getAuthentication().getPrincipal();
		log.info("[{}]{}, userInfo: {}, clientIp: {}, parameter: {}", request.getMethod(), request.getRequestURI(), gson.toJson(userInfo), ClientIpHolder.getClientIp(), gson.toJson(request.getParameterMap()));
		return true;
	}

	@Override
	public void afterCompletion(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response, @NonNull Object handler, Exception ex) {
	}
}
