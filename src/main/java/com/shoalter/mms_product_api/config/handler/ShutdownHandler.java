package com.shoalter.mms_product_api.config.handler;

import com.shoalter.mms_product_api.service.partner_product_price.PartnerProductPriceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PreDestroy;

@Slf4j
@Component
@RequiredArgsConstructor
public class ShutdownHandler {

	private static final String PRE_DESTROY_SHUTDOWN_TAG = "[PreDestroy-onShutdown]";
	private final PartnerProductPriceService partnerProductPriceService;

	@PreDestroy
	public void onShutdown() {
		log.info(PRE_DESTROY_SHUTDOWN_TAG + "Shutting down... Started.");
		partnerProductPriceService.preDestroyUpdateProcessingCronJobStatusToFail();

		log.info(PRE_DESTROY_SHUTDOWN_TAG + "Shutting down... Finished.");
	}
}
