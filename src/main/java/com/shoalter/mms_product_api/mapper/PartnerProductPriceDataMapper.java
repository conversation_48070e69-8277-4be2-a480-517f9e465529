package com.shoalter.mms_product_api.mapper;

import com.shoalter.mms_product_api.dao.repository.product.pojo.PartnerProductPriceDo;
import com.shoalter.mms_product_api.service.partner_product_price.pojo.PartnerProductPriceData;
import com.shoalter.mms_product_api.service.partner_product_price.pojo.PartnerProductPriceUpdateData;
import org.mapstruct.Builder;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.MappingTarget;
import org.mapstruct.Named;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING,
	builder = @Builder)
public interface PartnerProductPriceDataMapper {


	@Mapping(target = "busUnitId", constant = "85")
	@Mapping(target = "chargeCurrency", constant = "RMB")
	@Mapping(target = "chargeConvertedCurrency", constant = "HKD")
	@Mapping(target = "chargeConverted", source = "productPriceData", qualifiedByName = "calculateChargeConverted")
	@Mapping(target = "serviceFeeCurrency", constant = "RMB")
	@Mapping(target = "serviceFee", source = "chargePrice", qualifiedByName = "calculateServiceFee")
	@Mapping(target = "serviceFeeConvertedCurrency", constant = "HKD")
	@Mapping(target = "serviceFeeConverted", source = "productPriceData", qualifiedByName = "calculateServiceFeeConverted")
	@Mapping(target = "lastUpdateDate",  expression = "java(java.time.LocalDateTime.now())")
	PartnerProductPriceDo toCreatePartnerProductPriceDo(PartnerProductPriceData productPriceData);

	@Mapping(target = "chargeConverted", source = "updatePriceData", qualifiedByName = "calculateChargeConverted")
	@Mapping(target = "serviceFee", source = "updatePriceData.chargePrice", qualifiedByName = "calculateServiceFee")
	@Mapping(target = "serviceFeeConverted", source = "updatePriceData", qualifiedByName = "calculateServiceFeeConverted")
	@Mapping(target = "lastUpdateDate", source = "now")
	PartnerProductPriceDo toUpdatePartnerProductPriceDo(PartnerProductPriceUpdateData updatePriceData, LocalDateTime now, @MappingTarget PartnerProductPriceDo partnerProductPriceDo);


	@Named("calculateChargeConverted")
	default BigDecimal calculateChargeConverted(PartnerProductPriceData data) {
		return calculate(data.getChargePrice(), data.getExchangeRate());
	}

	@Named("calculateServiceFeeConverted")
	default BigDecimal calculateServiceFeeConverted(PartnerProductPriceData data) {
		BigDecimal serviceFee = calculateServiceFee(data.getChargePrice());
		return calculate(serviceFee, data.getExchangeRate());
	}

	@Named("calculateChargeConverted")
	default BigDecimal calculateChargeConverted(PartnerProductPriceUpdateData data) {
		return calculate(data.getChargePrice(), data.getExchangeRate());
	}

	@Named("calculateServiceFeeConverted")
	default BigDecimal calculateServiceFeeConverted(PartnerProductPriceUpdateData data) {
		BigDecimal serviceFee = calculateServiceFee(data.getChargePrice());
		return calculate(serviceFee, data.getExchangeRate());
	}

	@Named("calculateServiceFee")
	default BigDecimal calculateServiceFee(BigDecimal chargePrice) {
		if (chargePrice == null) {
			return null;
		}
		if (chargePrice.compareTo(new BigDecimal("5")) <= 0) {
			return new BigDecimal("2");
		} else if (chargePrice.compareTo(new BigDecimal("20")) <= 0) {
			return new BigDecimal("4");
		} else if (chargePrice.compareTo(new BigDecimal("20")) > 0) {
			return new BigDecimal("8");
		}
		return null;
	}

	default BigDecimal calculate(BigDecimal amount, BigDecimal exchangeRate) {
		return (amount == null || exchangeRate == null) ? null :
			amount.multiply(exchangeRate).setScale(2, RoundingMode.HALF_UP);
	}

}
