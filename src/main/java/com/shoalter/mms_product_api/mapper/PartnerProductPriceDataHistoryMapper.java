package com.shoalter.mms_product_api.mapper;

import com.shoalter.mms_product_api.dao.repository.product.pojo.PartnerProductPriceDo;
import com.shoalter.mms_product_api.dao.repository.product.pojo.PartnerProductPriceHistoryDo;
import org.mapstruct.Builder;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;

import java.time.LocalDateTime;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING,
	builder = @Builder)
public interface PartnerProductPriceDataHistoryMapper {

	@Mapping(target = "startDate", source = "partnerProductPriceDo.lastUpdateDate")
	@Mapping(target = "createDate", source = "now")
	@Mapping(target = "endDate", source = "now")
	@Mapping(target = "id", ignore = true)
	PartnerProductPriceHistoryDo toCreatePartnerProductPriceHistoryDo(PartnerProductPriceDo partnerProductPriceDo, LocalDateTime now);
}
