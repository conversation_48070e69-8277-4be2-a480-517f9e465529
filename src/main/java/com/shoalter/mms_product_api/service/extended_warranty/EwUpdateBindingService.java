package com.shoalter.mms_product_api.service.extended_warranty;

import com.shoalter.mms_product_api.config.product.BuCodeEnum;
import com.shoalter.mms_product_api.config.product.SaveProductStatus;
import com.shoalter.mms_product_api.config.product.SaveProductStatusEnum;
import com.shoalter.mms_product_api.config.product.SaveProductTypeEnum;
import com.shoalter.mms_product_api.config.type.ConstantType;
import com.shoalter.mms_product_api.config.user.RoleCode;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordDo;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordRowDo;
import com.shoalter.mms_product_api.dao.repository.product.ProductRepository;
import com.shoalter.mms_product_api.dao.repository.product.pojo.ProductStoreInfoDo;
import com.shoalter.mms_product_api.exception.NoDataException;
import com.shoalter.mms_product_api.exception.SystemException;
import com.shoalter.mms_product_api.exception.SystemI18nException;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.extended_warranty.pojo.BatchUpdateEwProductBindingDto;
import com.shoalter.mms_product_api.service.product.helper.BatchCheckHelper;
import com.shoalter.mms_product_api.service.product.helper.PermissionHelper;
import com.shoalter.mms_product_api.service.product.helper.SaveProductRecordHelper;
import com.shoalter.mms_product_api.service.product.helper.SaveProductRecordRowHelper;
import com.shoalter.mms_product_api.service.product.pojo.BatchUpdateEwProductBindingRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.BatchUpdateProductInfoDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterDto;
import com.shoalter.mms_product_api.service.product.pojo.SingleEditProductDto;
import com.shoalter.mms_product_api.util.CollectionUtil;
import com.shoalter.mms_product_api.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class EwUpdateBindingService {
	private final PermissionHelper permissionHelper;
	private final SaveProductRecordRowHelper saveProductRecordRowHelper;
	private final SaveProductRecordHelper saveProductRecordHelper;
	private final ProductRepository productRepository;
	private final MessageSource messageSource;

	@Transactional
	public ResponseDto<Void> createUpdateBindingInfoRecord(
			UserDto userDto, BatchUpdateEwProductBindingRequestDto requestDto, int uploadType) {
		//data check
		checkUserRole(userDto);

		if (CollectionUtil.isEmpty(requestDto.getData())) {
			throw new NoDataException();
		}

		if (requestDto.getData().size() > BatchCheckHelper.MAXIMUM_10000) {
			return ResponseDto.fail(List.of(messageSource.getMessage("message16", new String[]{String.format("%,d", BatchCheckHelper.MAXIMUM_10000)}, null)));
		}

		//prepare data
		Set<String> storefrontStoreCodes = new HashSet<>();
		Set<String> skuCodes = new HashSet<>();
		Map<String, List<BatchUpdateEwProductBindingDto>> requestGroupByStoreMap = new HashMap<>();
		for (BatchUpdateEwProductBindingDto updateData : requestDto.getData()) {
			storefrontStoreCodes.add(updateData.getStorefrontStoreCode());
			skuCodes.add(updateData.getSkuCode());
			requestGroupByStoreMap.putIfAbsent(updateData.getStorefrontStoreCode(), new ArrayList<>());
			requestGroupByStoreMap.get(updateData.getStorefrontStoreCode()).add(updateData);
		}

		Map<Integer, List<Pair<BatchUpdateEwProductBindingDto, ProductStoreInfoDo>>> groupByMerchantIdMap = new HashMap<>();
		List<BatchUpdateEwProductBindingDto> storeNotFoundAndNoPermissionProductList = new ArrayList<>();
		Map<String, List<ProductStoreInfoDo>> storeProductMap = productRepository.findDistinctProductStoreInfoByBuCodeAndSkuCodesAndStorefrontStoreCodes(BuCodeEnum.HKTV.name(), skuCodes, storefrontStoreCodes).stream()
				.collect(Collectors.groupingBy(ProductStoreInfoDo::getStorefrontStoreCode));

		for (Map.Entry<String, List<BatchUpdateEwProductBindingDto>> entry : requestGroupByStoreMap.entrySet()) {
			List<ProductStoreInfoDo> productStoreInfoDo = storeProductMap.get(entry.getKey());
			if (productStoreInfoDo == null || productStoreInfoDo.isEmpty()) {
				storeNotFoundAndNoPermissionProductList.addAll(entry.getValue());
			} else {
				collectionBySkuCode(entry.getValue(), productStoreInfoDo, groupByMerchantIdMap);
			}
		}

		//Verify permissions between user and merchant
		for (Map.Entry<Integer, List<Pair<BatchUpdateEwProductBindingDto, ProductStoreInfoDo>>> entry : groupByMerchantIdMap.entrySet()) {
			try {
				permissionHelper.checkPermission(userDto, entry.getKey());
			} catch (SystemI18nException systemI18nException) {
				log.info("No permission to use this merchant : {} user : {}", entry.getKey(), userDto.getUserCode());
				storeNotFoundAndNoPermissionProductList.addAll(entry.getValue().stream().map(Pair::getLeft).collect(Collectors.toList()));
				groupByMerchantIdMap.remove(entry.getKey());
			}
		}

		String fileNamePrefix = requestDto.getFileName().substring(0, requestDto.getFileName().lastIndexOf("."));
		String fileNameSuffix = requestDto.getFileName().substring(requestDto.getFileName().lastIndexOf("."));
		AtomicInteger index = new AtomicInteger(1);
		boolean isMultipleFile = (CollectionUtil.isNotEmpty(storeNotFoundAndNoPermissionProductList) && !groupByMerchantIdMap.isEmpty()) || groupByMerchantIdMap.size() > 1;

		//save electronic stores not exist and no permissions record
		if (CollectionUtil.isNotEmpty(storeNotFoundAndNoPermissionProductList)) {
			String fileName = isMultipleFile ? fileNamePrefix + StringUtil.UNDERLINE + index.getAndIncrement() + fileNameSuffix : requestDto.getFileName();
			SaveProductRecordDo saveProductRecordDo =
					saveProductRecordHelper.createSaveProductRecord(userDto, ConstantType.NON_EXISTENT_MERCHANT_ID, uploadType,
							fileName, SaveProductStatus.CHECKING_UPDATE_BINDING_PRODUCT);

			List<SaveProductRecordRowDo> saveProductRecordRowDoList = storeNotFoundAndNoPermissionProductList.stream()
					.map(storeNotFoundProduct -> saveProductRecordRowHelper.generateProductRecordRowDo(saveProductRecordDo.getId(),
							generateSingleEditProductDto(storeNotFoundProduct, null), SaveProductStatus.CHECKING_UPDATE_BINDING_PRODUCT, null))
					.collect(Collectors.toList());

			saveProductRecordRowHelper.batchSaveSaveProductRecordRowDo(saveProductRecordRowDoList);
			log.info("create record id: {}, save product type: {}, row size: {}, upload user id: {}, status: {}", saveProductRecordDo.getId(), SaveProductTypeEnum.getProductTypeName(saveProductRecordDo.getUploadType()), saveProductRecordRowDoList.size(), saveProductRecordDo.getUploadUserId(), SaveProductStatusEnum.getProductStatusName(saveProductRecordDo.getStatus()));
		}

		//save success products record
		groupByMerchantIdMap.forEach((merchantId, productStoreInfoDoList) -> {
			String fileName = isMultipleFile ? fileNamePrefix + StringUtil.UNDERLINE + index.getAndIncrement() + fileNameSuffix : requestDto.getFileName();
			SaveProductRecordDo saveProductRecordDo =
					saveProductRecordHelper.createSaveProductRecord(userDto, merchantId, uploadType,
							fileName, SaveProductStatus.CHECKING_UPDATE_BINDING_PRODUCT);

			List<SaveProductRecordRowDo> saveProductRecordRowDoList = productStoreInfoDoList.stream()
					.map(updateDataPair -> {
						SingleEditProductDto productRequestDto = generateSingleEditProductDto(updateDataPair.getLeft(), updateDataPair.getRight());
						return saveProductRecordRowHelper.generateProductRecordRowDo(saveProductRecordDo.getId(), productRequestDto, SaveProductStatus.CHECKING_UPDATE_BINDING_PRODUCT, null);
					})
					.collect(Collectors.toList());
			saveProductRecordRowHelper.batchSaveSaveProductRecordRowDo(saveProductRecordRowDoList);
			log.info("create record id: {}, save product type: {}, row size: {}, upload user id: {}, status: {}", saveProductRecordDo.getId(), SaveProductTypeEnum.getProductTypeName(saveProductRecordDo.getUploadType()), saveProductRecordRowDoList.size(), saveProductRecordDo.getUploadUserId(), SaveProductStatusEnum.getProductStatusName(saveProductRecordDo.getStatus()));
		});

		return ResponseDto.success(null);
	}

	private SingleEditProductDto generateSingleEditProductDto(BatchUpdateEwProductBindingDto batchUpdateDto, ProductStoreInfoDo productStoreInfoDo) {
		SingleEditProductDto productRequestDto = new SingleEditProductDto();

		BatchUpdateProductInfoDto batchEditProductDto = new BatchUpdateProductInfoDto();
		batchEditProductDto.setBatchUpdateEwProductBindingDto(batchUpdateDto);

		ProductMasterDto productMasterDto = new ProductMasterDto();
		productMasterDto.setSkuId(batchUpdateDto == null ? null : batchUpdateDto.getSkuCode());
		productMasterDto.setUuid(productStoreInfoDo == null ? null : productStoreInfoDo.getUuid());

		productRequestDto.setProduct(productMasterDto);
		productRequestDto.setBatchEditElement(batchEditProductDto);
		return productRequestDto;
	}

	private void collectionBySkuCode(List<BatchUpdateEwProductBindingDto> batchUpdateEwProductBindingList, List<ProductStoreInfoDo> productStoreInfoDoList,
									 Map<Integer, List<Pair<BatchUpdateEwProductBindingDto, ProductStoreInfoDo>>> groupByMerchantIdSuccessMap) {

		Map<String, ProductStoreInfoDo> storeProductMap = productStoreInfoDoList.stream()
				.collect(Collectors.toMap(ProductStoreInfoDo::getSkuCode, Function.identity()));

		batchUpdateEwProductBindingList.forEach(batchUpdateDto -> {
			ProductStoreInfoDo productStoreInfoDo = storeProductMap.get(batchUpdateDto.getSkuCode());
			Integer merchantId = productStoreInfoDo != null ? productStoreInfoDo.getMerchantId()
					: storeProductMap.values().stream()
					.findFirst()
					.map(ProductStoreInfoDo::getMerchantId)
					.orElseThrow(() -> new SystemException("No Merchant ID found"));
			groupByMerchantIdSuccessMap.computeIfAbsent(merchantId, k -> new ArrayList<>())
					.add(Pair.of(batchUpdateDto, productStoreInfoDo));
		});
	}

	private void checkUserRole(UserDto userDto) {
		if (RoleCode.EW_SKU_ALLOW_ROLE_SET.contains(userDto.getRoleCode())) {
			return;
		}
		throw new SystemI18nException("message131");
	}
}
