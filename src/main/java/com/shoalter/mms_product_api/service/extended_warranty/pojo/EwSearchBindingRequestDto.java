package com.shoalter.mms_product_api.service.extended_warranty.pojo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class EwSearchBindingRequestDto implements Serializable {
    private Integer page;
    private Integer size;
	@JsonProperty("store_id")
	@SerializedName("store_id")
	private List<String> storeId;
	@JsonProperty("sku_id")
	@SerializedName("sku_id")
	private String skuId;
	@JsonProperty("order_by")
	@SerializedName("order_by")
	private List<String> orderBy;
}
