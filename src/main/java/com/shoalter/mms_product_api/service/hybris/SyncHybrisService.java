package com.shoalter.mms_product_api.service.hybris;


import com.google.gson.Gson;
import com.shoalter.mms_product_api.config.hybris.HybrisAction;
import com.shoalter.mms_product_api.config.product.BuCodeEnum;
import com.shoalter.mms_product_api.config.product.ProductActionEnum;
import com.shoalter.mms_product_api.config.product.QueryTypeEnum;
import com.shoalter.mms_product_api.config.type.ConstantType;
import com.shoalter.mms_product_api.dao.repository.product.ProductRepository;
import com.shoalter.mms_product_api.dao.repository.product.ProductStoreStatusRepository;
import com.shoalter.mms_product_api.dao.repository.product.TempProductSyncHybrisRecordRepository;
import com.shoalter.mms_product_api.dao.repository.product.pojo.TempProductSyncHybrisRecordDo;
import com.shoalter.mms_product_api.mapper.SyncHybrisDataMapper;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.hybris.enums.HybrisMqFromSystemEnum;
import com.shoalter.mms_product_api.service.hybris.enums.SyncHybrisStatusEnum;
import com.shoalter.mms_product_api.service.hybris.helper.HybrisMqFlowHelper;
import com.shoalter.mms_product_api.service.hybris.pojo.HybrisUpdateDiscountRuleMqMessage;
import com.shoalter.mms_product_api.service.hybris.pojo.HybrisUpdatePriceActionDto;
import com.shoalter.mms_product_api.service.hybris.pojo.HybrisUpdateProductMainMqMessage;
import com.shoalter.mms_product_api.service.hybris.pojo.SyncHybrisMqDto;
import com.shoalter.mms_product_api.service.product.helper.ExchangeRateHelper;
import com.shoalter.mms_product_api.service.product.helper.ProductMasterHelper;
import com.shoalter.mms_product_api.service.product.pojo.ExchangeRatePriceDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterResultDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductOverviewResultDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductSearchRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.SaveProductData;
import com.shoalter.mms_product_api.util.CollectionUtil;
import com.shoalter.mms_product_api.util.DateUtil;
import com.shoalter.mms_product_api.util.SpringBeanProvider;
import com.shoalter.mms_product_api.util.enums.CurrencyEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

import static com.shoalter.mms_product_api.service.product.helper.ProductMasterHelper.SEARCH_MAX_SIZE;
import static com.shoalter.mms_product_api.util.BigDecimalUtil.removeAmtLastZero;

@RequiredArgsConstructor
@Service
@Slf4j
public class SyncHybrisService {

	private final ProductStoreStatusRepository productStoreStatusRepository;
	private final TempProductSyncHybrisRecordRepository syncHybrisRecordRepository;
	private final ProductRepository productRepository;
	private final ExchangeRateHelper exchangeRateHelper;
	private final HybrisMqFlowHelper hybrisMqFlowHelper;
	private final ProductMasterHelper productMasterHelper;
	private final SyncHybrisDataMapper syncHybrisDataMapper;
	private final Gson gson;

	public ResponseDto<Void> syncMainlandContractSkuToHybris() {
		SpringBeanProvider.getBean(SyncHybrisService.class).processMainlandContractRmbSkuSyncHybris();
		return ResponseDto.success(null);
	}

	@Async("createRecordExecutor")
	public void processMainlandContractRmbSkuSyncHybris() {
		log.info("Start Process Mainland Contract Rmb Sku Sync To Hybris");

		List<Integer> mainlandSkuProductIds = productStoreStatusRepository.findMainlandContractProductIds();
		log.info("Mainland Sku Product count:{}", mainlandSkuProductIds.size());

		int batchSize = 1000;
		List<SyncHybrisMqDto> findSyncHybrisMqDtos = new ArrayList<>();
		List<List<Integer>> partitionedPromotionProductLists = ListUtils.partition(mainlandSkuProductIds, batchSize);

		log.info("Partition size:{}", partitionedPromotionProductLists.size());
		for (List<Integer> partitionedPromotion : partitionedPromotionProductLists) {
			List<SyncHybrisMqDto> syncHybrisMqDtos = syncHybrisDataMapper.toSyncHybrisMqDto(
					productRepository.findSyncHybrisMqRmbProductsByProductIds(partitionedPromotion)
			);
			findSyncHybrisMqDtos.addAll(syncHybrisMqDtos);
		}

		// Production does not have mainland contract SKUs, so not use them for now.
//		List<String> storefrontStoreCode = syncHybrisMqDtos.stream().map(SyncHybrisMqDto::getStorefrontStoreCode).distinct().collect(Collectors.toList());
//		List<SyncHybrisMqDto> bundleMainlandSkuSyncDtos = generateBundleMainlandContractSkuToSyncDto(storefrontStoreCode);
//		syncHybrisMqDtos.addAll(bundleMainlandSkuSyncDtos);

		log.info("Sync Hybris RMB Product count:{}", findSyncHybrisMqDtos.size());

		Pair<List<HybrisUpdateProductMainMqMessage>, Map<String, TempProductSyncHybrisRecordDo>> syncHybrisData = generateHybrisUpdateProductMainMqMessages(findSyncHybrisMqDtos);
		List<HybrisUpdateProductMainMqMessage> hybrisUpdateProductMainMqMessages = syncHybrisData.getLeft();
		Map<String, TempProductSyncHybrisRecordDo> syncHybrisRecordTraceIdMap = syncHybrisData.getRight();

		// sent to hybris
		hybrisUpdateProductMainMqMessages.forEach(hybrisData -> {
			TempProductSyncHybrisRecordDo syncHybrisRecordDo = syncHybrisRecordTraceIdMap.getOrDefault(hybrisData.getTraceId(), null);
			if (syncHybrisRecordDo == null) {
				log.info("Cannot find TempProductSyncHybrisRecordDo, traceId:{}, skuCode:{}, action:{}, fromSystem:{}",
					hybrisData.getTraceId(), hybrisData.getSkuCode(), hybrisData.getAction(), hybrisData.getFromSystem());
			}

			try {
				hybrisMqFlowHelper.sendToHybrisForSync(hybrisData, syncHybrisRecordDo);
			} catch (Exception e) {
				log.error("processMainlandContractRmbSkuSyncHybris Error traceId:{}, storefrontStoreCode:{}, SkuCode:{}, error message:{}",
					hybrisData.getTraceId(), hybrisData.getMerchantId(), hybrisData.getSkuCode(), e.getMessage(), e);
			}
		});
		log.info("Finish Process Mainland Contract Rmb Sku Sync To Hybris");
	}

	private Pair<List<HybrisUpdateProductMainMqMessage>, Map<String, TempProductSyncHybrisRecordDo>> generateHybrisUpdateProductMainMqMessages(List<SyncHybrisMqDto> syncHybrisMqDtos) {
		BigDecimal rmbRate = exchangeRateHelper.getExchangeRateByCurrency(CurrencyEnum.RMB);
		SimpleDateFormat simpleDateFormat = new SimpleDateFormat(DateUtil.DATE_FORMAT_SIMPLE);
		Map<String, TempProductSyncHybrisRecordDo> syncHybrisRecordTraceIdMap = new HashMap<>();
		List<HybrisUpdateProductMainMqMessage> hybrisUpdateProductMainMqMessages = new ArrayList<>();
		// not include RMB_SKU_WHITELIST sku check
		SaveProductData saveProductData = SaveProductData.builder().rmbToHkdExchangeRate(rmbRate).build();

		syncHybrisMqDtos.forEach(syncHybrisMqDto -> {
			ExchangeRatePriceDto exchangeRatePrice = exchangeRateHelper.checkCurrencyAndGenerateHybrisPrice(
				syncHybrisMqDto.getSkuCode(),
				saveProductData,
				CurrencyEnum.RMB.name(),
				syncHybrisMqDto.getOriginalPrice(),
				syncHybrisMqDto.getSellingPrice()
			);
			log.info("generateHybrisUpdateProductMainMqMessages storeSkuId:{}, exchangeRatePrice:{}", syncHybrisMqDto.getStoreSkuId(), gson.toJson(exchangeRatePrice));

			String traceId = ConstantType.SYNC_PRICE_TO_HYBRIS_RMB_PREFIX + UUID.randomUUID();
			HybrisUpdatePriceActionDto hybrisUpdateProductMainMqMessage = HybrisUpdatePriceActionDto.builder()
				.traceId(traceId)
				.fromSystem(HybrisMqFromSystemEnum.MMS_PRODUCT.getCode())
				.action(HybrisAction.PRODUCT_SYNC_MODE_PRICE)
				.merchantId(syncHybrisMqDto.getStorefrontStoreCode())
				.skuCode(syncHybrisMqDto.getSkuCode())
				.originalPrice(exchangeRatePrice.getOriginalPriceHkd() == null ? null : exchangeRatePrice.getOriginalPriceHkd().doubleValue())
				.originalMainlandPrice(exchangeRatePrice.getOriginalMainlandPrice() == null ? null : exchangeRatePrice.getOriginalMainlandPrice().doubleValue())
				.primaryHktvCatId(syncHybrisMqDto.getPrimaryCatCode())
				.discountRule(generateSaveHybrisCreatePromotionalDiscountRuleDto(syncHybrisMqDto, simpleDateFormat, exchangeRatePrice))
				.build();

			hybrisUpdateProductMainMqMessages.add(hybrisUpdateProductMainMqMessage);
			syncHybrisRecordTraceIdMap.putIfAbsent(traceId, generateSyncHybrisRecordDo(syncHybrisMqDto, hybrisUpdateProductMainMqMessage));
		});

		return Pair.of(hybrisUpdateProductMainMqMessages, syncHybrisRecordTraceIdMap);
	}

	private HybrisUpdateDiscountRuleMqMessage generateSaveHybrisCreatePromotionalDiscountRuleDto(SyncHybrisMqDto product, SimpleDateFormat simpleDateFormat, ExchangeRatePriceDto exchangeRatePrice) {

		Date endDate = null;
		try {
			endDate = product.getSellingPrice() != null ? simpleDateFormat.parse(DateUtil.FOREVER_DATE) : new Date();
		} catch (ParseException e) {
			log.error("time format error: {}", e.getMessage(), e);
		}

		HybrisUpdateDiscountRuleMqMessage saveHybrisCreatePromotionalDiscountRuleDto = new HybrisUpdateDiscountRuleMqMessage();
		saveHybrisCreatePromotionalDiscountRuleDto.setNormalDiscount(exchangeRatePrice.getSellingPriceHkd() == null ? null : exchangeRatePrice.getSellingPriceHkd().doubleValue());
		saveHybrisCreatePromotionalDiscountRuleDto.setStartDate(simpleDateFormat.format(new Date()));
		saveHybrisCreatePromotionalDiscountRuleDto.setEndDate(simpleDateFormat.format(endDate));

		BigDecimal commissionRate = product.getCommissionRate();
		// commission rate cut float point when it's an integer
		if (commissionRate != null && Math.max(0, commissionRate.stripTrailingZeros().scale()) == 0) {
			saveHybrisCreatePromotionalDiscountRuleDto.setCommissionRate(removeAmtLastZero(commissionRate));
		} else {
			saveHybrisCreatePromotionalDiscountRuleDto.setCommissionRate(commissionRate);
		}

		saveHybrisCreatePromotionalDiscountRuleDto.setDiscountTextEn(product.getDiscountText());
		saveHybrisCreatePromotionalDiscountRuleDto.setDiscountTextZh(product.getDiscountTextTchi());
		saveHybrisCreatePromotionalDiscountRuleDto.setDiscountTextZhCN(product.getDiscountTextSchi());
		saveHybrisCreatePromotionalDiscountRuleDto.setStyle(product.getStyle());
		return saveHybrisCreatePromotionalDiscountRuleDto;
	}


	private TempProductSyncHybrisRecordDo generateSyncHybrisRecordDo(SyncHybrisMqDto syncHybrisMqDto, HybrisUpdateProductMainMqMessage hybrisUpdateProductMainMqMessage) {
		TempProductSyncHybrisRecordDo syncHybrisRecordDo = new TempProductSyncHybrisRecordDo();
		syncHybrisRecordDo.setTraceId(hybrisUpdateProductMainMqMessage.getTraceId());
		syncHybrisRecordDo.setStoreSkuId(syncHybrisMqDto.getStoreSkuId());
		syncHybrisRecordDo.setStatus(SyncHybrisStatusEnum.PROCESSING);
		syncHybrisRecordDo.setAction(hybrisUpdateProductMainMqMessage.getAction());
		return syncHybrisRecordRepository.save(syncHybrisRecordDo);
	}

	private List<SyncHybrisMqDto> generateBundleMainlandContractSkuToSyncDto(List<String> storefrontStoreCode) {
		if (CollectionUtil.isEmpty(storefrontStoreCode)) {
			return new ArrayList<>();
		}
		UserDto userDto = UserDto.generateSystemUserDto();
		ProductSearchRequestDto productSearchRequestDto = ProductSearchRequestDto.builder()
			.page(1)
			.size(SEARCH_MAX_SIZE)
			.buCode(List.of(BuCodeEnum.HKTV.name()))
			.isBundle(Boolean.TRUE)
			.storefrontStoreCodes(storefrontStoreCode)
			.build();
		ProductOverviewResultDto productOverviewResult = productMasterHelper.requestProductsByParams(userDto, productSearchRequestDto, QueryTypeEnum.LEFT.name());
		if (productOverviewResult == null) {
			log.error("Cannot find mainland bundle from product master");
			return new ArrayList<>();
		}
		if (productOverviewResult.getTotalElements() == 0) {
			log.info("Mainland bundle from product master is empty");
			return new ArrayList<>();
		}
		List<ProductMasterResultDto> bundleResults = new ArrayList<>(productOverviewResult.getContent());

		int totalPage = productOverviewResult.getTotalPages();
		for (int i = productSearchRequestDto.getPage() + 1; i <= totalPage; i++) {
			productSearchRequestDto.setPage(i);
			ProductOverviewResultDto iteratorResultDto = productMasterHelper.requestProductsByParams(userDto, productSearchRequestDto, QueryTypeEnum.LEFT.name());
			if (iteratorResultDto == null) {
				log.error("Cannot find mainland bundle from product master, query page:{}", i);
				continue;
			}
			if (CollectionUtil.isNotEmpty(iteratorResultDto.getContent())) {
				bundleResults.addAll(iteratorResultDto.getContent());
			}
		}

		return bundleResults.stream()
			.filter(bundleResult -> bundleResult != null && bundleResult.getAdditional() != null && bundleResult.getAdditional().getHktv() != null)
			.map(syncHybrisDataMapper::toSyncHybrisMqDto)
			.collect(Collectors.toList());

	}
}
