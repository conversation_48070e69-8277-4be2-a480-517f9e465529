package com.shoalter.mms_product_api.service.bundle.helper;

import com.google.gson.Gson;
import com.shoalter.mms_product_api.config.product.template.EditProductCommonlyUsedTemplateEnum;
import com.shoalter.mms_product_api.config.product.template.SyncOfflineBundleTemplateEnum;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordDo;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordRowDo;
import com.shoalter.mms_product_api.service.product.AbstractReport;
import com.shoalter.mms_product_api.service.product.pojo.HktvProductDto;
import com.shoalter.mms_product_api.service.product.pojo.SingleEditProductDto;
import com.shoalter.mms_product_api.util.ExcelUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellUtil;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.shoalter.mms_product_api.config.product.ExcelValidationName.VALIDATION_PRODUCT_STATUS;
import static com.shoalter.mms_product_api.config.product.ExcelValidationName.VALIDATION_VISIBLE;
import static com.shoalter.mms_product_api.util.ExcelUtil.PRODUCT_STATUS_LIST;
import static com.shoalter.mms_product_api.util.ExcelUtil.VISIBLE_LIST;

@Service
@Slf4j
@RequiredArgsConstructor
public class SyncOfflineBundleTemplateHelper extends AbstractReport  {
    public static final String SHEET_NAME = "Bundle Offline";
	private final Gson gson;

    public Workbook start(List<SaveProductRecordRowDo> rowList){
        Workbook workbook = new XSSFWorkbook();
        addDefaultStyle(workbook);
        addDataSheet(workbook);
		setSyncOfflineBundleUsedData(workbook, rowList);
		return workbook;
    }

    private void addDataSheet(Workbook workbook) {
        Sheet dataSheet = workbook.createSheet(SHEET_NAME);
        addHeaderColumn(workbook, dataSheet);
		dataSheet.createFreezePane(3, 1, 3, 1);
    }

    private void addHeaderColumn(Workbook workbook, Sheet dataSheet) {
		CellStyle headerLockedBlackFontStyle = ExcelUtil.createTableHeaderStyle(workbook, HorizontalAlignment.CENTER, VerticalAlignment.CENTER,"SansSerif", HSSFColor.HSSFColorPredefined.BLACK,true, true);
		CellStyle headerLockedRedFontStyle = ExcelUtil.createTableHeaderStyle(workbook, HorizontalAlignment.CENTER,VerticalAlignment.CENTER,"SansSerif",HSSFColor.HSSFColorPredefined.RED,true, true);
        int rowNum = 0;
        int colNum = 0;
        Row row = CellUtil.getRow(rowNum, dataSheet);
        row.setHeight((short) (30 * 20));

        for (SyncOfflineBundleTemplateEnum columnEnum : SyncOfflineBundleTemplateEnum.values()) {
            dataSheet.setColumnWidth(columnEnum.getColumnNumber(), (short) (columnEnum.getColName().length() + 4) * 256);

            Cell cell = CellUtil.getCell(row, colNum);
            cell.setCellValue(columnEnum.getColName());

            if (SyncOfflineBundleTemplateEnum.SKU_CODE == columnEnum || SyncOfflineBundleTemplateEnum.SKU_STATUS == columnEnum) {
                cell.setCellStyle(headerLockedBlackFontStyle);
            } else {
                cell.setCellStyle(headerLockedRedFontStyle);
            }
            colNum++;
        }
    }

	private void setSyncOfflineBundleUsedData(Workbook workbook, List<SaveProductRecordRowDo> rowList) {
		Sheet dataSheet = workbook.getSheet(SyncOfflineBundleTemplateHelper.SHEET_NAME);
		CellStyle bodyStyle = ExcelUtil.createBodyStyle(workbook, HorizontalAlignment.CENTER, false, false, false, false, true);
		int rowIndex = 1;

		for (SaveProductRecordRowDo recordRow : rowList) {
			SingleEditProductDto singleEditProductDto = gson.fromJson(recordRow.getContent(), SingleEditProductDto.class);
			HktvProductDto hktvProductDto = singleEditProductDto.getProduct().getAdditional().getHktv();
			setCellValue(dataSheet, bodyStyle, rowIndex, SyncOfflineBundleTemplateEnum.SKU_CODE.getColumnNumber(), hktvProductDto.getStoreSkuId());
			setCellValue(dataSheet, bodyStyle, rowIndex, SyncOfflineBundleTemplateEnum.SKU_STATUS.getColumnNumber(), hktvProductDto.getOnlineStatus().name());
			setCellValue(dataSheet, bodyStyle, rowIndex, SyncOfflineBundleTemplateEnum.ERROR_REASON.getColumnNumber(), recordRow.getErrorMessage());

			rowIndex++;
		}
	}
}
