package com.shoalter.mms_product_api.service.bundle;

import com.google.gson.Gson;
import com.shoalter.mms_product_api.config.product.BuCodeEnum;
import com.shoalter.mms_product_api.config.product.ErrorMessageTypeCode;
import com.shoalter.mms_product_api.config.product.OnlineStatusEnum;
import com.shoalter.mms_product_api.config.product.SaveProductStatus;
import com.shoalter.mms_product_api.config.product.SaveProductStatusEnum;
import com.shoalter.mms_product_api.config.product.SaveProductType;
import com.shoalter.mms_product_api.config.product.SaveProductTypeEnum;
import com.shoalter.mms_product_api.config.product.StatusCodeEnum;
import com.shoalter.mms_product_api.dao.repository.api.SaveProductRecordRepository;
import com.shoalter.mms_product_api.dao.repository.api.SaveProductRecordRowRepository;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordDo;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordRowDo;
import com.shoalter.mms_product_api.exception.SystemI18nException;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.bundle.helper.CheckBundleHelper;
import com.shoalter.mms_product_api.service.bundle.pojo.BundleInventoryInfoRequestDto;
import com.shoalter.mms_product_api.service.bundle.pojo.BundleSingleEditDto;
import com.shoalter.mms_product_api.service.bundle.pojo.BundleSingleEditProductDto;
import com.shoalter.mms_product_api.service.product.MembershipPricingEventSetDto;
import com.shoalter.mms_product_api.service.product.helper.CheckBuHelper;
import com.shoalter.mms_product_api.service.product.helper.CheckProductHelper;
import com.shoalter.mms_product_api.service.product.helper.GenerateIIDSDataHelper;
import com.shoalter.mms_product_api.service.product.helper.PermissionHelper;
import com.shoalter.mms_product_api.service.product.helper.ProductMasterHelper;
import com.shoalter.mms_product_api.service.product.helper.ProductPreProcessingHelper;
import com.shoalter.mms_product_api.service.product.helper.PromotionHelper;
import com.shoalter.mms_product_api.service.product.helper.SaveProductRecordHelper;
import com.shoalter.mms_product_api.service.product.helper.SaveProductRecordRowHelper;
import com.shoalter.mms_product_api.service.product.pojo.CheckProductResultDto;
import com.shoalter.mms_product_api.service.product.pojo.HktvProductDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterResultDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterSearchRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductRecordResponseDto;
import com.shoalter.mms_product_api.service.product.pojo.SaveProductResultDto;
import com.shoalter.mms_product_api.service.product.pojo.SingleEditProductDto;
import com.shoalter.mms_product_api.util.CollectionUtil;
import com.shoalter.mms_product_api.util.ResourceUtil;
import com.shoalter.mms_product_api.util.SpringBeanProvider;
import com.shoalter.mms_product_api.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.MessageSource;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Locale;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Service
@Slf4j
public class SingleEditBundleService {

	private final PermissionHelper permissionHelper;
	private final CheckBundleHelper checkBundleHelper;
	private final CheckProductHelper checkProductHelper;

	private final SaveProductRecordHelper saveProductRecordHelper;
	private final ProductPreProcessingHelper productPreProcessingHelper;
	private final GenerateIIDSDataHelper generateIIDSDataHelper;
	private final CheckBuHelper checkBuHelper;
	private final ProductMasterHelper productMasterHelper;
	private final SaveProductRecordRowHelper saveProductRecordRowHelper;
	private final PromotionHelper promotionHelper;
	private final MessageSource messageSource;
	private final SaveProductRecordRepository saveProductRecordRepository;
	private final SaveProductRecordRowRepository saveProductRecordRowRepository;
	private final Gson gson;

	public ResponseDto<ProductRecordResponseDto> start(UserDto userDto, BundleSingleEditDto bundleProduct, String clientIp) {
		ResponseDto<ProductRecordResponseDto> responseDto = SpringBeanProvider.getBean(SingleEditBundleService.class).processing(userDto, bundleProduct, clientIp);
		if (StatusCodeEnum.SUCCESS.getCode() == responseDto.getStatus()) {
			SpringBeanProvider.getBean(SingleEditBundleService.class).callProductMasterAndUpdateRecordRow(userDto, responseDto.getData().getRecordId());
		}

		return responseDto;
	}

	@Transactional
	public ResponseDto<ProductRecordResponseDto> processing(UserDto userDto, BundleSingleEditDto bundleProduct, String clientIp) {

		Integer merchantId = (bundleProduct.getProduct().getMerchantId() == null) ? userDto.getMerchantId() : bundleProduct.getProduct().getMerchantId();
		permissionHelper.checkPermission(userDto, merchantId);


		if (bundleProduct.getProduct().getAdditional().getHktv() != null) {
			HktvProductDto hktvProductDto = bundleProduct.getProduct().getAdditional().getHktv();
			BundleInventoryInfoRequestDto bundleInventoryInfoRequestDtoList = bundleProduct.getProduct().getBundleSetting().getMallInventoryInfo().stream()
					.filter(mallInfo -> mallInfo.getMall().equalsIgnoreCase(BuCodeEnum.HKTV.name())).findAny().orElseThrow();
			// check sku Id exists or not
			CheckProductResultDto checkSkuIsExistResult = checkProductSkuExistsInMerchant(userDto, bundleProduct);

			if (CollectionUtil.isEmpty(checkSkuIsExistResult.getErrorMessageList())) {
				return ResponseDto.<ProductRecordResponseDto>builder().status(StatusCodeEnum.FAIL.getCode()).errorMessageList(
						List.of(messageSource.getMessage("message51", null, Locale.getDefault()))).build();
			}

			if (!hktvProductDto.getIsPrimarySku().equals("Y")) {
				//bundle product must be the primary sku
				throw new SystemI18nException("message160", ErrorMessageTypeCode.SERVICE_EXCEPTION);
			}

			if (hktvProductDto.getOnlineStatus().equals(OnlineStatusEnum.OFFLINE)
					&& bundleInventoryInfoRequestDtoList.getSettingQuantity() > 0) {
				//avoid inventory quantity occupied
				throw new SystemI18nException("message163", ErrorMessageTypeCode.SERVICE_EXCEPTION);
			}

		}

		bundleProduct.getProduct().setMmsModifiedUser(userDto.getUserCode());
		bundleProduct.getProduct().setMmsModifiedTime(LocalDateTime.now());

		convertHktvProductPhoto(bundleProduct);
		BundleSingleEditProductDto product = bundleProduct.getProduct();
		SaveProductRecordDo saveProductRecordDo =
				saveProductRecordHelper.createSaveProductRecord(userDto, merchantId, SaveProductType.SINGLE_EDIT_BUNDLE, String.format(SaveProductRecordHelper.EDIT_PRODUCT_FILE_NAME, product.getSkuId(), System.currentTimeMillis()), SaveProductStatus.WAIT_START, clientIp);
		SaveProductRecordRowDo row = saveProductRecordRowHelper.createProductRecordRowDo(saveProductRecordDo.getId(), bundleProduct, SaveProductStatus.WAIT_START, null);
		product.setRecordRowId(row.getId());

		//request pm by product uuid to find product
		ProductMasterSearchRequestDto productMasterSearchRequestDto = ProductMasterSearchRequestDto.builder().uuids(Collections.singletonList(row.getUuid())).build();
		List<ProductMasterResultDto> productList = productMasterHelper.requestProductsByUuid(userDto, productMasterSearchRequestDto);
		if (CollectionUtil.isEmpty(productList)) {
			row.setErrorMessage(StringUtil.generateErrorMessage(List.of(messageSource.getMessage("message127", null, null))));
			row.setStatus(SaveProductStatus.FAIL);
		} else {
			ProductMasterResultDto beforeProduct = productList.get(0);
			//generate relate data in row content
			productPreProcessingHelper.preProcessingBundleProduct(userDto, saveProductRecordDo.getUploadType(), row, beforeProduct);
			generateIIDSDataHelper.generateIIDSData(row);

			String storeSkuId = beforeProduct.getAdditional().getHktv().getStoreSkuId();
			List<MembershipPricingEventSetDto> checkPricingResults = promotionHelper.checkMembershipPricingEventSet(userDto, List.of(storeSkuId));
			MembershipPricingEventSetDto checkPricingResult = checkPricingResults == null ? null : checkPricingResults.get(0);
			ResponseDto<Void> checkResult = checkBundleHelper.checkEditBundleHandler(userDto, row, beforeProduct, saveProductRecordDo, checkPricingResult);

			if (checkResult.getStatus() == StatusCodeEnum.SUCCESS.getCode()) {
				checkBuHelper.checkUpdateBuList(saveProductRecordDo, row);
			} else {
				row.setErrorMessage(StringUtil.generateErrorMessage(checkResult.getErrorMessageList()));
				row.setStatus(SaveProductStatus.FAIL);
				saveProductRecordDo.setStatus(SaveProductStatus.FAIL);
			}
		}
		log.info("create record id: {}, save product type: {}, row size: {}, upload user id: {}, status: {}", saveProductRecordDo.getId(), SaveProductTypeEnum.getProductTypeName(saveProductRecordDo.getUploadType()), 1, saveProductRecordDo.getUploadUserId(), SaveProductStatusEnum.getProductStatusName(saveProductRecordDo.getStatus()));
		return ResponseDto.success(ProductRecordResponseDto.builder().recordId(saveProductRecordDo.getId()).build());
	}

	@Transactional
	public void callProductMasterAndUpdateRecordRow(UserDto userDto, Long recordId) {
		SaveProductRecordDo saveProductRecordDo = saveProductRecordRepository.findById(recordId).orElseThrow();
		List<SaveProductRecordRowDo> rows = saveProductRecordRowRepository.findByRecordIdAndStatus(recordId, SaveProductStatus.WAIT_START);
		if (rows.isEmpty()) {
			saveProductRecordDo.setStatus(SaveProductStatus.FAIL);
			return;
		}

		SaveProductRecordRowDo row = rows.get(0);

		//send to pm
		SingleEditProductDto singleEditProductData = gson.fromJson(row.getContent(), SingleEditProductDto.class);
		ProductMasterDto productMasterDto = gson.fromJson(gson.toJson(singleEditProductData.getProduct()), ProductMasterDto.class);
		SaveProductResultDto productMasterCreateProductResult = productMasterHelper.requestEditProduct(userDto, List.of(productMasterDto), row.getSku(), HttpMethod.PUT, saveProductRecordDo);
		saveProductRecordHelper.updateRecordByProductMasterResult(productMasterCreateProductResult, saveProductRecordDo, List.of(row));
	}

	private CheckProductResultDto checkProductSkuExistsInMerchant(UserDto userDto, BundleSingleEditDto bundleProduct) {
		List<String> skuList = List.of(bundleProduct.getProduct().getSkuId());
		return checkProductHelper.checkProductSkuExistsInStore(userDto, bundleProduct.getProduct().getAdditional().getHktv().getStores(), skuList);
	}

	private void convertHktvProductPhoto(BundleSingleEditDto bundleProduct) {
		HktvProductDto hktvProductDto = bundleProduct.getProduct().getAdditional().getHktv();
		if (hktvProductDto != null) {
			if (StringUtil.isNotEmpty(hktvProductDto.getMainPhoto())) {
				hktvProductDto.setMainPhoto(convertPhoto(hktvProductDto.getMainPhoto()));
			}
			if (StringUtil.isNotEmpty(hktvProductDto.getAdvertisingPhoto())) {
				hktvProductDto.setAdvertisingPhoto(convertPhoto(hktvProductDto.getAdvertisingPhoto()));
			}
			if (CollectionUtil.isNotEmpty(hktvProductDto.getVariantProductPhoto())) {
				hktvProductDto.setVariantProductPhoto(hktvProductDto.getVariantProductPhoto().stream().map(this::convertPhoto).collect(Collectors.toList()));
			}
			if (CollectionUtil.isNotEmpty(hktvProductDto.getOtherPhoto())) {
				hktvProductDto.setOtherPhoto(hktvProductDto.getOtherPhoto().stream().map(this::convertPhoto).collect(Collectors.toList()));
			}
		}
	}

	private String convertPhoto(String photo) {
		return ResourceUtil.existsImageDomain(photo) && photo.lastIndexOf("_1200.") != -1 ?
				photo.substring(0, photo.lastIndexOf("_1200.")) + photo.substring(photo.lastIndexOf(".")) :
				photo;
	}
}
