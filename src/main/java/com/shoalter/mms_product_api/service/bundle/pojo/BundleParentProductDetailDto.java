package com.shoalter.mms_product_api.service.bundle.pojo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@Builder
public class BundleParentProductDetailDto {

	private String uuid;

	@JsonProperty(value = "last_synchronized_date")
	@SerializedName("last_synchronized_date")
	private Date lastSyncDate;

	@JsonProperty(value = "match_contract")
	@SerializedName("match_contract")
	private boolean matchContract;

	@JsonProperty(value = "storefront_store_code")
	@SerializedName("storefront_store_code")
	private String storeFrontStoreCode;

	@JsonProperty(value = "merchant_id")
	@SerializedName("merchant_id")
	private Integer merchantId;

	@JsonProperty(value = "merchant_name")
	@SerializedName("merchant_name")
	private String merchantName;

	@JsonProperty(value = "product_id")
	@SerializedName("product_id")
	private String productId;

	@JsonProperty(value = "brand_id")
	@SerializedName("brand_id")
	private Integer brandId;

	@JsonProperty(value = "manufactured_country")
	@SerializedName("manufactured_country")
	private String manufacturedCountry;

	@JsonProperty(value = "sku_id")
	@SerializedName("sku_id")
	private String skuId;

	@JsonProperty(value = "sku_name_en")
	@SerializedName("sku_name_en")
	private String skuNameEn;

	@JsonProperty(value = "sku_name_ch")
	@SerializedName("sku_name_ch")
	private String skuNameCh;

	@JsonProperty(value = "sku_name_sc")
	@SerializedName("sku_name_sc")
	private String skuNameSc;

	@JsonProperty(value = "original_price")
	@SerializedName("original_price")
	private BigDecimal originalPrice;

	@JsonProperty(value = "bundle_setting")
	@SerializedName("bundle_setting")
	private BundleSettingResponseDto bundleSetting;

	@JsonProperty("packing_box_type")
	@SerializedName("packing_box_type")
	private String packingBoxType;

	private BuBundleDto additional;
}
