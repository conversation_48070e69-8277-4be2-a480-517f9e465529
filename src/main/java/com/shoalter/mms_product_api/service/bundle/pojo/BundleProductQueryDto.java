package com.shoalter.mms_product_api.service.bundle.pojo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@Data
@Builder
public class BundleProductQueryDto {
	Integer page;
	Integer size;
	Boolean isBundle;
	String skuCode;
	String skuName;
	List<Integer> merchantId;
	String merchantName;
	List<String> storeId;
	List<String> productReadyMethod;
	Integer priority;
	List<String> storageType;
	List<String> orderBy;
}
