package com.shoalter.mms_product_api.service.bundle;

import com.shoalter.mms_product_api.config.product.BuCodeEnum;
import com.shoalter.mms_product_api.config.product.ErrorMessageTypeCode;
import com.shoalter.mms_product_api.config.product.ProductContractMatchStatusEnum;
import com.shoalter.mms_product_api.dao.mapper.businessUnit.BuProductCategoryMapper;
import com.shoalter.mms_product_api.dao.mapper.product.ProductStorePromotionMapper;
import com.shoalter.mms_product_api.dao.repository.store.StoreRepository;
import com.shoalter.mms_product_api.dao.repository.store.StoreWarehouseRepository;
import com.shoalter.mms_product_api.exception.SystemI18nException;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.bundle.pojo.BuBundleDto;
import com.shoalter.mms_product_api.service.bundle.pojo.BundleChildSkuDto;
import com.shoalter.mms_product_api.service.bundle.pojo.BundleInventoryInfoResponseDto;
import com.shoalter.mms_product_api.service.bundle.pojo.BundleParentDetailDto;
import com.shoalter.mms_product_api.service.bundle.pojo.BundleParentProductDetailDto;
import com.shoalter.mms_product_api.service.bundle.pojo.BundleSettingResponseDto;
import com.shoalter.mms_product_api.service.bundle.pojo.HktvBundleDto;
import com.shoalter.mms_product_api.service.bundle.pojo.request.BundleParentProductQueryDto;
import com.shoalter.mms_product_api.service.product.helper.InventoryHelper;
import com.shoalter.mms_product_api.service.product.helper.ProductMasterHelper;
import com.shoalter.mms_product_api.service.product.pojo.BuProductDto;
import com.shoalter.mms_product_api.service.product.pojo.CategoryInfoDto;
import com.shoalter.mms_product_api.service.product.pojo.HktvProductDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterResultDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterSearchRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductStatusDto;
import com.shoalter.mms_product_api.service.product.pojo.productinventoryapi.InventorySearchQtyFailResponseDto;
import com.shoalter.mms_product_api.service.product.pojo.productinventoryapi.InventorySearchQtyRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.productinventoryapi.InventorySearchQtyResponseDto;
import com.shoalter.mms_product_api.service.product.pojo.productinventoryapi.InventorySearchQtySuccessResponseDto;
import com.shoalter.mms_product_api.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Service
@Slf4j
public class FindBundleParentProductDetailService {

	private final ProductMasterHelper productMasterHelper;
	private final InventoryHelper inventoryHelper;
	private final ProductStorePromotionMapper productStorePromotionMapper;
	private final StoreWarehouseRepository storeWarehouseRepository;
	private final StoreRepository storeRepository;
	private final BuProductCategoryMapper buProductCategoryMapper;

	public ResponseDto<BundleParentDetailDto> start(BundleParentProductQueryDto bundleParentProductQueryDto, String buCode) {

		List<String> uuidList = List.of(bundleParentProductQueryDto.getUuid());

		//get product data from product master API
		List<ProductMasterResultDto> bundleProductResponseDtoList =
				findProductDataFromProductMasterAPI(bundleParentProductQueryDto.getUserDto(), uuidList);

		//get product store status from database
		List<String> childProductUuidList = bundleProductResponseDtoList.get(0).getBundleSetting().getChildSkuInfo()
				.stream().map(BundleChildSkuDto::getUuid).collect(Collectors.toList());
		List<ProductStatusDto> productStatusDtoList = productStorePromotionMapper.findProductStatusByUuids(childProductUuidList);
		if (productStatusDtoList.isEmpty()) {
			log.error("Failed to get product status from database, uuid: {}", uuidList);
			throw new SystemI18nException("message165", uuidList.get(0));
		}

		//get selling qty and ceiling qty from inventory API
		InventorySearchQtyResponseDto inventorySearchQtyResponseDto =
				findSellingQtyFromInventoryAPI(bundleParentProductQueryDto.getUserDto(), uuidList);

		BundleParentDetailDto detailDto =
				convertProductAndInventoryResponseDtoToBundleParentDetailDto(bundleProductResponseDtoList,
					inventorySearchQtyResponseDto, productStatusDtoList, buCode);

		return ResponseDto.<BundleParentDetailDto>builder().status(1).data(detailDto).build();
	}

	private List<ProductMasterResultDto> findProductDataFromProductMasterAPI(UserDto userDto, List<String> uuidList) {
		ProductMasterSearchRequestDto requestDto = ProductMasterSearchRequestDto.builder()
				.uuids(uuidList).build();
		List<ProductMasterResultDto> bundleProductResponseDtoList =
				productMasterHelper.requestProductsByUuid(userDto, requestDto);

		if (bundleProductResponseDtoList.isEmpty()) {
			log.error("Failed to get product data from product master API, uuidList: {}", uuidList);
			throw new SystemI18nException("message10", ErrorMessageTypeCode.PRODUCT_MASTER_POST_PRODUCTS_BATCH_PRODUCTS_ERROR);
		}

		return bundleProductResponseDtoList;
	}

	private InventorySearchQtyResponseDto findSellingQtyFromInventoryAPI(UserDto userDto, List<String> uuidList) {
		InventorySearchQtyRequestDto inventoryRequestDto =
				InventorySearchQtyRequestDto.generateInventorySearchQtyRequestDto(uuidList);
		InventorySearchQtyResponseDto inventorySearchQtyResponseDto =
				inventoryHelper.requestProductInventoryBundleSellingQty(userDto, inventoryRequestDto);

		if (inventorySearchQtyResponseDto == null) {
			log.error("Failed to get selling qty and ceiling qty from inventory API, uuidList: {}", uuidList);
			throw new SystemI18nException("message9", ErrorMessageTypeCode.INVENTORY_POST_PRODUCT_INVENTORY_SEARCH_BUNDLE_SELLING_QTY_ERROR);
		}

		return inventorySearchQtyResponseDto;
	}

	private BundleParentDetailDto convertProductAndInventoryResponseDtoToBundleParentDetailDto(
			List<ProductMasterResultDto> bundleProductResponseDtoList,
			InventorySearchQtyResponseDto inventorySearchQtyResponse, List<ProductStatusDto> productStatusDto, String buCode) {

		//Only request for 1 parent product for the purpose filling the edit square
		ProductMasterResultDto resultDto = bundleProductResponseDtoList.get(0);
		BuProductDto additionalDto = resultDto.getAdditional();
		List<InventorySearchQtySuccessResponseDto> successList = inventorySearchQtyResponse.getSuccessList();
		List<InventorySearchQtyFailResponseDto> failList = inventorySearchQtyResponse.getFailList();

		if (successList.size() <= 0 && failList.size() <= 0) {
			log.error("Failed to get inventory search qty from inventory API, inventorySearchQtyResponse: {}", inventorySearchQtyResponse);
			throw new SystemI18nException("message9", ErrorMessageTypeCode.INVENTORY_POST_PRODUCT_INVENTORY_SEARCH_BUNDLE_SELLING_QTY_ERROR);
		}

		boolean matchContract = true;
		for (ProductStatusDto productStatusDto1 : productStatusDto) {
			if (productStatusDto1.getStatus().equalsIgnoreCase(ProductContractMatchStatusEnum.NO_MATCH.getCode())) {
				matchContract = false;
				break;
			}
		}

		//set warehouse code for web
		String storefrontStoreCode = additionalDto.getHktv().getStoreSkuId().split(StringUtil.PRODUCT_SEPARATOR)[0];
		additionalDto.getHktv().setWarehouseCode(
				storeWarehouseRepository.findWarehouseIdWithDash(additionalDto.getHktv().getWarehouseId()));
		//set store id for web
		Integer storeId = storeRepository.findHktvStoreByStoreCode(additionalDto.getHktv().getStores())
				.orElseThrow(() -> new SystemI18nException("Store data not found, search by store code")).getId();
		additionalDto.getHktv().setStoreId(storeId);

		BundleParentProductDetailDto bundleParentProductDetailDto =
				BundleParentProductDetailDto.builder()
						.uuid(resultDto.getUuid())
						.lastSyncDate(resultDto.getModifiedTime())
						.matchContract(matchContract)
						.storeFrontStoreCode(storefrontStoreCode)
						.merchantId(resultDto.getMerchantId())
						.merchantName(resultDto.getMerchantName())
						.productId(resultDto.getProductId())
						.brandId(resultDto.getBrandId())
						.manufacturedCountry(resultDto.getManufacturedCountry())
						.skuId(resultDto.getSkuId())
						.skuNameCh(resultDto.getSkuNameCh())
						.skuNameEn(resultDto.getSkuNameEn())
						.skuNameSc(resultDto.getSkuNameSc())
						.packingBoxType(resultDto.getPackingBoxType())
						.originalPrice(resultDto.getOriginalPrice())
						.bundleSetting(BundleSettingResponseDto.generateBundleSettingResponseDto(resultDto.getBundleSetting()))
						.additional(convertFromPmAdditionalToBundleAdditional(additionalDto, buCode))
						.build();
		BundleSettingResponseDto bundleSettingResponseDto = bundleParentProductDetailDto.getBundleSetting();
		BundleInventoryInfoResponseDto bundleInventoryInfoResponseDto = bundleSettingResponseDto.getMallInventoryInfo()
				.stream().filter(bundleInventoryInfoDto1 -> bundleInventoryInfoDto1.getMall().equalsIgnoreCase(BuCodeEnum.HKTV.name())).findFirst()
				.orElseThrow(() -> new SystemI18nException("message9", ErrorMessageTypeCode.INVENTORY_POST_PRODUCT_INVENTORY_SEARCH_BUNDLE_SELLING_QTY_ERROR));

		String uuid = successList.size() > 0 ? successList.get(0).getUuid() : failList.get(0).getUuid();
		if (bundleInventoryInfoResponseDto != null && successList.size() > 0 && bundleParentProductDetailDto.getUuid().equals(uuid)) {
			bundleInventoryInfoResponseDto.setSettingQuantity(successList.get(0).getSettingQty());
			bundleInventoryInfoResponseDto.setAvailableQuantity(successList.get(0).getAvailableQty());
		} else if (bundleInventoryInfoResponseDto != null && failList.size() > 0 && bundleParentProductDetailDto.getUuid().equals(uuid)) {
			bundleInventoryInfoResponseDto.setSettingQuantity(null);
			bundleInventoryInfoResponseDto.setAlertQuantity(0);
			bundleInventoryInfoResponseDto.setCeilingQuantity(0);
			bundleInventoryInfoResponseDto.setAvailableQuantity(null);
		} else {
			log.error("uuid data from inventory API is not matched. uuid: {}, inventory uuid: {}", bundleParentProductDetailDto.getUuid(), uuid);
			throw new SystemI18nException("message9", ErrorMessageTypeCode.INVENTORY_POST_PRODUCT_INVENTORY_SEARCH_BUNDLE_SELLING_QTY_ERROR);
		}

		return BundleParentDetailDto.builder().product(bundleParentProductDetailDto).build();
	}

	private BuBundleDto convertFromPmAdditionalToBundleAdditional(BuProductDto additionalDto, String buCode) {
		HktvProductDto productMasterHktv = additionalDto.getHktv();
		HktvBundleDto hktvBundleDto = HktvBundleDto.convertFromHktvProductDto(productMasterHktv);

		// convert category code to category info for FN display
		CategoryInfoDto primaryCategoryInfo = buProductCategoryMapper.findCategoryInfoByBuCodeAndProductCatCodes(buCode, List.of(productMasterHktv.getPrimaryCategoryCode()))
				.stream()
				.findFirst()
				.orElseThrow(() -> new SystemI18nException("message49", productMasterHktv.getPrimaryCategoryCode()));
		List<CategoryInfoDto> productTypeInfo = productMasterHktv.getProductTypeCode().size() == 1 ? List.of(primaryCategoryInfo) :
				buProductCategoryMapper.findCategoryInfoByBuCodeAndProductCatCodes(buCode, productMasterHktv.getProductTypeCode());

		hktvBundleDto.setPrimaryCategoryInfo(primaryCategoryInfo);
		hktvBundleDto.setProductTypeInfo(productTypeInfo);

		return BuBundleDto.builder()
				.hktv(hktvBundleDto)
				.iids(additionalDto.getIids())
				.littleMall(additionalDto.getLittleMall())
				.build();
	}
}
