package com.shoalter.mms_product_api.service.partner_product_price.pojo;

import com.shoalter.mms_product_api.dao.repository.product.pojo.PartnerProductPriceDo;
import com.shoalter.mms_product_api.dao.repository.product.pojo.PartnerProductPriceHistoryDo;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class PartnerProductPriceChangedReportData {

	private String storefrontStoreCode;
	private String skuCode;
	private BigDecimal newChargePrice;
	private BigDecimal newChargeConverted;
	private BigDecimal newServiceFee;
	private BigDecimal originalChargePrice;
	private BigDecimal originalChargeConverted;
	private BigDecimal originalServiceFee;
	private LocalDateTime lastUpdateDate;


	public static PartnerProductPriceChangedReportData generateReportData(PartnerProductPriceDo currentData, PartnerProductPriceHistoryDo latestDiffHistory) {
		PartnerProductPriceChangedReportData reportData = new PartnerProductPriceChangedReportData();
		reportData.setStorefrontStoreCode(currentData.getStorefrontStoreCode());
		reportData.setSkuCode(currentData.getSkuCode());
		reportData.setNewChargePrice(currentData.getChargePrice());
		reportData.setNewChargeConverted(currentData.getChargeConverted());
		reportData.setNewServiceFee(currentData.getServiceFee());
		reportData.setLastUpdateDate(currentData.getLastUpdateDate());
		reportData.setOriginalChargePrice(latestDiffHistory.getChargePrice());
		reportData.setOriginalChargeConverted(latestDiffHistory.getChargeConverted());
		reportData.setOriginalServiceFee(latestDiffHistory.getServiceFee());
		return reportData;
	}
}
