package com.shoalter.mms_product_api.service.partner_product_price.helper;

import com.shoalter.mms_product_api.dao.repository.product.pojo.PartnerProductPriceDo;
import com.shoalter.mms_product_api.exception.SystemI18nException;
import com.shoalter.mms_product_api.service.partner_product_price.enums.TooniesSkuNotFoundColumnEnum;
import com.shoalter.mms_product_api.service.partner_product_price.enums.TooniesSkuPriceChangedColumnEnum;
import com.shoalter.mms_product_api.service.partner_product_price.pojo.PartnerProductPriceChangedReportData;
import com.shoalter.mms_product_api.service.product.AbstractReport;
import com.shoalter.mms_product_api.service.product.template.TemplateInterface;
import com.shoalter.mms_product_api.util.DateUtil;
import com.shoalter.mms_product_api.util.ExcelUtil;
import com.shoalter.mms_product_api.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellUtil;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.openxmlformats.schemas.spreadsheetml.x2006.main.CTCol;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

@RequiredArgsConstructor
@Service
@Slf4j
public class TooniesSkuTemplateHelper extends AbstractReport {

	private final String SKU_NOT_FOUND_SHEET_NAME = "Toonies_SKU_Not_Found";
	public static final String SKU_NOT_FOUND_FILE_NAME = "Toonies_SKU_Not_Found_%s.%s";
	private final String PRICE_CHANGE_REPORT_SHEET_NAME = "Toonies_SKU_Daily_Change_Report";
	public static final String PRICE_CHANGE_REPORT_FILE_NAME = "Toonies_SKU_Daily_Change_Report_%s.%s";
	public static final String FILE_DATE_FORMAT = "yyyyMMddhhmm";

	public ByteArrayResource generateTooniesSkuNotFoundFile(List<PartnerProductPriceDo> skuNotFoundList) {
		try (ByteArrayOutputStream os = new ByteArrayOutputStream();
			 Workbook tempWorkbook = setSkuNotFoundBodyColumn(setHeaderColumn(SKU_NOT_FOUND_SHEET_NAME, TooniesSkuNotFoundColumnEnum.class), skuNotFoundList)) {
			tempWorkbook.write(os);
			return new ByteArrayResource(os.toByteArray());
		} catch (IOException e) {
			log.error("Error occurred while creating Toonies SKU Not Found report file: {}", e.getMessage(), e);
			throw new SystemI18nException("message224");
		}
	}

	public ByteArrayResource generateTooniesSkuPriceChangedReport(List<PartnerProductPriceChangedReportData> reportData) {
		try (ByteArrayOutputStream os = new ByteArrayOutputStream();
			 Workbook tempWorkbook = setSkuPriceChangedReportBodyColumn(setHeaderColumn(PRICE_CHANGE_REPORT_SHEET_NAME, TooniesSkuPriceChangedColumnEnum.class), reportData)) {
			tempWorkbook.write(os);
			return new ByteArrayResource(os.toByteArray());
		} catch (IOException e) {
			log.error("Error occurred while creating Toonies SKU Price Changed report file: {}", e.getMessage(), e);
			throw new SystemI18nException("message224");
		}
	}

	public Workbook setHeaderColumn(String sheetName, Class<? extends TemplateInterface> templateEnum) {
		XSSFWorkbook workbook = new XSSFWorkbook();
		addDefaultStyle(workbook);
		XSSFSheet dataSheet = workbook.createSheet(sheetName);
		CellStyle headerStyle = ExcelUtil.createTableHeaderStyle(workbook, HorizontalAlignment.CENTER, false, true);
		headerStyle.setLocked(true);
		CellStyle notLockStyle = ExcelUtil.createTableHeaderStyle(workbook, HorizontalAlignment.CENTER, false, true);
		notLockStyle.setLocked(false);

		CTCol col = dataSheet.getCTWorksheet().getColsArray(0).addNewCol();
		col.setMin(1);
		col.setMax(16384);
		col.setWidth(9.15);
		col.setStyle(notLockStyle.getIndex());

		int rowNum = 0;
		int colNum = 0;
		Row row = CellUtil.getRow(rowNum, dataSheet);
		row.setHeight((short) (30 * 20));

		for (TemplateInterface columnEnum : templateEnum.getEnumConstants()) {
			Cell cell = CellUtil.getCell(row, colNum);
			cell.setCellValue(columnEnum.getColumnName());
			dataSheet.setColumnWidth(columnEnum.getColumnNumber(), columnEnum.getColumnName().length() * 400);
			cell.setCellStyle(headerStyle);
			colNum++;
		}
		return workbook;
	}

	public Workbook setSkuNotFoundBodyColumn(Workbook workbook, List<PartnerProductPriceDo> skuNotFoundList) {
		Workbook tempWorkbook = new SXSSFWorkbook((XSSFWorkbook) workbook, 100);
		Sheet sheet = tempWorkbook.getSheet(SKU_NOT_FOUND_SHEET_NAME);
		CellStyle bodyStyle = ExcelUtil.createBodyStyle(tempWorkbook, HorizontalAlignment.LEFT, false, false, false, false, false);
		AtomicInteger rowIndex = new AtomicInteger(1);

		skuNotFoundList.forEach(skuNotFound -> {
			setCellValue(sheet, bodyStyle, rowIndex.get(), TooniesSkuNotFoundColumnEnum.STORE_CODE.getColumnNumber(), skuNotFound.getStorefrontStoreCode());
			setCellValue(sheet, bodyStyle, rowIndex.get(), TooniesSkuNotFoundColumnEnum.SKU_ID.getColumnNumber(), skuNotFound.getSkuCode());
			rowIndex.addAndGet(1);
		});
		return tempWorkbook;
	}

	public Workbook setSkuPriceChangedReportBodyColumn(Workbook workbook, List<PartnerProductPriceChangedReportData> reportData) {
		Workbook tempWorkbook = new SXSSFWorkbook((XSSFWorkbook) workbook, 100);
		Sheet sheet = tempWorkbook.getSheet(PRICE_CHANGE_REPORT_SHEET_NAME);
		CellStyle bodyStyle = ExcelUtil.createBodyStyle(tempWorkbook, HorizontalAlignment.LEFT, false, false, false, false, false);
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DateUtil.DATE_FORMAT_SIMPLE);
		AtomicInteger rowIndex = new AtomicInteger(1);

		reportData.forEach(data -> {
			setCellValue(sheet, bodyStyle, rowIndex.get(), TooniesSkuPriceChangedColumnEnum.STORE_CODE.getColumnNumber(), data.getStorefrontStoreCode());
			setCellValue(sheet, bodyStyle, rowIndex.get(), TooniesSkuPriceChangedColumnEnum.SKU_ID.getColumnNumber(), data.getSkuCode());
			setCellValue(sheet, bodyStyle, rowIndex.get(), TooniesSkuPriceChangedColumnEnum.NEW_CHARGE_PRICE.getColumnNumber(), data.getNewChargePrice());
			setCellValue(sheet, bodyStyle, rowIndex.get(), TooniesSkuPriceChangedColumnEnum.NEW_CHARGE_PRICE_HKD.getColumnNumber(), data.getNewChargeConverted());
			setCellValue(sheet, bodyStyle, rowIndex.get(), TooniesSkuPriceChangedColumnEnum.NEW_SERVICE_FEE.getColumnNumber(), data.getNewServiceFee());
			setCellValue(sheet, bodyStyle, rowIndex.get(), TooniesSkuPriceChangedColumnEnum.ORIGINAL_CHARGE_PRICE.getColumnNumber(), data.getOriginalChargePrice());
			setCellValue(sheet, bodyStyle, rowIndex.get(), TooniesSkuPriceChangedColumnEnum.ORIGINAL_CHARGE_PRICE_HKD.getColumnNumber(), data.getOriginalChargeConverted());
			setCellValue(sheet, bodyStyle, rowIndex.get(), TooniesSkuPriceChangedColumnEnum.ORIGINAL_SERVICE_FEE.getColumnNumber(), data.getOriginalServiceFee());
			setCellValue(sheet, bodyStyle, rowIndex.get(), TooniesSkuPriceChangedColumnEnum.LAST_UPDATE_TIME.getColumnNumber(), data.getLastUpdateDate().format(formatter));
			rowIndex.addAndGet(1);
		});
		return tempWorkbook;
	}

	public HttpHeaders generateHeader() {
		String fileName = String.format(SKU_NOT_FOUND_FILE_NAME, new SimpleDateFormat(FILE_DATE_FORMAT).format(new Date()), StringUtil.FILE_EXTENSION_EXCEL);
		HttpHeaders header = new HttpHeaders();
		header.setContentType(MediaType.APPLICATION_OCTET_STREAM);
		header.setAccessControlExposeHeaders(Collections.singletonList(HttpHeaders.CONTENT_DISPOSITION));
		header.set(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + fileName);
		return header;
	}
}
