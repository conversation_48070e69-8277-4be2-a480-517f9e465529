package com.shoalter.mms_product_api.service.partner_product_price.helper;

import com.shoalter.mms_product_api.config.product.ThirdPartySourceEnum;
import com.shoalter.mms_product_api.dao.repository.product.PartnerProductPriceHistoryRepository;
import com.shoalter.mms_product_api.dao.repository.product.PartnerProductPriceRepository;
import com.shoalter.mms_product_api.dao.repository.product.pojo.PartnerProductPriceDo;
import com.shoalter.mms_product_api.dao.repository.product.pojo.PartnerProductPriceHistoryDo;
import com.shoalter.mms_product_api.mapper.PartnerProductPriceDataHistoryMapper;
import com.shoalter.mms_product_api.mapper.PartnerProductPriceDataMapper;
import com.shoalter.mms_product_api.service.partner_product_price.enums.TooniesStatusEnum;
import com.shoalter.mms_product_api.service.partner_product_price.pojo.PartnerProductPriceData;
import com.shoalter.mms_product_api.service.partner_product_price.pojo.PartnerProductPriceUpdateData;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@RequiredArgsConstructor
@Service
@Slf4j
public class PartnerProductPriceHelper {

	private final PartnerProductPriceRepository partnerProductPriceRepository;
	private final PartnerProductPriceHistoryRepository partnerProductPriceHistoryRepository;
	private final PartnerProductPriceDataMapper partnerProductPriceDataMapper;
	private final PartnerProductPriceDataHistoryMapper partnerProductPriceDataHistoryMapper;

	@Transactional
	public void saveHistoryAndUpdatePartnerProductPriceRecord(PartnerProductPriceDo partnerProductPriceDo, BigDecimal chargePrice, TooniesStatusEnum statusEnum, BigDecimal rmbExchangeRate) {
		LocalDateTime now = LocalDateTime.now();

		// history endDate = updateRecord lastUpdateDate : now
		PartnerProductPriceHistoryDo historyDo = partnerProductPriceDataHistoryMapper.toCreatePartnerProductPriceHistoryDo(partnerProductPriceDo, now);
		partnerProductPriceHistoryRepository.save(historyDo);

		PartnerProductPriceUpdateData updatePriceData = PartnerProductPriceUpdateData.builder()
			.status(statusEnum.getValue())
			.exchangeRate(rmbExchangeRate)
			.chargePrice(chargePrice)
			.build();

		PartnerProductPriceDo updatePartnerProductPriceDo =
			partnerProductPriceDataMapper.toUpdatePartnerProductPriceDo(updatePriceData, now, partnerProductPriceDo);
		partnerProductPriceRepository.save(updatePartnerProductPriceDo);
	}

	@Transactional
	public void savePartnerProductPriceDo
		(
			TooniesStatusEnum statusEnum,
			BigDecimal rmbExchangeRate,
			String storefrontStoreCode,
			String productCode,
			String skuCode,
			String storeSkuId,
			BigDecimal chargePrice
		) {
		PartnerProductPriceData priceData = generatePartnerProductPriceData(statusEnum, rmbExchangeRate, storefrontStoreCode, productCode, skuCode, storeSkuId, chargePrice);
		PartnerProductPriceDo createPartnerProductPriceDo = partnerProductPriceDataMapper.toCreatePartnerProductPriceDo(priceData);
		partnerProductPriceRepository.save(createPartnerProductPriceDo);
	}

	public PartnerProductPriceData generatePartnerProductPriceData
		(
			TooniesStatusEnum statusEnum,
			BigDecimal rmbExchangeRate,
			String storefrontStoreCode,
			String productCode,
			String skuCode,
			String storeSkuId,
			BigDecimal chargePrice
		) {
		return PartnerProductPriceData.builder()
			.source(ThirdPartySourceEnum.TOONIES.getValue())
			.status(statusEnum.getValue())
			.exchangeRate(rmbExchangeRate)
			.storefrontStoreCode(storefrontStoreCode)
			.productCode(productCode)
			.skuCode(skuCode)
			.storeSkuId(storeSkuId)
			.chargePrice(chargePrice)
			.build();
	}
}
