package com.shoalter.mms_product_api.service.partner_product_price.enums;

import com.shoalter.mms_product_api.service.product.template.TemplateInterface;
import lombok.Getter;

@Getter
public enum TooniesSkuPriceChangedColumnEnum implements TemplateInterface<TooniesSkuPriceChangedColumnEnum> {

	STORE_CODE(0, "Store Code", null, null, null),
	SKU_ID(1, "SKU ID", null, null, null),
	NEW_CHARGE_PRICE(2, "New Charge Price", null, null, null),
	NEW_CHARGE_PRICE_HKD(3, "New Charge Price (HKD)", null, null, null),
	NEW_SERVICE_FEE(4, "New Service Fee", null, null, null),
	ORIGINAL_CHARGE_PRICE(5, "Original Charge Price", null, null, null),
	ORIGINAL_CHARGE_PRICE_HKD(6, "Original Charge Price (HKD)", null, null, null),
	ORIGINAL_SERVICE_FEE(7, "Original Service Fee", null, null, null),
	LAST_UPDATE_TIME(8, "Last Update Time", null, null, null);

	private final Integer columnNumber;
	private final String columnName;
	private final String validationName;
	private final TooniesSkuPriceChangedColumnEnum parent;
	private final String parentSegment;

	TooniesSkuPriceChangedColumnEnum(Integer columnNumber, String columnName, String validationName, TooniesSkuPriceChangedColumnEnum parent, String parentSegment) {
		this.columnNumber = columnNumber;
		this.columnName = columnName;
		this.validationName = validationName;
		this.parent = parent;
		this.parentSegment = parentSegment;
	}

	@Override
	public Integer getParentColumnNumber() {
		return parent.getColumnNumber();
	}

}
