package com.shoalter.mms_product_api.service.partner_product_price;

import com.shoalter.mms_product_api.config.product.BuCodeEnum;
import com.shoalter.mms_product_api.config.product.StatusCodeEnum;
import com.shoalter.mms_product_api.config.product.SysParmCodeEnum;
import com.shoalter.mms_product_api.config.product.SysParmSegmentEnum;
import com.shoalter.mms_product_api.config.type.ConstantType;
import com.shoalter.mms_product_api.dao.repository.product.PartnerProductPriceHistoryRepository;
import com.shoalter.mms_product_api.dao.repository.product.PartnerProductPriceRepository;
import com.shoalter.mms_product_api.dao.repository.product.pojo.PartnerProductPriceDo;
import com.shoalter.mms_product_api.dao.repository.product.pojo.PartnerProductPriceHistoryDo;
import com.shoalter.mms_product_api.dao.repository.system.SysParmRepository;
import com.shoalter.mms_product_api.dao.repository.system.pojo.SysParmDo;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.external_system.mms_setting.enums.MmsSettingFunctionEnum;
import com.shoalter.mms_product_api.service.mms_cron_job.enums.MmsCronJobNameEnum;
import com.shoalter.mms_product_api.service.mms_cron_job.enums.MmsCronJobStatusEnum;
import com.shoalter.mms_product_api.service.mms_cron_job.helper.MmsCronJobHelper;
import com.shoalter.mms_product_api.service.mms_cron_job.pojo.MmsCronJobData;
import com.shoalter.mms_product_api.service.notification.enums.NotificationCodeEnum;
import com.shoalter.mms_product_api.service.notification.helper.NotificationHelper;
import com.shoalter.mms_product_api.service.notification.pojo.NotificationAttachmentRequest;
import com.shoalter.mms_product_api.service.notification.pojo.NotificationAttachmentResponse;
import com.shoalter.mms_product_api.service.notification.pojo.dto.MessageLanguageDto;
import com.shoalter.mms_product_api.service.notification.pojo.dto.ParameterDto;
import com.shoalter.mms_product_api.service.notification.pojo.dto.TemplateDetailDto;
import com.shoalter.mms_product_api.service.partner_product_price.enums.TooniesStatusEnum;
import com.shoalter.mms_product_api.service.partner_product_price.helper.PartnerProductPriceHelper;
import com.shoalter.mms_product_api.service.partner_product_price.helper.TooniesSkuTemplateHelper;
import com.shoalter.mms_product_api.service.partner_product_price.pojo.PartnerProductPriceChangedReportData;
import com.shoalter.mms_product_api.service.product.helper.ExchangeRateHelper;
import com.shoalter.mms_product_api.service.product.helper.MmsThirdPartySkuHelper;
import com.shoalter.mms_product_api.service.product.helper.ProductMasterHelper;
import com.shoalter.mms_product_api.service.product.pojo.littlemall.SearchProductIdsRequestData;
import com.shoalter.mms_product_api.service.product.pojo.productmaster.request.ProductMasterLightweightProductRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.productmaster.response.ProductMasterLightweightProductResponseDto;
import com.shoalter.mms_product_api.service.product.pojo.productmaster.response.ProductMasterLightweightProductsDto;
import com.shoalter.mms_product_api.service.product.pojo.third_party.pojo.TooniesProductDetailResponseDto;
import com.shoalter.mms_product_api.service.product.pojo.third_party.pojo.TooniesProductDetailSkuDto;
import com.shoalter.mms_product_api.util.CollectionUtil;
import com.shoalter.mms_product_api.util.StringUtil;
import com.shoalter.mms_product_api.util.enums.CurrencyEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.FileSystemResource;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.file.Files;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

import static com.shoalter.mms_product_api.service.partner_product_price.helper.TooniesSkuTemplateHelper.FILE_DATE_FORMAT;
import static com.shoalter.mms_product_api.service.partner_product_price.helper.TooniesSkuTemplateHelper.PRICE_CHANGE_REPORT_FILE_NAME;
import static com.shoalter.mms_product_api.service.partner_product_price.helper.TooniesSkuTemplateHelper.SKU_NOT_FOUND_FILE_NAME;

@RequiredArgsConstructor
@Service
@Slf4j
public class PartnerProductPriceService {

	private final ExchangeRateHelper exchangeRateHelper;
	private final ProductMasterHelper productMasterHelper;
	private final MmsThirdPartySkuHelper mmsThirdPartySkuHelper;
	private final TooniesSkuTemplateHelper tooniesSkuTemplateHelper;
	private final NotificationHelper notificationHelper;
	private final PartnerProductPriceHelper partnerProductPriceHelper;
	private final MmsCronJobHelper mmsCronJobHelper;
	private final SysParmRepository sysParmRepository;
	private final PartnerProductPriceRepository partnerProductPriceRepository;
	private final PartnerProductPriceHistoryRepository partnerProductPriceHistoryRepository;
	private final Executor partnerProductPriceTaskExecutor;

	@Value("${toonies.report.receiver}")
	private String TOONIES_REPORT_RECEIVER;
	@Value("${partner.product.price.check.timeout.minutes:360}")
	private Integer PARTNER_PRODUCT_PRICE_CHECK_TIMEOUT_MINUTES;
	@Value("${partner.product.price.report.timeout.minutes:1200}")
	private Integer PARTNER_PRODUCT_PRICE_REPORT_TIMEOUT_MINUTES;

	private static final int PROCESS_BATCH_SIZE = 2;

	private static final String TOONIES_SKU_NOT_FOUND_REPORT = "Toonies Sku Not Found Report";
	private static final String TEMPLATE_SKU_NOT_FOUND_DATE_FORMAT = "yyyyMMddHH";


	private static final String TOONIES_DAILY_CHARGE_CHANGED_REPORT = "Toonies Daily Charge Changed Report";
	private static final String TEMPLATE_DAILY_CHARGE_CHANGED_REPORT_DATE_FORMAT = "yyyyMMdd";

	@Async("cronJobTaskExecutor")
	public void savePartnerProductPriceRecord() {
		MmsCronJobData mmsCronJobData = mmsCronJobHelper.canProcessCronJob(MmsCronJobNameEnum.PARTNER_PRODUCT_PRICE_CHECK);
		if (Boolean.FALSE.equals(mmsCronJobData.isCanProcessCronJob())) {
			log.info("Cron job: {} processing not yet implemented", mmsCronJobData.getMmsCronJobName());
			return;
		}
		BigDecimal rmbExchangeRate = exchangeRateHelper.getExchangeRateByCurrency(MmsSettingFunctionEnum.PCR, CurrencyEnum.RMB);
		if (rmbExchangeRate == null) {
			log.warn("RMB exchange rate is not available, skipping savePartnerProductPriceRecord");
			return;
		}
		List<SysParmDo> supplierStoreWhitelist = sysParmRepository.findBySegmentAndCode(SysParmSegmentEnum.SUPPLIER_STORE_WHITELIST.name(), SysParmCodeEnum.TOONIES.getCode());
		if (CollectionUtil.isEmpty(supplierStoreWhitelist)) {
			log.warn("Supplier store whitelist is empty, skipping savePartnerProductPriceRecord");
			return;
		}

		Set<String> storefrontStoreCodes = supplierStoreWhitelist.stream()
			.map(SysParmDo::getParmValue)
			.filter(StringUtils::isNotBlank)
			.flatMap(paramValue -> Arrays.stream(paramValue.split(StringUtil.COMMA)))
			.collect(Collectors.toSet());

		if (CollectionUtil.isEmpty(storefrontStoreCodes)) {
			log.warn("StorefrontStoreCodes are empty, skipping savePartnerProductPriceRecord");
			return;
		}

		UserDto userDto = UserDto.generateSystemUserDto();
		Map<String, List<String>> storefrontStoreCodeToProductIdsMap = generateStorefrontStoreCodeToProductIdsMap(userDto, storefrontStoreCodes);

		if (CollectionUtil.isEmpty(storefrontStoreCodeToProductIdsMap)) {
			log.warn("No product IDs found for any storefrontStoreCode, skipping savePartnerProductPriceRecord");
			return;
		}
		int totalProductCount = storefrontStoreCodeToProductIdsMap.values().stream()
			.mapToInt(List::size)
			.sum();

		log.info("Product IDs found for storefrontStoreCodes, store count:{}, productId count:{}, store and Product IDs: {}",
			storefrontStoreCodeToProductIdsMap.size(), totalProductCount, storefrontStoreCodeToProductIdsMap);

		mmsCronJobData = mmsCronJobHelper.createOrUpdateProcessingCronJob(mmsCronJobData, PARTNER_PRODUCT_PRICE_CHECK_TIMEOUT_MINUTES);
		MmsCronJobStatusEnum mmsCronJobStatusEnum;
		try {
			CompletableFuture<Void> all = CompletableFuture.allOf(storefrontStoreCodeToProductIdsMap.entrySet().stream()
				.flatMap(entry -> {
					String storefrontStoreCode = entry.getKey();
					List<String> productIds = entry.getValue();
					return ListUtils.partition(productIds, PROCESS_BATCH_SIZE).stream()
						.map(partitionProductIds -> CompletableFuture.runAsync(() -> {
							try {
								processStoreProductGroup(userDto, storefrontStoreCode, partitionProductIds, rmbExchangeRate);
							} catch (Exception e) {
								log.error("processStoreProductGroup error, store: {}, productIds: {}, error: {}", storefrontStoreCode, partitionProductIds, e.getMessage(), e);
							}
						}, partnerProductPriceTaskExecutor));
				})
				.toArray(CompletableFuture[]::new));
			all.join();

			log.info("finish process product");

			// send mail notification for not found skus
			sendMailForNotFoundStoreSkuReport(storefrontStoreCodes);
			mmsCronJobStatusEnum = MmsCronJobStatusEnum.SUCCESS;
		} catch (Exception e) {
			log.error("savePartnerProductPriceRecord error: {}", e.getMessage(), e);
			mmsCronJobStatusEnum = MmsCronJobStatusEnum.FAIL;
		}
		mmsCronJobHelper.updateCronJobStatus(mmsCronJobData, mmsCronJobStatusEnum);
	}

	protected Map<String, List<String>> generateStorefrontStoreCodeToProductIdsMap(UserDto userDto, Set<String> storefrontStoreCodes) {
		Map<String, List<String>> storefrontStoreCodeToProductIdsMap = new HashMap<>();
		for (String storefrontStoreCode : storefrontStoreCodes) {
			SearchProductIdsRequestData searchProductIdsRequestData = SearchProductIdsRequestData.generateHktvStore(storefrontStoreCode);
			List<String> productIdsResponseData = productMasterHelper.requestProductIds(userDto, searchProductIdsRequestData);
			if (CollectionUtil.isEmpty(productIdsResponseData)) {
				log.warn("No Product IDs found for storefrontStoreCode: {}", storefrontStoreCode);
				continue;
			}
			storefrontStoreCodeToProductIdsMap.putIfAbsent(storefrontStoreCode, productIdsResponseData);
		}
		return storefrontStoreCodeToProductIdsMap;
	}

	protected void processStoreProductGroup(UserDto userDto, String storefrontStoreCode, List<String> productIds, BigDecimal rmbExchangeRate) {
		// Request lightweight product details from Product Master
		ProductMasterLightweightProductRequestDto lightweightProductRequest = ProductMasterLightweightProductRequestDto.builder()
			.storefrontStoreCode(storefrontStoreCode)
			.productIds(productIds).build();
		List<ProductMasterLightweightProductResponseDto> lightweightProducts = productMasterHelper.requestLightweightProduct(userDto, lightweightProductRequest);
		if (lightweightProducts == null || CollectionUtil.isEmpty(lightweightProducts)) {
			log.warn("No lightweight products found for storefrontStoreCode: {}, productIds: {}", storefrontStoreCode, productIds);
			return;
		}
		Map<String, List<ProductMasterLightweightProductsDto>> productCodeSkusMap = lightweightProducts.stream()
			.filter(response -> StringUtil.isNotEmpty(response.getProductCode()) && CollectionUtil.isNotEmpty(response.getProducts()))
			.collect(Collectors.toMap(ProductMasterLightweightProductResponseDto::getProductCode, ProductMasterLightweightProductResponseDto::getProducts));

		// Request toonies product details
		Map<String, List<TooniesProductDetailSkuDto>> tooniesProductSkusMap = new HashMap<>();
		productCodeSkusMap.keySet().forEach(productCode -> {
			try {
				TooniesProductDetailResponseDto tooniesProductDetailResponse = mmsThirdPartySkuHelper.fetchSkuTooniesDetail(productCode);
				if (tooniesProductDetailResponse != null && CollectionUtil.isNotEmpty(tooniesProductDetailResponse.getSkus())) {
					tooniesProductSkusMap.putIfAbsent(tooniesProductDetailResponse.getProductCode(), tooniesProductDetailResponse.getSkus());
				}
				Thread.sleep(150);
			} catch (InterruptedException e) {
				Thread.currentThread().interrupt();
				log.warn("Thread was interrupted during sleep after error: {}", e.getMessage());
			} catch (Exception e) {
				log.error("Error fetching Toonies product detail for storefrontStoreCode:{}, productCode {}: {}", storefrontStoreCode, productCode, e.getMessage(), e);
			}
		});
		processSkusData(productCodeSkusMap, tooniesProductSkusMap, rmbExchangeRate, storefrontStoreCode);
	}

	protected void processSkusData(
		Map<String, List<ProductMasterLightweightProductsDto>> productCodeSkusMap,
		Map<String, List<TooniesProductDetailSkuDto>> tooniesProductSkusMap,
		BigDecimal rmbExchangeRate,
		String storefrontStoreCode
	) {
		productCodeSkusMap.forEach((productCode, productSkus) -> {
			Map<String, BigDecimal> tooniesSkuMap;
			if (tooniesProductSkusMap.containsKey(productCode)) {
				tooniesSkuMap = tooniesProductSkusMap.get(productCode).stream()
					.filter(tooniesSku -> StringUtil.isNotEmpty(tooniesSku.getSkuCode()))
					.collect(Collectors.toMap(TooniesProductDetailSkuDto::getSkuCode, TooniesProductDetailSkuDto::getPrice, (a, b) -> b));
			} else {
				tooniesSkuMap = new HashMap<>();
			}


			productSkus.forEach(productSku -> {
				String storeSkuId = productSku.getStoreSkuId();
				String skuCode = productSku.getSkuCode();

				Optional<PartnerProductPriceDo> partnerProductPriceDo = partnerProductPriceRepository.findByBusUnitIdAndStoreSkuId(BuCodeEnum.HKTV.getBusUnitId(), storeSkuId);
				// check if sku is present in toonies
				TooniesStatusEnum tooniesStatusEnum = tooniesSkuMap.containsKey(skuCode) ? TooniesStatusEnum.SKU_FOUND : TooniesStatusEnum.SKU_NOT_FOUND;

				// sku not found in DB, create new record
				if (partnerProductPriceDo.isEmpty()) {
					partnerProductPriceHelper.savePartnerProductPriceDo(
						tooniesStatusEnum,
						rmbExchangeRate,
						storefrontStoreCode,
						productCode,
						skuCode,
						storeSkuId,
						tooniesSkuMap.get(skuCode)
					);
				} else {
					if (tooniesStatusEnum == TooniesStatusEnum.SKU_FOUND) {
						BigDecimal dbChargeConvertedPrice = partnerProductPriceDo.get().getChargeConverted();
						BigDecimal tooniesChargeConvertedPrice =
							Optional.ofNullable(tooniesSkuMap.get(skuCode))
								.map(chargePrice -> chargePrice.multiply(rmbExchangeRate).setScale(2, RoundingMode.HALF_UP))
								.orElse(null);
						boolean isChargePriceUpdate = !Objects.equals(dbChargeConvertedPrice, tooniesChargeConvertedPrice);
						boolean isTooniesStatusUpdate = partnerProductPriceDo.get().getStatus() == TooniesStatusEnum.SKU_NOT_FOUND.getValue();
						// sku present in DB, price or toonies status changed, update partner product price / insert history
						if (isChargePriceUpdate || isTooniesStatusUpdate) {
							partnerProductPriceHelper.saveHistoryAndUpdatePartnerProductPriceRecord(partnerProductPriceDo.get(), tooniesSkuMap.get(skuCode), tooniesStatusEnum, rmbExchangeRate);
						}
					} else {
						// SKU_FOUND -> SKU_NOT_SOUND : update status / insert history, price use original chargePrice and new exchange rate calculate
						if (partnerProductPriceDo.get().getStatus() == TooniesStatusEnum.SKU_FOUND.getValue()) {
							partnerProductPriceHelper.saveHistoryAndUpdatePartnerProductPriceRecord(partnerProductPriceDo.get(), partnerProductPriceDo.get().getChargePrice(), tooniesStatusEnum, rmbExchangeRate);
						}
					}
				}
			});
		});
	}

	private void sendMailForNotFoundStoreSkuReport(Set<String> storefrontStoreCodes) {
		log.info("start sendMailForNotFoundStoreSkuReport");
		if (CollectionUtil.isEmpty(storefrontStoreCodes)) {
			log.info("No not found store SKUs to send notification");
			return;
		}
		List<PartnerProductPriceDo> skuNotFoundList = partnerProductPriceRepository.findByBusUnitIdAndStorefrontStoreCodeInAndStatus(BuCodeEnum.HKTV.getBusUnitId(), storefrontStoreCodes, TooniesStatusEnum.SKU_NOT_FOUND.getValue());
		if (CollectionUtil.isEmpty(skuNotFoundList)) {
			log.info("No not found store SKUs to send notification");
			return;
		}

		File tempFile = null;
		try {
			ByteArrayResource byteArrayResource = tooniesSkuTemplateHelper.generateTooniesSkuNotFoundFile(skuNotFoundList);
			tempFile = notificationHelper.createTempExcelFile(byteArrayResource.getByteArray(), SKU_NOT_FOUND_FILE_NAME, FILE_DATE_FORMAT);

			String parameterDate = new SimpleDateFormat(TEMPLATE_SKU_NOT_FOUND_DATE_FORMAT).format(new Date());
			List<MessageLanguageDto> messageLanguageList = MessageLanguageDto.generateAllLanguageTemplate(
				TemplateDetailDto.builder()
					.parameterList(List.of(
						new ParameterDto(TEMPLATE_SKU_NOT_FOUND_DATE_FORMAT, parameterDate)
					)).build(), null
			);

			NotificationAttachmentRequest request = notificationHelper.generateSystemNotificationAttachmentRequestForGroupReceiver(
				new FileSystemResource(tempFile),
				NotificationCodeEnum.TOONIES_SKU_NOT_FOUND,
				messageLanguageList,
				false,
				TOONIES_SKU_NOT_FOUND_REPORT,
				TOONIES_REPORT_RECEIVER
			);
			ResponseDto<NotificationAttachmentResponse> response = notificationHelper.requestSendMessageWithAttachment(request);
			if (response.getStatus() == StatusCodeEnum.SUCCESS.getCode()) {
				log.info("sendMailForNotFoundStoreSkuReport success");
			} else {
				log.error("sendMailForNotFoundStoreSkuReport fail");
			}
		} catch (
			Exception e) {
			log.error("Error occurred while sendMailForNotFoundStoreSkuReport: {}", e.getMessage(), e);
		} finally {
			// delete temp file
			if (tempFile != null && tempFile.exists()) {
				try {
					Files.delete(tempFile.toPath());
				} catch (IOException e) {
					log.error("Error occurred while deleting temp file: {}", e.getMessage(), e);
				}
			}
		}
	}

	@Async("cronJobTaskExecutor")
	public void sendMailForPartnerProductPriceChangeDailyReport() {
		MmsCronJobData mmsCronJobData = mmsCronJobHelper.canProcessCronJob(MmsCronJobNameEnum.PARTNER_PRODUCT_PRICE_REPORT);
		if (Boolean.FALSE.equals(mmsCronJobData.isCanProcessCronJob())) {
			log.info("Cron job: {} processing not yet implemented", mmsCronJobData.getMmsCronJobName());
			return;
		}
		LocalDateTime endTime = LocalDate.now().atTime(9, 30);
		LocalDateTime startTime = endTime.minusDays(1);
		// find all update price sku between day
		List<PartnerProductPriceDo> currentDataList = partnerProductPriceRepository.findByLastUpdateDateBetween(startTime, endTime);
		if (CollectionUtil.isEmpty(currentDataList)) {
			log.info("Partner product price change record is empty, nothing to do");
			return;
		}
		// find all sku history between day
		List<PartnerProductPriceHistoryDo> allHistories =
			partnerProductPriceHistoryRepository.findByEndDateBetween(startTime, endTime);

		if (CollectionUtil.isEmpty(allHistories)) {
			log.info("PartnerProduct price change record history is empty, nothing to do");
			return;
		}

		// find price changed history and generate daily report data
		List<PartnerProductPriceChangedReportData> reportData = generateReportDate(currentDataList, allHistories);
		if (CollectionUtil.isEmpty(reportData)) {
			log.info("No Partner Product Price Change to send Daily Report");
			return;
		}

		log.info("Sending email notification for Partner Product Price Change Daily Report SKU count: {}", reportData.size());
		mmsCronJobData = mmsCronJobHelper.createOrUpdateProcessingCronJob(mmsCronJobData, PARTNER_PRODUCT_PRICE_REPORT_TIMEOUT_MINUTES);
		MmsCronJobStatusEnum mmsCronJobStatusEnum = mmsCronJobData.getMmsCronJobDo().getStatus();

		File tempFile = null;
		try {
			ByteArrayResource byteArrayResource = tooniesSkuTemplateHelper.generateTooniesSkuPriceChangedReport(reportData);
			tempFile = notificationHelper.createTempExcelFile(byteArrayResource.getByteArray(), PRICE_CHANGE_REPORT_FILE_NAME, ConstantType.DATE_FORMAT_YMD);

			String parameterDate = new SimpleDateFormat(TEMPLATE_DAILY_CHARGE_CHANGED_REPORT_DATE_FORMAT).format(new Date());
			List<MessageLanguageDto> messageLanguageList = MessageLanguageDto.generateAllLanguageTemplate(
				TemplateDetailDto.builder()
					.parameterList(List.of(
						new ParameterDto(TEMPLATE_DAILY_CHARGE_CHANGED_REPORT_DATE_FORMAT, parameterDate)
					)).build(), null
			);

			NotificationAttachmentRequest request = notificationHelper.generateSystemNotificationAttachmentRequestForGroupReceiver(
				new FileSystemResource(tempFile),
				NotificationCodeEnum.TOONIES_CHARGE_DAILY_REPORT,
				messageLanguageList,
				false,
				TOONIES_DAILY_CHARGE_CHANGED_REPORT,
				TOONIES_REPORT_RECEIVER
			);
			ResponseDto<NotificationAttachmentResponse> response = notificationHelper.requestSendMessageWithAttachment(request);
			if (response.getStatus() == StatusCodeEnum.SUCCESS.getCode()) {
				log.info("sendMailForPartnerProductPriceChangeDailyReport success");
			} else {
				log.error("sendMailForPartnerProductPriceChangeDailyReport fail");
			}
			mmsCronJobStatusEnum = MmsCronJobStatusEnum.SUCCESS;
		} catch (Exception e) {
			log.error("Error occurred while sendMailForPartnerProductPriceChangeDailyReport: {}", e.getMessage(), e);
			mmsCronJobStatusEnum = MmsCronJobStatusEnum.FAIL;
		} finally {
			// delete temp file
			if (tempFile != null && tempFile.exists()) {
				try {
					Files.delete(tempFile.toPath());
				} catch (IOException e) {
					log.error("Error occurred while deleting temp file: {}", e.getMessage(), e);
				}
			}
			mmsCronJobHelper.updateCronJobStatus(mmsCronJobData, mmsCronJobStatusEnum);
		}
	}

	protected List<PartnerProductPriceChangedReportData> generateReportDate(List<PartnerProductPriceDo> currentDataList, List<PartnerProductPriceHistoryDo> allHistories) {
		List<PartnerProductPriceChangedReportData> reportData = new ArrayList<>();

		// group history data by SKU
		Map<String, List<PartnerProductPriceHistoryDo>> historySkuMap = allHistories.stream()
			.collect(Collectors.groupingBy(PartnerProductPriceHistoryDo::getStoreSkuId));

		for (PartnerProductPriceDo currentData : currentDataList) {
			List<PartnerProductPriceHistoryDo> histories =
				historySkuMap.getOrDefault(currentData.getStoreSkuId(), Collections.emptyList());

			if (histories.isEmpty()) {
				continue;
			}

			// find latest price changed history
			PartnerProductPriceHistoryDo latestDiffHistory = histories.stream()
				.filter(h -> !Objects.equals(h.getChargeConverted(), currentData.getChargeConverted()))
				.max(Comparator.comparing(PartnerProductPriceHistoryDo::getEndDate))
				.orElse(null);

			if (latestDiffHistory == null) {
				continue;
			}
			reportData.add(PartnerProductPriceChangedReportData.generateReportData(currentData, latestDiffHistory));
		}
		return reportData;
	}

	public void preDestroyUpdateProcessingCronJobStatusToFail() {
		mmsCronJobHelper.preDestroyUpdateProcessingCronJobStatusToFail();
	}
}

