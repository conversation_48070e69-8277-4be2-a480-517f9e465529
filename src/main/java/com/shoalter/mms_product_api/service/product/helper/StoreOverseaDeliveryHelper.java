package com.shoalter.mms_product_api.service.product.helper;

import com.shoalter.mms_product_api.dao.repository.store.StoreOverseaDeliveryRepository;
import com.shoalter.mms_product_api.dao.repository.store.pojo.CategoryOverseaRegionDo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Service
@Slf4j
public class StoreOverseaDeliveryHelper {
	private final StoreOverseaDeliveryRepository storeOverseaDeliveryRepository;

	/**
	 * Check if the regions under test are valid, and return the invalid regions
	 */
	public Set<String> validateAndReturnInvalidRegions(Set<String> validRegions, Set<String> regionsUnderTest) {
		Set<String> invalidRegions = new HashSet<>();
		if (validRegions.isEmpty()) {
			invalidRegions.addAll(regionsUnderTest);
		} else {
			for (String region : regionsUnderTest) {
				if (!validRegions.contains(region)) {
					invalidRegions.add(region);
				}
			}
		}

		return invalidRegions;
	}

	public Set<String> getValidRegions(String buCode, Integer storeId, Set<Integer> categoryIds) {
		List<CategoryOverseaRegionDo> categoryRegions = storeOverseaDeliveryRepository.findCategoryOverseaRegionByStoreIdAndCategoryIds(buCode, storeId, new ArrayList<>(categoryIds));

		Map<Integer, Set<String>> categoryIdRegionsMap = groupRegionsByCategory(categoryRegions);

		return findCommonRegions(categoryIdRegionsMap);
	}

	/**
	 * Groups oversea delivery regions by category ID
	 *
	 * @param categoryRegions List of category and region mappings
	 * @return Map of category IDs to their corresponding delivery regions
	 */
	private Map<Integer, Set<String>> groupRegionsByCategory(List<CategoryOverseaRegionDo> categoryRegions) {
		return categoryRegions.stream()
			.collect(Collectors.groupingBy(
				CategoryOverseaRegionDo::getCategoryId,
				Collectors.mapping(CategoryOverseaRegionDo::getRegion, Collectors.toSet())
			));
	}

	/**
	 * Finds the intersection of delivery regions across all categories
	 *
	 * @param categoryRegionsMap Map of category IDs to their delivery regions
	 * @return Set of common regions across all categories
	 */
	private Set<String> findCommonRegions(Map<Integer, Set<String>> categoryRegionsMap) {
		if (categoryRegionsMap.isEmpty()) {
			log.warn("Category regions map is empty, returning an empty set of common regions.");
			return Collections.emptySet();
		}

		Set<String> regionSet = new HashSet<>();
		for (Set<String> regions : categoryRegionsMap.values()) {
			if (regionSet.isEmpty()) {
				regionSet.addAll(regions);
			} else {
				regionSet.retainAll(regions);
			}
		}
		return regionSet;
	}
}
