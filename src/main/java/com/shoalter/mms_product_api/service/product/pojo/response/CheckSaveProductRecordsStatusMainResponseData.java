package com.shoalter.mms_product_api.service.product.pojo.response;

import lombok.Data;

import java.util.List;

@Data
public class CheckSaveProductRecordsStatusMainResponseData {
	private Long recordId;
	private String status;
	private List<CheckSaveProductRecordRowStatusResponseData> rows;

	public CheckSaveProductRecordsStatusMainResponseData(Long recordId, String status, List<CheckSaveProductRecordRowStatusResponseData> rows) {
		this.recordId = recordId;
		this.status = status;
		this.rows = rows;
	}
}
