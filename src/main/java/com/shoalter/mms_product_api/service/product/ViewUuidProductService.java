package com.shoalter.mms_product_api.service.product;

import com.google.gson.Gson;
import com.shoalter.mms_product_api.config.product.ErrorMessageTypeCode;
import com.shoalter.mms_product_api.config.product.SysParmSegment;
import com.shoalter.mms_product_api.dao.repository.brand.BrandRepository;
import com.shoalter.mms_product_api.dao.repository.brand.pojo.BrandDo;
import com.shoalter.mms_product_api.dao.repository.contract.ContractRepository;
import com.shoalter.mms_product_api.dao.repository.store.StoreRepository;
import com.shoalter.mms_product_api.dao.repository.store.StoreWarehouseRepository;
import com.shoalter.mms_product_api.dao.repository.system.SysParmRepository;
import com.shoalter.mms_product_api.dao.repository.system.pojo.SysParmDo;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.product.helper.ProductImageHelper;
import com.shoalter.mms_product_api.service.product.helper.ProductMasterHelper;
import com.shoalter.mms_product_api.service.product.pojo.*;
import com.shoalter.mms_product_api.util.CollectionUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Service
@Slf4j
public class ViewUuidProductService {
	private final ProductMasterHelper productMasterHelper;
	private final ProductImageHelper productImageHelper;
	private final MessageSource messageSource;
	private final BrandRepository brandRepository;
	private final SysParmRepository sysParmRepository;
	private final ContractRepository contractRepository;
	private final StoreRepository storeRepository;
	private final StoreWarehouseRepository storeWarehouseRepository;
	private final Gson gson;
	private final List<String> segmentParentList = List.of(
			SysParmSegment.COUNTRY_OF_ORIGIN, SysParmSegment.COLOR, SysParmSegment.COLOR_FAMILIES,
			SysParmSegment.SIZE, SysParmSegment.SIZE_SYSTEM, SysParmSegment.PRODUCT_FIELD, SysParmSegment.PRODUCT_FIELD_VALUE,
			SysParmSegment.STORAGE_TEMPERATURE, SysParmSegment.PACK_BOX_TYPE, SysParmSegment.PRODUCT_READY_METHOD, SysParmSegment.DELIVERY_METHOD,
			SysParmSegment.CURRENCY, SysParmSegment.PRODUCT_READY_DAYS, SysParmSegment.PICKUP_DAYS,
			SysParmSegment.DELIVERY_COMPLETION_DAYS, SysParmSegment.PICKUP_TIMESLOT, SysParmSegment.VOUCHER_TYPE, SysParmSegment.VOUCHER_DISPLAY_TYPE,
			SysParmSegment.VOUCHER_TEMPLATE_TYPE, SysParmSegment.PAYMENT_TERM, SysParmSegment.PRODUCT_READY_DAYS_DEFAULT
	);

	public ResponseDto<ProductInventoryViewDto> start(UserDto userDto, String uuid) {
		// call product master Get batch products api(Get products by uuids)
		ProductMasterSearchRequestDto productMasterSearchRequestDto = ProductMasterSearchRequestDto.builder().uuids(Collections.singletonList(uuid)).build();
		List<ProductMasterResultDto> productMasterResultList = productMasterHelper.requestProductsByUuid(userDto, productMasterSearchRequestDto);
		if (CollectionUtil.isEmpty(productMasterResultList)) {
			return generateFailResponse("message10", ErrorMessageTypeCode.PRODUCT_MASTER_POST_PRODUCTS_BATCH_PRODUCTS_ERROR);
		}
		ProductMasterResultDto productMasterResultDto = productMasterResultList.get(0);

		if (productMasterResultDto.getAdditional() != null && productMasterResultDto.getAdditional().getHktv() != null) {
			productImageHelper.convertPhotoUrl(productMasterResultDto.getAdditional().getHktv());
		}
		ProductInventoryViewDto product = new ProductInventoryViewDto();
		product.setUuid(uuid);
		product.setProductData(convertProductMasterViewDto(productMasterResultDto));
		return ResponseDto.<ProductInventoryViewDto>builder().data(product).status(1).build();
	}

	private ProductMasterViewDto convertProductMasterViewDto(ProductMasterResultDto productMasterResultDto) {
		ProductMasterViewDto productMasterViewDto = gson.fromJson(gson.toJson(productMasterResultDto), ProductMasterViewDto.class);
		List<SysParmDo> sysParamDoList = sysParmRepository.findBySegments(segmentParentList);
		Map<String, List<SysParmDo>> segmentSysParmDoMap = sysParamDoList.stream().collect(Collectors.groupingBy(SysParmDo::getSegment));
		convertMasterInfo(productMasterResultDto, productMasterViewDto, segmentSysParmDoMap);
		convertHktvInfo(productMasterResultDto, productMasterViewDto, segmentSysParmDoMap);
		return productMasterViewDto;
	}

	private void convertMasterInfo(ProductMasterResultDto productMasterResultDto, ProductMasterViewDto productMasterViewDto, Map<String, List<SysParmDo>> segmentSysParmDoMap) {
		if (productMasterResultDto.getBrandId() != null) {
			BrandDo brandDo = brandRepository.findById(productMasterResultDto.getBrandId()).orElse(null);
			if (brandDo != null) {
				productMasterViewDto.setBrandNameChi(brandDo.getBrandNameTc());
				productMasterViewDto.setBrandNameEn(brandDo.getBrandNameEn());
			}
		}

		productMasterViewDto.setManufacturedCountryDesc(findShortDescBySegmentAndCode(segmentSysParmDoMap, SysParmSegment.COUNTRY_OF_ORIGIN, productMasterResultDto.getManufacturedCountry()));

		productMasterViewDto.setStorageTemperatureDesc(findShortDescBySegmentAndCode(segmentSysParmDoMap,SysParmSegment.STORAGE_TEMPERATURE, productMasterResultDto.getStorageTemperature()));
		productMasterViewDto.setPackingBoxTypeDesc(findShortDescBySegmentAndCode(segmentSysParmDoMap, SysParmSegment.PACK_BOX_TYPE, productMasterResultDto.getPackingBoxType()));

		productMasterViewDto.setColourFamiliesDesc(findShortDescBySegmentAndCode(segmentSysParmDoMap, SysParmSegment.COLOR_FAMILIES, productMasterResultDto.getColourFamilies()));
		productMasterViewDto.setColourDesc(findShortDescBySegmentAndCode(segmentSysParmDoMap, SysParmSegment.COLOR, productMasterResultDto.getColor()));

		productMasterViewDto.setSizeSystemDesc(findShortDescBySegmentAndCode(segmentSysParmDoMap, SysParmSegment.SIZE_SYSTEM, productMasterResultDto.getSizeSystem()));
		productMasterViewDto.setSizeDesc(findShortDescBySegmentAndCode(segmentSysParmDoMap, SysParmSegment.SIZE, productMasterResultDto.getSize()));

		productMasterViewDto.setOption1Desc(findShortDescBySegmentAndCode(segmentSysParmDoMap, SysParmSegment.PRODUCT_FIELD, productMasterResultDto.getOption1()));
		productMasterViewDto.setOption2Desc(findShortDescBySegmentAndCode(segmentSysParmDoMap, SysParmSegment.PRODUCT_FIELD, productMasterResultDto.getOption2()));
		productMasterViewDto.setOption3Desc(findShortDescBySegmentAndCode(segmentSysParmDoMap, SysParmSegment.PRODUCT_FIELD, productMasterResultDto.getOption3()));

		productMasterViewDto.setOption1ValueDesc(findShortDescBySegmentAndCode(segmentSysParmDoMap, SysParmSegment.PRODUCT_FIELD_VALUE, productMasterResultDto.getOption1Value()));
		productMasterViewDto.setOption2ValueDesc(findShortDescBySegmentAndCode(segmentSysParmDoMap, SysParmSegment.PRODUCT_FIELD_VALUE, productMasterResultDto.getOption2Value()));
		productMasterViewDto.setOption3ValueDesc(findShortDescBySegmentAndCode(segmentSysParmDoMap, SysParmSegment.PRODUCT_FIELD_VALUE, productMasterResultDto.getOption3Value()));
	}

	private void convertHktvInfo(ProductMasterResultDto productMasterResultDto, ProductMasterViewDto productMasterViewDto, Map<String, List<SysParmDo>> segmentSysParmDoMap) {
		if (productMasterResultDto.getAdditional().getHktv() != null) {
			storeRepository.findHktvStoreByStoreCode(productMasterResultDto.getAdditional().getHktv().getStores()).ifPresent(storeDo -> productMasterViewDto.getAdditional().getHktv().setStoreName(storeDo.getStoreName()));

			contractRepository.findById(productMasterResultDto.getAdditional().getHktv().getContractNo()).ifPresent(contractDo -> productMasterViewDto.getAdditional().getHktv().setContractName(contractDo.getContractNo()));

			String warehouse = storeWarehouseRepository.findWarehouseId(productMasterResultDto.getAdditional().getHktv().getWarehouseId());
			if (warehouse != null) {
				productMasterViewDto.getAdditional().getHktv().setWarehouse(warehouse);
			}

			productMasterViewDto.getAdditional().getHktv().setProductReadyMethodDesc(findShortDescBySegmentAndCode(segmentSysParmDoMap, SysParmSegment.PRODUCT_READY_METHOD, productMasterResultDto.getAdditional().getHktv().getProductReadyMethod()));
			productMasterViewDto.getAdditional().getHktv().setDeliveryMethodDesc(findShortDescBySegmentAndCode(segmentSysParmDoMap, SysParmSegment.DELIVERY_METHOD, productMasterResultDto.getAdditional().getHktv().getDeliveryMethod()));
			productMasterViewDto.getAdditional().getHktv().setCurrencyDesc(findShortDescBySegmentAndCode(segmentSysParmDoMap, SysParmSegment.CURRENCY, productMasterResultDto.getAdditional().getHktv().getCurrency()));
			productMasterViewDto.getAdditional().getHktv().setPickupDaysDesc(findShortDescBySegmentAndCode(segmentSysParmDoMap, SysParmSegment.PICKUP_DAYS, productMasterResultDto.getAdditional().getHktv().getPickupDays()));
			// 前端此欄位是取Parm Value的值
			productMasterViewDto.getAdditional().getHktv().setProductReadyDaysDesc(findParmValueBySegmentAndCode(segmentSysParmDoMap, SysParmSegment.PRODUCT_READY_DAYS, productMasterResultDto.getAdditional().getHktv().getProductReadyDays()));
		}
	}

	private ResponseDto<ProductInventoryViewDto> generateFailResponse(String message, String errorMessageTypeCode) {
		return ResponseDto.<ProductInventoryViewDto>builder().status(-1).errorMessageList(List.of(messageSource.getMessage(message, new String[]{errorMessageTypeCode}, null))).build();
	}

	private String findShortDescBySegmentAndCode(Map<String, List<SysParmDo>> segmentSysParmDoMap, String segment, String code) {
		if (CollectionUtil.isNotEmpty(segmentSysParmDoMap.get(segment))) {
			return segmentSysParmDoMap.get(segment).stream()
					.filter(row -> Objects.equals(row.getCode(), code))
					.map(SysParmDo::getShortDesc).findFirst().orElse(null);
		}
		return null;
	}

	private String findParmValueBySegmentAndCode(Map<String, List<SysParmDo>> segmentSysParmDoMap, String segment, String code) {
		if (CollectionUtil.isNotEmpty(segmentSysParmDoMap.get(segment))) {
			return segmentSysParmDoMap.get(segment).stream()
					.filter(row -> Objects.equals(row.getCode(), code))
					.map(SysParmDo::getParmValue).findFirst().orElse(null);
		}
		return null;
	}
}
