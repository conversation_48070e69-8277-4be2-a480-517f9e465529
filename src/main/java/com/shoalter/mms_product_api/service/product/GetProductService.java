package com.shoalter.mms_product_api.service.product;

import com.shoalter.mms_product_api.config.type.ConstantType;
import com.shoalter.mms_product_api.dao.repository.api.SaveProductRecordRowRepository;
import com.shoalter.mms_product_api.dao.repository.product.ProductRepository;
import com.shoalter.mms_product_api.mapper.ProductResponseDataMapper;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.product.helper.ProductImageHelper;
import com.shoalter.mms_product_api.service.product.helper.ProductMasterHelper;
import com.shoalter.mms_product_api.service.product.pojo.HktvProductDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductInventoryDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterResultDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterSearchRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductResponseDto;
import com.shoalter.mms_product_api.util.CollectionUtil;
import com.shoalter.mms_product_api.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@RequiredArgsConstructor
@Service
@Slf4j
public class GetProductService {

	private final ProductRepository productRepository;
	private final SaveProductRecordRowRepository saveProductRecordRowRepository;

	private final ProductMasterHelper productMasterHelper;
	private final MessageSource messageSource;
	private final ProductImageHelper productImageHelper;

	public ResponseDto<ProductInventoryDto> start(UserDto userDto, String uuid, Boolean hasEditStatus) {
		ProductMasterSearchRequestDto productMasterSearchRequestDto = ProductMasterSearchRequestDto.builder().uuids(Collections.singletonList(uuid)).build();
		List<ProductMasterResultDto> productMasterResultList = productMasterHelper.requestProductsByUuid(userDto, productMasterSearchRequestDto);
		if (CollectionUtil.isEmpty(productMasterResultList)) {
			return ResponseDto.<ProductInventoryDto>builder().status(-1).errorMessageList(List.of(messageSource.getMessage("message127", null, null))).build();
		}

		ProductMasterResultDto productMasterResultDto = productMasterResultList.get(0);
		processHktvData(uuid, hasEditStatus, productMasterResultDto);

		ProductInventoryDto product = new ProductInventoryDto();
		product.setUuid(uuid);
		ProductResponseDto productResponseDto = ProductResponseDataMapper.INSTANCE.toResponseDto(productMasterResultDto);
		product.setProductData(productResponseDto);
		return ResponseDto.<ProductInventoryDto>builder().data(product).status(1).build();
	}

	private void processHktvData(String uuid, Boolean hasEditStatus, ProductMasterResultDto productMasterResultDto) {
		if (productMasterResultDto.getAdditional() == null || productMasterResultDto.getAdditional().getHktv() == null) {
			return;
		}
		HktvProductDto hktvProductDto = productMasterResultDto.getAdditional().getHktv();
		productImageHelper.convertPhotoUrl(hktvProductDto);
		// MS-2309 add status by approve
		String status = productRepository.findStatusByUuid(uuid);
		hktvProductDto.setStatus(status);

		if (Boolean.TRUE.equals(hasEditStatus)) {
			int editingCount = saveProductRecordRowRepository.countByUuidAndStatus(uuid);
			hktvProductDto.setHasEditing(editingCount > 0);
		}

		if (StringUtil.isNotEmpty(hktvProductDto.getVoucherTemplateType()) && hktvProductDto.getVoucherTemplateType().length() == 1) {
			hktvProductDto.setVoucherTemplateType(ConstantType.VOUCHER_TEMPLATE_TYPE_PREFIX + hktvProductDto.getVoucherTemplateType());
		}
	}
}
