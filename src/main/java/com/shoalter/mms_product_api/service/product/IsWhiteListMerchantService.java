package com.shoalter.mms_product_api.service.product;

import com.shoalter.mms_product_api.config.product.SysParmSegment;
import com.shoalter.mms_product_api.dao.repository.system.SysParmRepository;
import com.shoalter.mms_product_api.dao.repository.system.pojo.SysParmDo;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.util.CollectionUtil;
import com.shoalter.mms_product_api.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Service
@Slf4j
public class IsWhiteListMerchantService {
	private final SysParmRepository sysParmRepository;

	public ResponseDto<Boolean> start(Integer merchantId) {
		boolean isWhiteList = checkAllProcessesIsWhiteList();
		if (!isWhiteList) {
			String merchantIdString = String.valueOf(merchantId);
			Set<String> productManagementWhitelistSet = getWhiteListMerchantIds();
			isWhiteList = productManagementWhitelistSet.contains(merchantIdString);
		}
		return ResponseDto.<Boolean>builder().status(1).data(isWhiteList).build();
	}

	public boolean checkAllProcessesIsWhiteList() {
		boolean isWhiteList = false;
		List<SysParmDo> productManagementSettingList = sysParmRepository.findBySegment(SysParmSegment.PRODUCT_MANAGEMENT_SETTING);
		if (CollectionUtil.isNotEmpty(productManagementSettingList)) {
			SysParmDo productManagementSetting = productManagementSettingList.get(0);
			// If isWhiteList is true, all processes is already using the NEW FLOW.
			isWhiteList = StringUtils.equals("PRODUCT_MASTER", productManagementSetting.getParmValue());
		}
		return isWhiteList;
	}


	public Set<String> getWhiteListMerchantIds() {
		List<SysParmDo> productManagementWhitelistList = sysParmRepository.findBySegment(SysParmSegment.PRODUCT_MANAGEMENT_WHITELIST);
		if (CollectionUtil.isEmpty(productManagementWhitelistList)) {
			return new HashSet<>();
		}
		return productManagementWhitelistList.stream()
			.map(SysParmDo::getParmValue)
			.filter(StringUtils::isNotBlank)
			.flatMap(paramValue -> Arrays.stream(paramValue.split(StringUtil.COMMA)))
			.collect(Collectors.toSet());
	}
}
