package com.shoalter.mms_product_api.service.product;

import com.shoalter.mms_product_api.config.product.BuCodeEnum;
import com.shoalter.mms_product_api.config.product.ErrorMessageTypeCode;
import com.shoalter.mms_product_api.config.product.SaveProductStatus;
import com.shoalter.mms_product_api.config.product.SaveProductStatusEnum;
import com.shoalter.mms_product_api.config.product.SaveProductType;
import com.shoalter.mms_product_api.config.product.SaveProductTypeEnum;
import com.shoalter.mms_product_api.config.type.ConstantType;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordDo;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordRowDo;
import com.shoalter.mms_product_api.dao.repository.product.pojo.StoreMerchantViewDo;
import com.shoalter.mms_product_api.dao.repository.store.StoreRepository;
import com.shoalter.mms_product_api.exception.NoDataException;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.product.helper.ProductMasterHelper;
import com.shoalter.mms_product_api.service.product.helper.SaveProductRecordHelper;
import com.shoalter.mms_product_api.service.product.helper.SaveProductRecordRowHelper;
import com.shoalter.mms_product_api.service.product.pojo.BuProductDto;
import com.shoalter.mms_product_api.service.product.pojo.LittleMallFlattenProductsRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.LittleMallProductDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterBaseResponseDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductSearchLittleMallFalttenSkuRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.SingleEditProductDto;
import com.shoalter.mms_product_api.service.product.pojo.response.LittleMallFlattenSkuDetailResponse;
import com.shoalter.mms_product_api.service.product.pojo.response.LittleMallFlattenSkuResponse;
import com.shoalter.mms_product_api.util.CollectionUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class LittleMallFlattenProductsService {
	private final SaveProductRecordHelper saveProductRecordHelper;
	private final SaveProductRecordRowHelper saveProductRecordRowHelper;
	private final StoreRepository storeRepository;
	private final ProductMasterHelper productMasterHelper;
	private final MessageSource messageSource;

	@Transactional
	public ResponseDto<Void> start(UserDto userDto, LittleMallFlattenProductsRequestDto littleMallFlattenProductsRequestDto) {
		if (CollectionUtil.isEmpty(littleMallFlattenProductsRequestDto.getStorefrontStoreCodes())) {
			throw new NoDataException();
		}

		// check store existence
		List<StoreMerchantViewDo> stores = storeRepository.findStoreMerchantViewDoByBuCodeAndStorefrontStoreCodes(BuCodeEnum.LITTLE_MALL.name(), littleMallFlattenProductsRequestDto.getStorefrontStoreCodes());
		Set<String> existStores = stores.stream().map(StoreMerchantViewDo::getStorefrontStoreCode).collect(Collectors.toSet());
		List<String> nonExistStores = littleMallFlattenProductsRequestDto.getStorefrontStoreCodes().stream()
			.filter(storefrontStoreCode -> !existStores.contains(storefrontStoreCode))
			.collect(Collectors.toList());
		if (!nonExistStores.isEmpty()) {
			return ResponseDto.fail(List.of(messageSource.getMessage("message184", new String[]{nonExistStores.toString()}, null)));
		}

		//call product master
		ProductMasterBaseResponseDto<List<LittleMallFlattenSkuResponse>> productMasterResult = productMasterHelper.requestSearchLittleMallFlattenSkusByStores(userDto, new ProductSearchLittleMallFalttenSkuRequestDto(littleMallFlattenProductsRequestDto.getStorefrontStoreCodes()));
		if (productMasterResult == null) {
			return ResponseDto.fail(List.of(messageSource.getMessage("message10", new String[]{ErrorMessageTypeCode.PRODUCT_MASTER_POST_PRODUCTS_SEARCH_ERROR}, null)));
		}
		if (CollectionUtil.isEmpty(productMasterResult.getData())) {
			return ResponseDto.fail(List.of(messageSource.getMessage("message329", null, null)));
		}

		//save record
		Map<String, List<LittleMallFlattenSkuDetailResponse>> skuMap = productMasterResult.getData().stream()
			.collect(Collectors.toMap(LittleMallFlattenSkuResponse::getStorefrontStoreCode, LittleMallFlattenSkuResponse::getProducts));
		for (StoreMerchantViewDo storeMerchantViewDo : stores) {
			if (!skuMap.containsKey(storeMerchantViewDo.getStorefrontStoreCode())) {
				log.info("no sku to flatten in store {}, skip process.", storeMerchantViewDo.getStorefrontStoreCode());
				continue;
			}

			int fileIndex = 0;
			//create record : 1 record per 10000 rows in 1 store
			List<LittleMallFlattenSkuDetailResponse> littleMallSkus = skuMap.get(storeMerchantViewDo.getStorefrontStoreCode());
			for (List<LittleMallFlattenSkuDetailResponse> partitionLittleMallSkus : ListUtils.partition(littleMallSkus, ConstantType.PRODUCT_UPLOAD_MAX_SIZE)) {
				fileIndex++;
				String fileName = String.format("flatten_%s_%d_%d", storeMerchantViewDo.getStorefrontStoreCode(), fileIndex, System.currentTimeMillis());
				SaveProductRecordDo saveProductRecordDo = saveProductRecordHelper.createSaveProductRecord(userDto.getUserId(), storeMerchantViewDo.getMerchantId(),
					SaveProductType.LITTLE_MALL_FLATTEN_PRODUCTS, fileName, SaveProductStatus.REQUESTING_PM);

				List<SaveProductRecordRowDo> recordRowDoList = new ArrayList<>();
				for (LittleMallFlattenSkuDetailResponse littleMallFlattenSkuResponse : partitionLittleMallSkus) {
					SingleEditProductDto singleEditProductDto = generateSingleEditProductDto(storeMerchantViewDo.getStorefrontStoreCode(), littleMallFlattenSkuResponse);
					SaveProductRecordRowDo saveProductRecordRowDo = saveProductRecordRowHelper
						.generateProductRecordRowDo(saveProductRecordDo.getId(), singleEditProductDto, SaveProductStatus.REQUESTING_PM, null);
					recordRowDoList.add(saveProductRecordRowDo);
				}
				saveProductRecordRowHelper.batchSaveSaveProductRecordRowDo(recordRowDoList);
				log.info("create record id: {}, save product type: {}, row size: {}, upload user id: {}, status: {}", saveProductRecordDo.getId(), SaveProductTypeEnum.getProductTypeName(saveProductRecordDo.getUploadType()), recordRowDoList.size(), saveProductRecordDo.getUploadUserId(), SaveProductStatusEnum.getProductStatusName(saveProductRecordDo.getStatus()));
			}

		}

		return ResponseDto.success(null);
	}

	private SingleEditProductDto generateSingleEditProductDto(String storeCode, LittleMallFlattenSkuDetailResponse littleMallFlattenSkuDetailResponse) {
		LittleMallProductDto littleMallProductDto = new LittleMallProductDto();
		littleMallProductDto.setStoreCode(storeCode);
		littleMallProductDto.setIsPrimarySku(littleMallFlattenSkuDetailResponse.getIsPrimarySku());
		BuProductDto buProductDto = new BuProductDto();
		buProductDto.setLittleMall(littleMallProductDto);

		ProductMasterDto productMasterDto = new ProductMasterDto();
		productMasterDto.setUuid(littleMallFlattenSkuDetailResponse.getUuid());
		productMasterDto.setProductId(littleMallFlattenSkuDetailResponse.getProductId());
		productMasterDto.setSkuId(littleMallFlattenSkuDetailResponse.getSkuId());
		productMasterDto.setMerchantId(littleMallFlattenSkuDetailResponse.getMerchantId());
		productMasterDto.setAdditional(buProductDto);

		SingleEditProductDto singleEditProductDto = new SingleEditProductDto();
		singleEditProductDto.setProduct(productMasterDto);
		return singleEditProductDto;
	}
}
