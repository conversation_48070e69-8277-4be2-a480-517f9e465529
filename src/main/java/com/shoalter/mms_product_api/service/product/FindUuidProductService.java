package com.shoalter.mms_product_api.service.product;

import com.shoalter.mms_product_api.config.product.ErrorMessageTypeCode;
import com.shoalter.mms_product_api.dao.repository.product.ProductRepository;
import com.shoalter.mms_product_api.exception.SystemI18nException;
import com.shoalter.mms_product_api.mapper.ProductResponseDataMapper;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.product.helper.ProductImageHelper;
import com.shoalter.mms_product_api.service.product.helper.ProductMasterHelper;
import com.shoalter.mms_product_api.service.product.pojo.HktvProductDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductInventoryDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterResultDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterSearchRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductResponseDto;
import com.shoalter.mms_product_api.util.CollectionUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@RequiredArgsConstructor
@Service
@Slf4j
public class FindUuidProductService {

    private final ProductRepository productRepository;

    private final ProductMasterHelper productMasterHelper;
    private final ProductImageHelper productImageHelper;

    /**
     * call product master Get batch products api(Get product by uuid)
     * 此處應該與GetProductService 邏輯相同，如果有動到邏輯需確認兩邊是否一致
     */
    public ResponseDto<ProductInventoryDto> start(UserDto userDto, String uuid) {
        ProductMasterResultDto productMasterResultDto = getProductByProductMaster(userDto, uuid);
        convertHktvData(uuid, productMasterResultDto);

        return generateResponse(uuid, productMasterResultDto);
    }

    private ProductMasterResultDto getProductByProductMaster(UserDto userDto, String uuid) {
        ProductMasterSearchRequestDto productMasterSearchRequestDto = ProductMasterSearchRequestDto.builder().uuids(Collections.singletonList(uuid)).build();
        List<ProductMasterResultDto> productMasterResultList = productMasterHelper.requestProductsByUuid(userDto, productMasterSearchRequestDto);
        if (CollectionUtil.isEmpty(productMasterResultList)) {
            throw new SystemI18nException("message10", ErrorMessageTypeCode.PRODUCT_MASTER_POST_PRODUCTS_BATCH_PRODUCTS_ERROR);
        }
        return productMasterResultList.get(0);
    }

    private void convertHktvData(String uuid, ProductMasterResultDto productMasterResultDto) {
        if (productMasterResultDto.getAdditional() != null && productMasterResultDto.getAdditional().getHktv() != null) {
            HktvProductDto hktvProductDto = productMasterResultDto.getAdditional().getHktv();
			productImageHelper.convertPhotoUrl(hktvProductDto);
            // MS-2309 add status by approve
            String status = productRepository.findStatusByUuid(uuid);
            hktvProductDto.setStatus(status);
        }
    }

    private ResponseDto<ProductInventoryDto> generateResponse(String uuid, ProductMasterResultDto productMasterResultDto) {
        ProductInventoryDto product = new ProductInventoryDto();
        product.setUuid(uuid);
		ProductResponseDto productResponseDto = ProductResponseDataMapper.INSTANCE.toResponseDto(productMasterResultDto);
        product.setProductData(productResponseDto);
        return ResponseDto.<ProductInventoryDto>builder().data(product).status(1).build();
    }
}
