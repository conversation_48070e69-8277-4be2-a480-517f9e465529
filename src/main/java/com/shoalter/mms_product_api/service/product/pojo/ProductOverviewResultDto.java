package com.shoalter.mms_product_api.service.product.pojo;

import lombok.Data;

import java.util.List;

@Data
public class ProductOverviewResultDto {
    private List<ProductMasterResultDto> content;
    private Integer totalPages;
    private Integer totalElements;
    private Boolean last;
    private Integer size;
    private Integer number;
    private Integer numberOfElements;
    private Boolean first;
    private Boolean empty;
    private ProductOverviewPageableResultDto pageable;
    private ProductOverviewSortResultDto sort;
}
