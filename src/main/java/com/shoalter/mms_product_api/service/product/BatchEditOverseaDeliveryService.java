package com.shoalter.mms_product_api.service.product;

import com.google.gson.Gson;
import com.shoalter.mms_product_api.config.product.SaveProductStatus;
import com.shoalter.mms_product_api.config.product.SaveProductStatusEnum;
import com.shoalter.mms_product_api.config.product.SaveProductType;
import com.shoalter.mms_product_api.config.product.SaveProductTypeEnum;
import com.shoalter.mms_product_api.config.type.ConstantType;
import com.shoalter.mms_product_api.config.user.RoleCode;
import com.shoalter.mms_product_api.dao.mapper.product.ProductStoreStatusMapper;
import com.shoalter.mms_product_api.dao.mapper.store.MerchantStoreMapper;
import com.shoalter.mms_product_api.dao.mapper.store.pojo.MerchantMapStorefrontStoreCodeDto;
import com.shoalter.mms_product_api.dao.repository.api.SaveProductRecordGroupRepository;
import com.shoalter.mms_product_api.dao.repository.api.SaveProductRecordRepository;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordDo;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordGroupDo;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordRowDo;
import com.shoalter.mms_product_api.exception.BadRequestException;
import com.shoalter.mms_product_api.exception.SystemI18nException;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.product.helper.SaveProductRecordRowHelper;
import com.shoalter.mms_product_api.service.product.pojo.BatchEditOverseaDeliveryRequestDetailDto;
import com.shoalter.mms_product_api.service.product.pojo.BatchEditOverseaDeliveryRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.SkuMapUuidDto;
import com.shoalter.mms_product_api.util.CollectionUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class BatchEditOverseaDeliveryService {

    private final SaveProductRecordGroupRepository saveProductRecordGroupRepository;
    private final SaveProductRecordRepository saveProductRecordRepository;

    private final MerchantStoreMapper merchantStoreMapper;
    private final ProductStoreStatusMapper productStoreStatusMapper;


    private final SaveProductRecordRowHelper saveProductRecordRowHelper;

    private final MessageSource messageSource;
    private final Gson gson;

    @Transactional
    public ResponseDto<Long> start(UserDto userDto, BatchEditOverseaDeliveryRequestDto batchEditOverseaDeliveryRequestDto) {
        checkRequest(userDto, batchEditOverseaDeliveryRequestDto);

        SaveProductRecordGroupDo saveProductRecordGroupDo = createSaveProductRecordGroupDo(userDto, batchEditOverseaDeliveryRequestDto.getFileName());
        Map<String, Long> storeMapRecord = createSaveProductRecord(userDto, batchEditOverseaDeliveryRequestDto.getFileName(), saveProductRecordGroupDo, batchEditOverseaDeliveryRequestDto);

        createSaveProductRecordRow(storeMapRecord, batchEditOverseaDeliveryRequestDto);

		batchEditOverseaDeliveryRequestDto.getBatchEditOverseaDeliveryRequestDetailDtoList()
			.stream().collect(Collectors.groupingBy(BatchEditOverseaDeliveryRequestDetailDto::getStoreId)).forEach((storeId, subList) -> {
				log.info("create record id: {}, save product type: {}, row size: {}, upload user id: {}, status: {}", storeMapRecord.get(storeId), SaveProductTypeEnum.getProductTypeName(SaveProductType.BATCH_EDIT_OVERSEA_DELIVERY), subList.size(), userDto.getUserId(), SaveProductStatusEnum.getProductStatusName(SaveProductStatus.CHECKING_PRODUCT));
			});
        return ResponseDto.<Long>builder().data(saveProductRecordGroupDo.getId()).status(1).build();
    }

    private void checkRequest(UserDto userDto, BatchEditOverseaDeliveryRequestDto batchEditOverseaDeliveryRequestDto) {
        checkUserRole(userDto);
        checkRequestCount(batchEditOverseaDeliveryRequestDto);
    }

    private void checkUserRole(UserDto userDto) {
        if (RoleCode.ALLOW_OVERSEA_DELIVERY_ROLES.contains(userDto.getRoleCode())) {
            return;
        }
        throw new SystemI18nException("message131");
    }

    private void checkRequestCount(BatchEditOverseaDeliveryRequestDto batchEditOverseaDeliveryRequestDto) {
        if (CollectionUtil.isEmpty(batchEditOverseaDeliveryRequestDto.getBatchEditOverseaDeliveryRequestDetailDtoList())) {
            throw new BadRequestException(messageSource.getMessage("message135", null, null));
        }

        if (batchEditOverseaDeliveryRequestDto.getBatchEditOverseaDeliveryRequestDetailDtoList().size() > 10000) {
            throw new BadRequestException(messageSource.getMessage("message136", null, null));
        }
    }

    private SaveProductRecordGroupDo createSaveProductRecordGroupDo(UserDto userDto, String fileName) {
        SaveProductRecordGroupDo saveProductRecordGroupDo = new SaveProductRecordGroupDo();
        saveProductRecordGroupDo.setStatus(SaveProductStatus.CHECKING_PRODUCT);
        saveProductRecordGroupDo.setUploadType(SaveProductType.BATCH_EDIT_OVERSEA_DELIVERY);
        saveProductRecordGroupDo.setFileName(fileName);
        saveProductRecordGroupDo.setUploadUserId(userDto.getUserId());
        saveProductRecordGroupDo.setCreateTime(new Date());
        return saveProductRecordGroupRepository.save(saveProductRecordGroupDo);
    }

    private Map<String, Long> createSaveProductRecord(UserDto userDto, String fileName, SaveProductRecordGroupDo saveProductRecordGroupDo, BatchEditOverseaDeliveryRequestDto batchEditOverseaDeliveryRequestDto) {
        Map<String, Integer> storeMapMerchant = generateStoreMappingMerchant(batchEditOverseaDeliveryRequestDto);

        List<Integer> merchantIdList = new ArrayList<>(new HashSet<>(storeMapMerchant.values()));
        List<SaveProductRecordDo> saveProductRecordDoList = merchantIdList
                .stream()
                .map(merchantId -> {
                    SaveProductRecordDo saveProductRecordDo = new SaveProductRecordDo();
                    saveProductRecordDo.setMerchantId(merchantId);
                    saveProductRecordDo.setUploadType(SaveProductType.BATCH_EDIT_OVERSEA_DELIVERY);
                    saveProductRecordDo.setFileName(fileName);
                    saveProductRecordDo.setStatus(SaveProductStatus.WAIT_START);
                    saveProductRecordDo.setUploadUserId(userDto.getUserId());
                    saveProductRecordDo.setUploadTime(new Date());
                    saveProductRecordDo.setGroupId(saveProductRecordGroupDo.getId());
                    return saveProductRecordRepository.save(saveProductRecordDo);
                }).collect(Collectors.toList());
        return storeMapMerchant.keySet().stream().collect(Collectors.toMap(
                key -> key,
                key -> saveProductRecordDoList.stream()
                        .filter(dto -> storeMapMerchant.get(key).equals(dto.getMerchantId()))
                        .mapToLong(SaveProductRecordDo::getId)
                        .sum()
        ));
    }

    private void createSaveProductRecordRow(Map<String, Long> storeMapRecord, BatchEditOverseaDeliveryRequestDto batchEditOverseaDeliveryRequestDto) {
        Map<String, List<BatchEditOverseaDeliveryRequestDetailDto>> storeCodeRequestDtoListMap =
                batchEditOverseaDeliveryRequestDto.getBatchEditOverseaDeliveryRequestDetailDtoList()
                        .stream().collect(Collectors.groupingBy(BatchEditOverseaDeliveryRequestDetailDto::getStoreId));

        List<SaveProductRecordRowDo> saveProductRecordRowDoList = new ArrayList<>();
        storeCodeRequestDtoListMap.forEach((storefrontStoreCode, subList) -> {
            Map<String, String> skuMapUuid = findUuid(storefrontStoreCode, subList);

            subList.forEach(request -> {
                Long recordId = storeMapRecord.get(request.getStoreId());
                String sku = request.getSkuId();

                SaveProductRecordRowDo saveProductRecordRowDo = new SaveProductRecordRowDo();
                saveProductRecordRowDo.setRecordId(recordId);
                saveProductRecordRowDo.setSku(sku);
                saveProductRecordRowDo.setUuid(skuMapUuid.get(sku));
                saveProductRecordRowDo.setStatus(SaveProductStatus.CHECKING_PRODUCT);
                saveProductRecordRowDo.setContent(gson.toJson(request));

                saveProductRecordRowDoList.add(saveProductRecordRowDo);
            });
        });

        saveProductRecordRowHelper.batchSaveSaveProductRecordRowDo(saveProductRecordRowDoList);
        updateRecordStatusToProcessing(new ArrayList<>(storeMapRecord.values()));
    }

    private Map<String, String> findUuid(String storefrontStoreCode, List<BatchEditOverseaDeliveryRequestDetailDto> subList) {
        List<String> storeSkuList = subList
                .stream()
                .map(request -> String.format("%s_S_%s", storefrontStoreCode, request.getSkuId()))
                .collect(Collectors.toList());
        List<SkuMapUuidDto> skuMapUuidList = productStoreStatusMapper.findUuidByStoreSkuList(storeSkuList);
        return skuMapUuidList.stream().collect(Collectors.toMap(SkuMapUuidDto::getSkuCode, SkuMapUuidDto::getUuid));
    }

    private void updateRecordStatusToProcessing(List<Long> recordIdList) {
        saveProductRecordRepository.updateStatusByRecordIdList(recordIdList, SaveProductStatus.PROCESSING);
    }

    private Map<String, Integer> generateStoreMappingMerchant(BatchEditOverseaDeliveryRequestDto batchEditOverseaDeliveryRequestDto) {
        Set<String> storefrontStoreCodeSet = batchEditOverseaDeliveryRequestDto.getBatchEditOverseaDeliveryRequestDetailDtoList()
                .stream()
                .map(BatchEditOverseaDeliveryRequestDetailDto::getStoreId)
                .collect(Collectors.toSet());
        List<MerchantMapStorefrontStoreCodeDto> merchantMapStorefrontStoreCodeDtoList =
                merchantStoreMapper.merchantMapStorefrontStoreCode(new ArrayList<>(storefrontStoreCodeSet));
        Map<String, Integer> storeMappingMerchant = merchantMapStorefrontStoreCodeDtoList
                .stream()
                .collect(Collectors.toMap(MerchantMapStorefrontStoreCodeDto::getStorefrontStoreCode, MerchantMapStorefrontStoreCodeDto::getMerchantId));
        if (storefrontStoreCodeSet.size() > storeMappingMerchant.size()) {
            storefrontStoreCodeSet.forEach(storefrontStoreCode -> storeMappingMerchant.putIfAbsent(storefrontStoreCode, ConstantType.NON_EXISTENT_MERCHANT_ID));
        }
        return storeMappingMerchant;
    }
}
