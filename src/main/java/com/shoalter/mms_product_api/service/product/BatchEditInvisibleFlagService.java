package com.shoalter.mms_product_api.service.product;

import static com.shoalter.mms_product_api.config.type.ConstantType.INVISIBLE_FLAG_N;
import static com.shoalter.mms_product_api.config.type.ConstantType.INVISIBLE_FLAG_Y;

import com.google.gson.Gson;
import com.shoalter.mms_product_api.config.asyncTask.MdcAwareExecutor;
import com.shoalter.mms_product_api.config.product.ProductMasterStatusEnum;
import com.shoalter.mms_product_api.config.properties.AsyncTaskProperties;
import com.shoalter.mms_product_api.service.product.helper.ProductMasterHelper;
import com.shoalter.mms_product_api.service.product.helper.SaveProductHelper;
import com.shoalter.mms_product_api.service.product.pojo.EditInvisibleRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterBaseResponseDto;
import com.shoalter.mms_product_api.service.product.pojo.productmaster.request.ProductMasterUpdateVisibilityRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.productmaster.response.ProductMasterSearchVisibilityResponseDto;
import com.shoalter.mms_product_api.service.product.pojo.productmaster.response.ProductMasterUpdateVisibilityResponseDto;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

@Service
@Slf4j
@RequiredArgsConstructor
public class BatchEditInvisibleFlagService {

	private final SaveProductHelper saveProductHelper;
	private final ProductMasterHelper productMasterHelper;
	private final AsyncTaskProperties asyncTaskProperties;
	private final Gson gson;

	@Value("${product.master.size.update.visibility}")
	private Integer updateInvisiblePartitionSize;

	@Async("ecomEngineSyncExecutor")
	public void start(EditInvisibleRequestDto request) {

		log.info("BatchEditInvisibleFlagService start, start time: {}", LocalDateTime.now());

		StopWatch stopWatch = new StopWatch();
		stopWatch.start();

		ProductMasterBaseResponseDto<List<ProductMasterSearchVisibilityResponseDto>> productMasterResponse =
				productMasterHelper.requestUuidsByStorefrontStoreCodeAndProductIds(request.getProducts());

		if (productMasterResponse == null || productMasterResponse.getData() == null) {
			log.error("ProductMaster uuids returned null for products: {}",
					gson.toJson(request.getProducts()));
			return;
		}

		List<ProductMasterSearchVisibilityResponseDto> responseData = productMasterResponse.getData();

		List<List<ProductMasterSearchVisibilityResponseDto>> partition =
				ListUtils.partition(responseData, updateInvisiblePartitionSize);

		log.info("Update invisible flag partition size: {}, total sku count: {}",
				partition.size(),
				responseData.size());

		try (MdcAwareExecutor executor = MdcAwareExecutor.ofFixedThreadPool(asyncTaskProperties.getEcomEngine().getUpdateInvisibleFixedThreadPool())) {

			List<CompletableFuture<List<ProductMasterUpdateVisibilityResponseDto>>> futures = new ArrayList<>();

			for (int i = 0; i < partition.size(); i++) {
				int partIndex = i + 1;
				List<ProductMasterSearchVisibilityResponseDto> batch = partition.get(i);
				futures.add(
					CompletableFuture.supplyAsync(
						() ->
							updateInvisible(batch, request.getInvisible(), Pair.of(partIndex, partition.size())),
						executor)
				);
			}

			CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

			List<ProductMasterUpdateVisibilityResponseDto> errorResponses = futures.stream()
				.flatMap(future -> {
					try {
						return future.get().stream();
					} catch (Exception e) {
						log.error("Error occurred during async processing", e);
						return Stream.empty();
					}
				})
				.collect(Collectors.toList());

			if (CollectionUtils.isNotEmpty(errorResponses)) {
				// TODO Implement notification function
				errorResponses.forEach(
					response -> log.warn("Failed to update invisible flag: {}", gson.toJson(response)));
			}
		} catch (Exception e) {
			log.error("BatchEditInvisibleFlagService error, stackTrace: {}",
				ExceptionUtils.getStackTrace(e));
		}

		stopWatch.stop();
		log.info("BatchEditInvisibleFlagService end, total milliseconds: {}",
			stopWatch.getTotalTimeMillis());
	}

	protected List<ProductMasterUpdateVisibilityResponseDto> updateInvisible(
		List<ProductMasterSearchVisibilityResponseDto> responseDtoList, boolean invisible,
		Pair<Integer, Integer> partitionIndexPair) {

		log.info("Start update invisible, part: {}/{}", partitionIndexPair.getLeft(),
			partitionIndexPair.getRight());

		if (CollectionUtils.isEmpty(responseDtoList)) {
			return new ArrayList<>();
		}

		String visibilityFlag = invisible ? INVISIBLE_FLAG_N : INVISIBLE_FLAG_Y;

		List<String> uuidsFromProductMaster = responseDtoList.stream()
			.map(ProductMasterSearchVisibilityResponseDto::getUuid).collect(Collectors.toList());

		ProductMasterUpdateVisibilityRequestDto updateVisibilityRequest =
			ProductMasterUpdateVisibilityRequestDto.builder()
				.visibility(visibilityFlag)
				.uuids(uuidsFromProductMaster)
				.build();

		ProductMasterBaseResponseDto<List<ProductMasterUpdateVisibilityResponseDto>> updateVisibilityResponse =
			productMasterHelper.requestUpdateVisibility(updateVisibilityRequest);

		if (updateVisibilityResponse == null) {
			log.error("ProductMaster visibility update returned null for responseDtoList: {}",
				responseDtoList);
			return responseDtoList.stream()
				.map(response ->
					ProductMasterUpdateVisibilityResponseDto.builder()
						.uuid(response.getUuid())
						.storeSkuId(response.getStoreSkuId())
						.message("ProductMaster visibility update returned null.")
						.build())
				.collect(Collectors.toList());
		}

		if (ProductMasterStatusEnum.FAIL.name().equals(updateVisibilityResponse.getStatus())) {
			return responseDtoList.stream()
				.map(response ->
					ProductMasterUpdateVisibilityResponseDto.builder()
						.uuid(response.getUuid())
						.storeSkuId(response.getStoreSkuId())
						.message(updateVisibilityResponse.getMessage())
						.build())
				.collect(Collectors.toList());
		}

		List<ProductMasterUpdateVisibilityResponseDto> failedResult = new ArrayList<>();
		List<ProductMasterUpdateVisibilityResponseDto> productMasterUpdateFailedData =
			updateVisibilityResponse.getData();

		List<String> uuidsToUpdate = new ArrayList<>(uuidsFromProductMaster);

		if (CollectionUtils.isNotEmpty(productMasterUpdateFailedData)) {
			failedResult.addAll(productMasterUpdateFailedData);
			List<String> failedUuids = productMasterUpdateFailedData
				.stream()
				.map(ProductMasterUpdateVisibilityResponseDto::getUuid)
				.collect(Collectors.toList());
			uuidsToUpdate.removeAll(failedUuids);
		}

		uuidsToUpdate.forEach(uuid -> {
			String failedReason = saveProductHelper.updateInvisible(uuid, invisible);
			if (failedReason != null) {
				failedResult.add(ProductMasterUpdateVisibilityResponseDto.builder()
					.uuid(uuid)
					.message(failedReason)
					.build());
			}
		});

		return failedResult;
	}
}
