package com.shoalter.mms_product_api.service.product.pojo;

import com.shoalter.mms_product_api.config.product.BuCodeEnum;
import com.shoalter.mms_product_api.config.product.OnlineStatusEnum;
import com.shoalter.mms_product_api.dao.repository.business.pojo.BuProductCategoryDo;
import com.shoalter.mms_product_api.dao.repository.store.pojo.StoreDo;
import com.shoalter.mms_product_api.service.bundle.pojo.BundleChildSkuDto;
import com.shoalter.mms_product_api.service.product.MembershipPricingEventSetDto;
import lombok.Builder;
import lombok.Data;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.math.BigDecimal;
import java.util.List;

@Data
@Builder
public class ProductCheckDto {
	private StoreDo store;
	private String manufactureCountry;
	private String primaryCategoryCode;
	private BuProductCategoryDo primaryCategory;
	private String isPrimarySku;
	private String buCode;
	private boolean isNewProduct;
	private Integer merchantId;
	private String insuranceContractProdTermName;
	private Integer contractId;
	private Integer warehouseId;
	private String warehouseCode;
	private String readyMethodCode;
	private String deliveryMethodCode;
	private String pickupTimeslot;
	private String skuId;
	private String productId;
	private List<String> productTypeCodeList;
	private Integer brandId;
	private String packingBoxTypeCode;
	private BigDecimal mallDollar;
	private BigDecimal vipMallDollar;
	private String videoLink;
	private String videoLinkTextEn;
	private String videoLinkTextCh;
	private String videoLinkTextSc;
	private String videoLink2;
	private String videoLinkTextEn2;
	private String videoLinkTextCh2;
	private String videoLinkTextSc2;
	private String videoLink3;
	private String videoLinkTextEn3;
	private String videoLinkTextCh3;
	private String videoLinkTextSc3;
	private String videoLink4;
	private String videoLinkTextEn4;
	private String videoLinkTextCh4;
	private String videoLinkTextSc4;
	private String videoLink5;
	private String videoLinkTextEn5;
	private String videoLinkTextCh5;
	private String videoLinkTextSc5;
	//consignment parameter
	private String urgent;
	private String beforeUrgent;
	private BigDecimal weight;
	private String weightUnit;
	private BigDecimal height;
	private BigDecimal depth;
	private BigDecimal length;
	private String skuSDescHktvEn;
	private String skuSDescHktvCh;
	private String skuSDescHktvSc;
	private String skuLDescHktvEn;
	private String skuLDescHktvCh;
	private String skuLDescHktvSc;
	private String storageType;
	private String beforeStorageType;
	private Integer minimumShelfLife;
	private String rmCode;
	private String beforeRmcode;
	private String preSellFruit;
	private String physicalStore;
	private String storageTemperature;
	private String field1;
	private String value1;
	private String field2;
	private String value2;
	private String field3;
	private String value3;
	private String sizeSystem;
	private String size;
	private String colorFamiliar;
	private String colorEn;
	private String pickupDays;
	private String mainPhoto;
	private String advertisePhoto;
	private List<String> otherPhoto;
	private List<String> variantProductPhoto;
	private BigDecimal cost;
	private BigDecimal originalPrice;
	private BigDecimal sellingPrice;
	private String currency;
	private List<ProductBarcodeDto> barcodeDtoList;
	private String removalService;
	private String goodsType;
	private List<String> deliveryDistrictList;
	private String virtualStore;
	private Long userMax;
	private String productReadyDays;
	private Integer returnDays;
	private OnlineStatusEnum onlineStatus;
	private String packingDimensionUnit;
	private boolean isOfflineDueToRollback;
	private String skuNameEn;
	private String skuNameCh;
	private String skuNameSc;
	private List<BundleChildSkuDto> childSkuInfo;
	private String affiliateUrl;
	private String discountTextEn;
	private String discountTextCh;
	private String discountTextSc;
	private String packingSpecEn;
	private String packingSpecCh;
	private String packingSpecSc;
	private List<CartonSizeDto> cartonSizeDtoList;
	private String visibility;
	private String redeemStartDate;
	private String fixRedemptionDate;
	private String featureStartDate;
	private String featureEndDate;
	private Integer uponPurchaseDate;
	private String voucherType;
	private String voucherDisplayType;
	private String voucherTemplateType;
	private String expiryType;
	private String finePrintEn;
	private String finePrintCh;
	private String finePrintSc;
	private String warrantyPeriodUnit;
	private Integer warrantyPeriod;
	private String warrantySupplierEn;
	private String warrantySupplierCh;
	private String warrantySupplierSc;
	private String warrantyRemarkEn;
	private String warrantyRemarkCh;
	private String warrantyRemarkSc;
	private String serviceCentreAddressEn;
	private String serviceCentreAddressCh;
	private String serviceCentreAddressSc;
	private String serviceCentreEmail;
	private String serviceCentreContact;
	private String invoiceRemarksEn;
	private String invoiceRemarksCh;
	private String invoiceRemarksSc;
	private String style;
	private CheckProductResultDto check3PlResult;
	private BigDecimal ewPercentageSetting;
	private String claimLinkEn;
	private String claimLinkCh;
	private String claimLinkSc;
	private Long beforeUserMax;
	private ProductPartnerInfoDto partnerInfo;
	private ExternalPlatform externalPlatform;
	private MembershipPricingEventSetDto checkPricingResult;
	private boolean isSkuOfflineOrWillBeOffline;


	/**
	 * If product is offline or will be offline, use this method to check and ignore checking which is not necessary when product is offline
	 */
	public static boolean isSkuOfflineOrWillBeOffline(@NonNull ProductMasterDto productMasterDto, @Nullable HktvProductDto beforeProductHktv) {
		if (beforeProductHktv == null) {
			// new product
			return OnlineStatusEnum.OFFLINE.equals(productMasterDto.getAdditional().getHktv().getOnlineStatus());
		} else {
			// edit exist product
			// if online status is updated, check if it is offline, else check if it was offline
			if (productMasterDto.getAdditional().getHktv().getOnlineStatus() != null)
				return OnlineStatusEnum.OFFLINE.equals(productMasterDto.getAdditional().getHktv().getOnlineStatus());
			else
				return OnlineStatusEnum.OFFLINE.equals(beforeProductHktv.getOnlineStatus());
		}
	}

	public static ProductCheckDto generateProductCheckDo(ProductMasterDto productMasterDto, StoreDo store, BuProductCategoryDo primaryCategory, boolean isNewProduct, boolean isOfflineDueToRollback,
														 HktvProductDto beforeProductHktv,CheckProductResultDto check3PlResult, MembershipPricingEventSetDto checkPricingResult) {
		return ProductCheckDto.builder()
				.onlineStatus(productMasterDto.getAdditional().getHktv().getOnlineStatus())
				.store(store)
				.primaryCategoryCode(productMasterDto.getAdditional().getHktv().getPrimaryCategoryCode())
				.primaryCategory(primaryCategory)
				.isPrimarySku(productMasterDto.getAdditional().getHktv().getIsPrimarySku())
				.buCode(BuCodeEnum.HKTV.name())
				.isNewProduct(isNewProduct)
				.merchantId(productMasterDto.getMerchantId())
				.insuranceContractProdTermName(productMasterDto.getAdditional().getHktv().getTermName())
				.contractId(productMasterDto.getAdditional().getHktv().getContractNo())
				.warehouseId(productMasterDto.getAdditional().getHktv().getWarehouseId())
				.readyMethodCode(productMasterDto.getAdditional().getHktv().getProductReadyMethod())
				.deliveryMethodCode(productMasterDto.getAdditional().getHktv().getDeliveryMethod())
				.pickupTimeslot(productMasterDto.getAdditional().getHktv().getPickupTimeslot())
				.skuId(productMasterDto.getSkuId())
				.productId(productMasterDto.getProductId())
				.productTypeCodeList(productMasterDto.getAdditional().getHktv().getProductTypeCode())
				.brandId(productMasterDto.getBrandId())
				.packingBoxTypeCode(productMasterDto.getPackingBoxType())
				.mallDollar(productMasterDto.getAdditional().getHktv().getMallDollar())
				.vipMallDollar(productMasterDto.getAdditional().getHktv().getVipMallDollar())
				.videoLink(productMasterDto.getAdditional().getHktv().getVideoLink())
				.videoLinkTextEn(productMasterDto.getAdditional().getHktv().getVideoLinkTextEn())
				.videoLinkTextCh(productMasterDto.getAdditional().getHktv().getVideoLinkTextCh())
				.videoLinkTextSc(productMasterDto.getAdditional().getHktv().getVideoLinkTextSc())
				.videoLink2(productMasterDto.getAdditional().getHktv().getVideoLink2())
				.videoLinkTextEn2(productMasterDto.getAdditional().getHktv().getVideoLinkTextEn2())
				.videoLinkTextCh2(productMasterDto.getAdditional().getHktv().getVideoLinkTextCh2())
				.videoLinkTextSc2(productMasterDto.getAdditional().getHktv().getVideoLinkTextSc2())
				.videoLink3(productMasterDto.getAdditional().getHktv().getVideoLink3())
				.videoLinkTextEn3(productMasterDto.getAdditional().getHktv().getVideoLinkTextEn3())
				.videoLinkTextCh3(productMasterDto.getAdditional().getHktv().getVideoLinkTextCh3())
				.videoLinkTextSc3(productMasterDto.getAdditional().getHktv().getVideoLinkTextSc3())
				.videoLink4(productMasterDto.getAdditional().getHktv().getVideoLink4())
				.videoLinkTextEn4(productMasterDto.getAdditional().getHktv().getVideoLinkTextEn4())
				.videoLinkTextCh4(productMasterDto.getAdditional().getHktv().getVideoLinkTextCh4())
				.videoLinkTextSc4(productMasterDto.getAdditional().getHktv().getVideoLinkTextSc4())
				.videoLink5(productMasterDto.getAdditional().getHktv().getVideoLink5())
				.videoLinkTextEn5(productMasterDto.getAdditional().getHktv().getVideoLinkTextEn5())
				.videoLinkTextCh5(productMasterDto.getAdditional().getHktv().getVideoLinkTextCh5())
				.videoLinkTextSc5(productMasterDto.getAdditional().getHktv().getVideoLinkTextSc5())
				.urgent(productMasterDto.getAdditional().getHktv().getUrgent())
				.beforeUrgent(beforeProductHktv != null ? beforeProductHktv.getUrgent() : null)
				.weight(productMasterDto.getWeight())
				.weightUnit(productMasterDto.getWeightUnit())
				.height(productMasterDto.getPackingHeight())
				.depth(productMasterDto.getPackingDepth())
				.length(productMasterDto.getPackingLength())
				.skuSDescHktvEn(productMasterDto.getAdditional().getHktv().getSkuShortDescriptionEn())
				.skuSDescHktvCh(productMasterDto.getAdditional().getHktv().getSkuShortDescriptionCh())
				.skuSDescHktvSc(productMasterDto.getAdditional().getHktv().getSkuShortDescriptionSc())
				.skuLDescHktvEn(productMasterDto.getAdditional().getHktv().getSkuLongDescriptionEn())
				.skuLDescHktvCh(productMasterDto.getAdditional().getHktv().getSkuLongDescriptionCh())
				.skuLDescHktvSc(productMasterDto.getAdditional().getHktv().getSkuLongDescriptionSc())
				.storageType(productMasterDto.getAdditional().getHktv().getStorageType())
				.beforeStorageType(beforeProductHktv != null ? beforeProductHktv.getStorageType() : null)
				.minimumShelfLife(productMasterDto.getMinimumShelfLife())
				.rmCode(productMasterDto.getAdditional().getHktv().getRmCode())
				.beforeRmcode(beforeProductHktv != null ? beforeProductHktv.getRmCode() : null)
				.preSellFruit(productMasterDto.getAdditional().getHktv().getPreSellFruit())
				.physicalStore(productMasterDto.getAdditional().getHktv().getPhysicalStore())
				.storageTemperature(productMasterDto.getStorageTemperature())
				.field1(productMasterDto.getOption1())
				.value1(productMasterDto.getOption1Value())
				.field2(productMasterDto.getOption2())
				.value2(productMasterDto.getOption2Value())
				.field3(productMasterDto.getOption3())
				.value3(productMasterDto.getOption3Value())
				.sizeSystem(productMasterDto.getSizeSystem())
				.size(productMasterDto.getSize())
				.colorFamiliar(productMasterDto.getColourFamilies())
				.colorEn(productMasterDto.getColor())
				.pickupDays(productMasterDto.getAdditional().getHktv().getPickupDays())
				.returnDays(productMasterDto.getAdditional().getHktv().getReturnDays())
				.mainPhoto(productMasterDto.getAdditional().getHktv().getMainPhoto())
				.advertisePhoto(productMasterDto.getAdditional().getHktv().getAdvertisingPhoto())
				.otherPhoto(productMasterDto.getAdditional().getHktv().getOtherPhoto())
				.variantProductPhoto(productMasterDto.getAdditional().getHktv().getVariantProductPhoto())
				.cost(productMasterDto.getAdditional().getHktv().getCost())
				.originalPrice(productMasterDto.getOriginalPrice())
				.sellingPrice(productMasterDto.getAdditional().getHktv().getSellingPrice())
				.currency(productMasterDto.getAdditional().getHktv().getCurrency())
				.barcodeDtoList(productMasterDto.getBarcodes())
				.removalService(productMasterDto.getAdditional().getHktv().getNeedRemovalServices())
				.goodsType(productMasterDto.getAdditional().getHktv().getGoodsType())
				.deliveryDistrictList(productMasterDto.getAdditional().getHktv().getDeliveryDistrict())
				.virtualStore(productMasterDto.getAdditional().getHktv().getVirtualStore())
				.userMax(productMasterDto.getAdditional().getHktv().getUserMax())
				.productReadyDays(productMasterDto.getAdditional().getHktv().getProductReadyDays())
				.packingDimensionUnit(productMasterDto.getPackingDimensionUnit())
				.isOfflineDueToRollback(isOfflineDueToRollback)
				.skuNameEn(productMasterDto.getSkuNameEn())
				.skuNameCh(productMasterDto.getSkuNameCh())
				.skuNameSc(productMasterDto.getSkuNameSc())
				.childSkuInfo(productMasterDto.getBundleSetting() != null ? productMasterDto.getBundleSetting().getChildSkuInfo() : null)
				.manufactureCountry(productMasterDto.getManufacturedCountry())
				.affiliateUrl(productMasterDto.getAdditional().getHktv().getAffiliateUrl())
				.discountTextEn(productMasterDto.getAdditional().getHktv().getDiscountTextEn())
				.discountTextCh(productMasterDto.getAdditional().getHktv().getDiscountTextCh())
				.discountTextSc(productMasterDto.getAdditional().getHktv().getDiscountTextSc())
				.packingSpecEn(productMasterDto.getAdditional().getHktv().getPackingSpecEn())
				.packingSpecCh(productMasterDto.getAdditional().getHktv().getPackingSpecCh())
				.packingSpecSc(productMasterDto.getAdditional().getHktv().getPackingSpecSc())
				.cartonSizeDtoList(productMasterDto.getCartonSizeList())
				.visibility(productMasterDto.getAdditional().getHktv().getVisibility())
				.redeemStartDate(productMasterDto.getAdditional().getHktv().getRedeemStartDate())
				.fixRedemptionDate(productMasterDto.getAdditional().getHktv().getFixedRedemptionDate())
				.featureStartDate(productMasterDto.getAdditional().getHktv().getFeatureStartTime())
				.featureEndDate(productMasterDto.getAdditional().getHktv().getFeatureEndTime())
				.uponPurchaseDate(productMasterDto.getAdditional().getHktv().getUponPurchaseDate())
				.voucherType(productMasterDto.getAdditional().getHktv().getVoucherType())
				.voucherDisplayType(productMasterDto.getAdditional().getHktv().getVoucherDisplayType())
				.voucherTemplateType(productMasterDto.getAdditional().getHktv().getVoucherTemplateType())
				.expiryType(productMasterDto.getAdditional().getHktv().getExpiryType())
				.finePrintEn(productMasterDto.getAdditional().getHktv().getFinePrintEn())
				.finePrintCh(productMasterDto.getAdditional().getHktv().getFinePrintCh())
				.finePrintSc(productMasterDto.getAdditional().getHktv().getFinePrintSc())
				.warrantyPeriodUnit(productMasterDto.getAdditional().getHktv().getWarrantyPeriodUnit())
				.warrantyPeriod(productMasterDto.getAdditional().getHktv().getWarrantyPeriod())
				.warrantySupplierEn(productMasterDto.getAdditional().getHktv().getWarrantySupplierEn())
				.warrantySupplierCh(productMasterDto.getAdditional().getHktv().getWarrantySupplierCh())
				.warrantySupplierSc(productMasterDto.getAdditional().getHktv().getWarrantySupplierSc())
				.warrantyRemarkEn(productMasterDto.getAdditional().getHktv().getWarrantyRemarkEn())
				.warrantyRemarkCh(productMasterDto.getAdditional().getHktv().getWarrantyRemarkCh())
				.warrantyRemarkSc(productMasterDto.getAdditional().getHktv().getWarrantyRemarkSc())
				.serviceCentreAddressEn(productMasterDto.getAdditional().getHktv().getServiceCentreAddressEn())
				.serviceCentreAddressCh(productMasterDto.getAdditional().getHktv().getServiceCentreAddressCh())
				.serviceCentreAddressSc(productMasterDto.getAdditional().getHktv().getServiceCentreAddressSc())
				.serviceCentreEmail(productMasterDto.getAdditional().getHktv().getServiceCentreEmail())
				.serviceCentreContact(productMasterDto.getAdditional().getHktv().getServiceCentreContact())
				.invoiceRemarksEn(productMasterDto.getAdditional().getHktv().getInvoiceRemarksEn())
				.invoiceRemarksCh(productMasterDto.getAdditional().getHktv().getInvoiceRemarksCh())
				.invoiceRemarksSc(productMasterDto.getAdditional().getHktv().getInvoiceRemarksSc())
				.style(productMasterDto.getAdditional().getHktv().getStyle())
				.check3PlResult(check3PlResult)
				.ewPercentageSetting(productMasterDto.getAdditional().getHktv().getEwPercentageSetting())
				.claimLinkEn(productMasterDto.getAdditional().getHktv().getClaimLinkEn())
				.claimLinkCh(productMasterDto.getAdditional().getHktv().getClaimLinkCh())
				.claimLinkSc(productMasterDto.getAdditional().getHktv().getClaimLinkSc())
				.beforeUserMax(beforeProductHktv != null ? beforeProductHktv.getUserMax() : null)
				.partnerInfo(productMasterDto.getAdditional().getHktv().getPartnerInfo())
				.externalPlatform(productMasterDto.getAdditional().getHktv().getExternalPlatform())
				.checkPricingResult(checkPricingResult)
				.isSkuOfflineOrWillBeOffline(isSkuOfflineOrWillBeOffline(productMasterDto, beforeProductHktv))
				.build();
	}

}
