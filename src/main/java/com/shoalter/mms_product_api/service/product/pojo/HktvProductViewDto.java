package com.shoalter.mms_product_api.service.product.pojo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;

@Data
public class HktvProductViewDto extends HktvProductDto implements Serializable {
	@JsonProperty("contract_name")
	@SerializedName("contract_name")
	private String contractName;
	@JsonProperty("store_name")
	@SerializedName("store_name")
	private String storeName;
	@JsonProperty("product_ready_method_desc")
	@SerializedName("product_ready_method_desc")
	private String productReadyMethodDesc;
	@JsonProperty("delivery_method_desc")
	@SerializedName("delivery_method_desc")
	private String deliveryMethodDesc;
	@JsonProperty("currency_desc")
	@SerializedName("currency_desc")
	private String currencyDesc;
	@JsonProperty("warehouse")
	@SerializedName("warehouse")
	private String warehouse;
	@JsonProperty("product_ready_days_desc")
	@SerializedName("product_ready_days_desc")
	private String productReadyDaysDesc;
	@JsonProperty("pickup_days_desc")
	@SerializedName("pickup_days_desc")
	private String pickupDaysDesc;
}
