package com.shoalter.mms_product_api.service.product.pojo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class BatchTranslateRequestDto {
	@JsonProperty("bu_code")
	@SerializedName("bu_code")
    private String buCode;
	@JsonProperty("storefront_store_code")
	@SerializedName("storefront_store_code")
    private List<String> storefrontStoreCodeList;
	@JsonProperty("file_name")
	@SerializedName("file_name")
	private String fileName;
}
