package com.shoalter.mms_product_api.service.product.pojo;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class ProductReportProductDto {
    private String no;

    private String brandNameEn;

    private String manufacturedCountry;
    private String colourFamilies;
    private String color;
    private String sizeSystem;
    private String size;
    private String field1;
    private String field1Value;
    private String field2;
    private String field2Value;
    private String field3;
    private String field3Value;
    private String barcode;
    private BigDecimal packingHeight;
    private BigDecimal packingLength;
    private BigDecimal packingDepth;
    private String packingDimensionUnit;
    private BigDecimal weight;
    private String weightUnit;
	private String skuNameEn;
	private String skuNameCh;

    /**
     * HKTV 產品特有資料
     */
    private ProductReportHktvProductInfoDto hktvInfo;
}
