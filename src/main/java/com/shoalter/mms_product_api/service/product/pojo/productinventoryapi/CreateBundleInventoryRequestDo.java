package com.shoalter.mms_product_api.service.product.pojo.productinventoryapi;

import java.util.List;
import java.util.stream.Collectors;

import com.shoalter.mms_product_api.config.product.ProductInventoryActionEnum;
import com.shoalter.mms_product_api.config.product.ProductInventoryStockStatusEnum;
import com.shoalter.mms_product_api.service.product.pojo.SingleEditProductDto;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class CreateBundleInventoryRequestDo {
	private boolean shareMode;
	private List<CreateBundleInventoryBuInfoDo> buInfoList;
	private String uuid;

	public static CreateBundleInventoryRequestDo generateBundleInventoryRequestDo(SingleEditProductDto singleEditProductDto, String uuid) {
		List<CreateBundleInventoryBuInfoDo> buInfoList = singleEditProductDto
			.getProduct().getBundleSetting().getMallInventoryInfo().stream()
			.map(data -> CreateBundleInventoryBuInfoDo.builder()
				.qty(data.getSettingQuantity())
				.mode(ProductInventoryActionEnum.SET.getParameter())
				.buCode(data.getMall())
				.stockStatus(ProductInventoryStockStatusEnum.AVAILABLE.getParameter())
				.build())
			.collect(Collectors.toList());
		return CreateBundleInventoryRequestDo.builder()
			.uuid(uuid)
			.shareMode(false)
			.buInfoList(buInfoList)
			.build();
	}
}
