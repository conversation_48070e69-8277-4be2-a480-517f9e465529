package com.shoalter.mms_product_api.service.product.pojo;

import com.shoalter.mms_product_api.config.product.ProductMasterErrorTypeEnum;
import com.shoalter.mms_product_api.config.product.StatusCodeEnum;
import com.shoalter.mms_product_api.util.CollectionUtil;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
public class ProductMasterResponseDto {
	private int status;
	private String traceId;
	private String message;
	private ProductMasterErrorTypeEnum errorCode;
	private List<String> errorMessageList;

	public static ProductMasterResponseDto fail(List<String> errorMessageList) {
		return ProductMasterResponseDto.builder()
			.status(StatusCodeEnum.FAIL.getCode())
			.errorMessageList(errorMessageList)
			.build();
	}

	public static ProductMasterResponseDto generate(SaveProductResultDto saveProductResultDto, List<String> errorMessageList) {
		return ProductMasterResponseDto.<String>builder()
			.traceId(saveProductResultDto.getData())
			.errorCode(ProductMasterErrorTypeEnum.getEnum(saveProductResultDto.getCode()))
			.status(CollectionUtil.isEmpty(errorMessageList) ? StatusCodeEnum.SUCCESS.getCode() : StatusCodeEnum.FAIL.getCode())
			.errorMessageList(errorMessageList)
			.build();
	}
}
