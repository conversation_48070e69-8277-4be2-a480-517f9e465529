package com.shoalter.mms_product_api.service.product;

import com.shoalter.mms_product_api.config.product.SysParmSegment;
import com.shoalter.mms_product_api.dao.repository.system.SysParmRepository;
import com.shoalter.mms_product_api.dao.repository.system.pojo.SysParmDo;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.product.helper.PermissionHelper;
import com.shoalter.mms_product_api.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

@RequiredArgsConstructor
@Service
@Slf4j
public class IsVirtualStoreService {
	private final SysParmRepository sysParmRepository;
	private final PermissionHelper permissionHelper;

	public ResponseDto<Boolean> start(UserDto userDto, Integer merchantId) {
		permissionHelper.checkPermission(userDto, merchantId);
		List<SysParmDo> virtualStoreList = sysParmRepository.findBySegment(SysParmSegment.VIRTUAL_STORE_MERCHANT);
		boolean isVirtualStore = false;
		for (SysParmDo virtualStore : virtualStoreList) {
			if (StringUtil.isNotEmpty(virtualStore.getParmValue())) {
				String[] virtualStoreMerchantArray = virtualStore.getParmValue().split(",");
				if (Arrays.asList(virtualStoreMerchantArray).contains(String.valueOf(merchantId))){
					isVirtualStore = true;
					break;
				}
			}
		}
		return ResponseDto.<Boolean>builder().status(1).data(isVirtualStore).build();
	}
}
