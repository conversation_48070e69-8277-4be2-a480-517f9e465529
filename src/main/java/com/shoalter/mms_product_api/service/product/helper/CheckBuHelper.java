package com.shoalter.mms_product_api.service.product.helper;

import com.google.gson.Gson;
import com.shoalter.mms_product_api.config.product.ProductMasterBusinessUnitType;
import com.shoalter.mms_product_api.config.product.SaveProductType;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordDo;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordRowDo;
import com.shoalter.mms_product_api.service.product.pojo.SingleEditProductDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@RequiredArgsConstructor
@Service
@Slf4j
public class CheckBuHelper {
	private final Gson gson;

	public void checkUpdateBuList(SaveProductRecordDo productRecord, SaveProductRecordRowDo row) {
		switch (productRecord.getUploadType()) {
			case SaveProductType.BATCH_EDIT_OVERSEA_DELIVERY:
			case SaveProductType.BATCH_EDIT_PRODUCT_PACKAGING_INFO:
				break;
			default:
				addBuToSend(row);
				break;
		}
	}

	private void addBuToSend(SaveProductRecordRowDo row) {
		SingleEditProductDto singleSaveProductDto = gson.fromJson(row.getContent(), SingleEditProductDto.class);
		List<String> updateBuList = checkBuToSend(singleSaveProductDto);

		if (!updateBuList.isEmpty()) {
			singleSaveProductDto.getProduct().setBuToSend(updateBuList);
			row.setContent(gson.toJson(singleSaveProductDto));
		}
	}

	public void addBuToSend(SingleEditProductDto singleSaveProductDto) {
		List<String> updateBuList = checkBuToSend(singleSaveProductDto);
		if (!updateBuList.isEmpty()) {
			singleSaveProductDto.getProduct().setBuToSend(updateBuList);
		}
	}

	private List<String> checkBuToSend(SingleEditProductDto singleSaveProductDto) {
		List<String> updateBuList = new ArrayList<>();
		if (singleSaveProductDto.getProduct().getAdditional().getHktv() != null) {
			updateBuList.add(ProductMasterBusinessUnitType.HKTV);
		}
		if (singleSaveProductDto.getProduct().getAdditional().getThirdParty() != null) {
			updateBuList.add(ProductMasterBusinessUnitType.THIRD_PL);
		}
		if (singleSaveProductDto.getProduct().getAdditional().getLittleMall() != null) {
			updateBuList.add(ProductMasterBusinessUnitType.LITTLE_MALL);
		}
		if (singleSaveProductDto.getProduct().getAdditional().getIids() != null) {
			updateBuList.add(ProductMasterBusinessUnitType.IIDS);
		}
		return updateBuList;
	}
}
