package com.shoalter.mms_product_api.service.product.helper;

import com.google.gson.Gson;
import com.shoalter.mms_product_api.config.hybris.HybrisAction;
import com.shoalter.mms_product_api.config.interceptor.ClientIpHolder;
import com.shoalter.mms_product_api.config.product.BuCodeEnum;
import com.shoalter.mms_product_api.config.product.CategoryConfig;
import com.shoalter.mms_product_api.config.product.ErrorMessageTypeCode;
import com.shoalter.mms_product_api.config.product.MonitorPlatformGroupEnum;
import com.shoalter.mms_product_api.config.product.OnlineStatusEnum;
import com.shoalter.mms_product_api.config.product.ProductActionEnum;
import com.shoalter.mms_product_api.config.product.ProductContractMatchStatusEnum;
import com.shoalter.mms_product_api.config.product.ProductFileConfig;
import com.shoalter.mms_product_api.config.product.ProductStatus;
import com.shoalter.mms_product_api.config.product.SaveProductSource;
import com.shoalter.mms_product_api.config.product.SaveProductStatus;
import com.shoalter.mms_product_api.config.product.SaveProductStatusEnum;
import com.shoalter.mms_product_api.config.product.SaveProductType;
import com.shoalter.mms_product_api.config.product.SaveProductTypeEnum;
import com.shoalter.mms_product_api.config.product.StatusCodeEnum;
import com.shoalter.mms_product_api.config.product.SysParmSegment;
import com.shoalter.mms_product_api.config.product.SysParmSegmentEnum;
import com.shoalter.mms_product_api.config.type.ConstantType;
import com.shoalter.mms_product_api.config.type.ContractType;
import com.shoalter.mms_product_api.config.type.ProductReadyMethodType;
import com.shoalter.mms_product_api.dao.mapper.businessUnit.BusinessMapper;
import com.shoalter.mms_product_api.dao.mapper.businessUnit.pojo.BusinessPlatformDo;
import com.shoalter.mms_product_api.dao.mapper.product.ProductAttributesHistoryMapper;
import com.shoalter.mms_product_api.dao.mapper.product.ProductCategoryHistoryMapMapper;
import com.shoalter.mms_product_api.dao.mapper.product.ProductHistoryMapper;
import com.shoalter.mms_product_api.dao.mapper.product.ProductImagesHistoryMapper;
import com.shoalter.mms_product_api.dao.mapper.product.ProductMapper;
import com.shoalter.mms_product_api.dao.mapper.product.ProductStorePromotionMapper;
import com.shoalter.mms_product_api.dao.mapper.product.ProductVideoHistoryMapper;
import com.shoalter.mms_product_api.dao.repository.api.SaveProductRecordRepository;
import com.shoalter.mms_product_api.dao.repository.api.SaveProductRecordRowRepository;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordDo;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordRowDo;
import com.shoalter.mms_product_api.dao.repository.brand.BrandRepository;
import com.shoalter.mms_product_api.dao.repository.brand.pojo.BrandDo;
import com.shoalter.mms_product_api.dao.repository.bundle.BundleProductRepository;
import com.shoalter.mms_product_api.dao.repository.bundle.pojo.BundleProductBundleSettingData;
import com.shoalter.mms_product_api.dao.repository.bundle.pojo.BundleProductDo;
import com.shoalter.mms_product_api.dao.repository.business.BuProductCategoryRepository;
import com.shoalter.mms_product_api.dao.repository.business.pojo.BuProductCategoryDo;
import com.shoalter.mms_product_api.dao.repository.contract.ContractProdTermsRepository;
import com.shoalter.mms_product_api.dao.repository.contract.ContractRepository;
import com.shoalter.mms_product_api.dao.repository.contract.ContractTypeRepository;
import com.shoalter.mms_product_api.dao.repository.contract.pojo.ContractDo;
import com.shoalter.mms_product_api.dao.repository.contract.pojo.ContractProdTermsDo;
import com.shoalter.mms_product_api.dao.repository.contract.pojo.ContractTypeDo;
import com.shoalter.mms_product_api.dao.repository.product.ProductAttributeRepository;
import com.shoalter.mms_product_api.dao.repository.product.ProductBarcodeRepository;
import com.shoalter.mms_product_api.dao.repository.product.ProductBuStatusRepository;
import com.shoalter.mms_product_api.dao.repository.product.ProductCartonSizeRepository;
import com.shoalter.mms_product_api.dao.repository.product.ProductCategoriesRepository;
import com.shoalter.mms_product_api.dao.repository.product.ProductHistoryRepository;
import com.shoalter.mms_product_api.dao.repository.product.ProductImagesRepository;
import com.shoalter.mms_product_api.dao.repository.product.ProductOverseaDeliveryRepository;
import com.shoalter.mms_product_api.dao.repository.product.ProductRepository;
import com.shoalter.mms_product_api.dao.repository.product.ProductStoreStatusHistoryRepository;
import com.shoalter.mms_product_api.dao.repository.product.ProductStoreStatusRepository;
import com.shoalter.mms_product_api.dao.repository.product.ProductVideoRepository;
import com.shoalter.mms_product_api.dao.repository.product.pojo.ProductAttributesDo;
import com.shoalter.mms_product_api.dao.repository.product.pojo.ProductBarcodeDo;
import com.shoalter.mms_product_api.dao.repository.product.pojo.ProductBuStatusDo;
import com.shoalter.mms_product_api.dao.repository.product.pojo.ProductCartonSizeDo;
import com.shoalter.mms_product_api.dao.repository.product.pojo.ProductCategoriesDo;
import com.shoalter.mms_product_api.dao.repository.product.pojo.ProductDo;
import com.shoalter.mms_product_api.dao.repository.product.pojo.ProductImagesDo;
import com.shoalter.mms_product_api.dao.repository.product.pojo.ProductOverseaDeliveryDo;
import com.shoalter.mms_product_api.dao.repository.product.pojo.ProductStoreStatusDo;
import com.shoalter.mms_product_api.dao.repository.product.pojo.ProductStoreStatusHistoryDo;
import com.shoalter.mms_product_api.dao.repository.product.pojo.ProductVideoDo;
import com.shoalter.mms_product_api.dao.repository.rabbit.RabbitRetryDo;
import com.shoalter.mms_product_api.dao.repository.rabbit.RabbitRetryRepository;
import com.shoalter.mms_product_api.dao.repository.rm.RmTeamRepository;
import com.shoalter.mms_product_api.dao.repository.rm.pojo.RmTeamDo;
import com.shoalter.mms_product_api.dao.repository.store.StoreRepository;
import com.shoalter.mms_product_api.dao.repository.store.StoreWarehouseRepository;
import com.shoalter.mms_product_api.dao.repository.store.pojo.StoreDo;
import com.shoalter.mms_product_api.dao.repository.store.pojo.StoreWarehouseDo;
import com.shoalter.mms_product_api.dao.repository.system.SysParmRepository;
import com.shoalter.mms_product_api.dao.repository.system.pojo.SysParmDo;
import com.shoalter.mms_product_api.exception.SystemException;
import com.shoalter.mms_product_api.helper.CacheHelper;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.service.base.pojo.SyncResponseDto;
import com.shoalter.mms_product_api.service.base.pojo.SyncService;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.bundle.pojo.BundleChildSkuDto;
import com.shoalter.mms_product_api.service.bundle.pojo.BundleUrlData;
import com.shoalter.mms_product_api.service.bundle.pojo.BundleValidationData;
import com.shoalter.mms_product_api.service.gateway.GatewayHelper;
import com.shoalter.mms_product_api.service.hybris.helper.HybrisHelper;
import com.shoalter.mms_product_api.service.product.pojo.CartonSizeDto;
import com.shoalter.mms_product_api.service.product.pojo.CheckProductResultDto;
import com.shoalter.mms_product_api.service.product.pojo.ExchangeRatePriceDto;
import com.shoalter.mms_product_api.service.product.pojo.HktvProductDto;
import com.shoalter.mms_product_api.service.product.pojo.MmsgwApiResultDto;
import com.shoalter.mms_product_api.service.product.pojo.MmsgwResponseDto;
import com.shoalter.mms_product_api.service.product.pojo.MmsgwResponseStatusDto;
import com.shoalter.mms_product_api.service.product.pojo.MmsgwStorefrontRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.MmsgwStorefrontResponseDto;
import com.shoalter.mms_product_api.service.product.pojo.MppsProductDo;
import com.shoalter.mms_product_api.service.product.pojo.ProductBarcodeDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductImageDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterMqDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterReturnDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMppsRabbitDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMppsRabbitSkuDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductOldMultimediaDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductPartnerInfoDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductStatusDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductVideoDto;
import com.shoalter.mms_product_api.service.product.pojo.SaveHybrisCreatePromotionalDiscountRuleDto;
import com.shoalter.mms_product_api.service.product.pojo.SaveHybrisProductDto;
import com.shoalter.mms_product_api.service.product.pojo.SaveHybrisVideoInfoDto;
import com.shoalter.mms_product_api.service.product.pojo.SaveProductData;
import com.shoalter.mms_product_api.service.product.pojo.SaveProductResultDto;
import com.shoalter.mms_product_api.service.product.pojo.SaveVideoResultDto;
import com.shoalter.mms_product_api.service.product.pojo.SingleEditProductDto;
import com.shoalter.mms_product_api.service.product.pojo.UpdateHybrisProdctForOnOffLineDto;
import com.shoalter.mms_product_api.service.product.pojo.VariantMatrixProductDto;
import com.shoalter.mms_product_api.service.product.pojo.hybris.HybrisActionMapper;
import com.shoalter.mms_product_api.service.product.pojo.hybris.HybrisSalesChannelRuleDto;
import com.shoalter.mms_product_api.service.product.pojo.hybris.HybrisSyncData;
import com.shoalter.mms_product_api.util.CollectionUtil;
import com.shoalter.mms_product_api.util.DateUtil;
import com.shoalter.mms_product_api.util.ResourceUtil;
import com.shoalter.mms_product_api.util.SpringBeanProvider;
import com.shoalter.mms_product_api.util.StringUtil;
import com.shoalter.mms_product_api.util.enums.CurrencyEnum;
import java.util.Locale;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.hibernate.service.spi.ServiceException;
import org.kie.api.runtime.KieContainer;
import org.kie.api.runtime.KieSession;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.MessageSource;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ForkJoinPool;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static com.shoalter.mms_product_api.config.type.ConstantType.INVISIBLE_FLAG_N;
import static com.shoalter.mms_product_api.config.type.ConstantType.INVISIBLE_FLAG_Y;
import static com.shoalter.mms_product_api.util.BigDecimalUtil.removeAmtLastZero;
import static com.shoalter.mms_product_api.util.DataColorUtil.getColorEnOrCh;

@Slf4j
@RequiredArgsConstructor
@Service
public class SaveProductHelper {

	private final MessageSource messageSource;
	private final RabbitTemplate mppsRabbitTemplate;
	private final Gson gson;
	private static final DateTimeFormatter productMasterSimpleDateFormat = DateTimeFormatter.ofPattern(
		"yyyy-MM-dd'T'HH:mm:ss");
	private static final DateTimeFormatter hybrisDateFormat = DateTimeFormatter.ofPattern(
		"MM/dd/yyyy HH:mm:ss");
	private static final DateTimeFormatter simpleDateFormat = DateTimeFormatter.ofPattern(
		"MM/dd/yyyy");
	// this format is for db save
	private static final DateTimeFormatter productSimpleDateFormat = DateTimeFormatter.ofPattern(
		"yyyy-MM-dd HH:mm:ss");
	private final ProductVideoHelper productVideoHelper;
	private final ProductImageHelper productImageHelper;
	private final GatewayHelper gatewayHelper;
	private final MerchantHelper merchantHelper;
	private final ProductPreProcessingHelper productPreProcessingHelper;
	private final CheckBuHelper checkBuHelper;
	private final ProductMasterHelper productMasterHelper;
	private final SaveProductRecordHelper saveProductRecordHelper;
	private final GenerateIIDSDataHelper generateIIDSDataHelper;
	private final CheckProductHelper checkProductHelper;
	private final SaveProductRecordRowHelper saveProductRecordRowHelper;
	private final ProductPriceMonitorProductHelper productPriceMonitorProductHelper;
	private final CacheHelper cacheHelper;
	private final ExchangeRateHelper exchangeRateHelper;

	private final ProductRepository productRepository;
	private final SysParmRepository sysParmRepository;
	private final ProductAttributeRepository productAttributeRepository;
	private final ProductBuStatusRepository productBuStatusRepository;
	private final ProductStoreStatusRepository productStoreStatusRepository;
	private final ContractRepository contractRepository;
	private final ContractTypeRepository contractTypeRepository;
	private final ContractProdTermsRepository contractProdTermsRepository;
	private final BuProductCategoryRepository buProductCategoryRepository;
	private final StoreRepository storeRepository;
	private final ProductImagesRepository productImagesRepository;
	private final ProductVideoRepository productVideoRepository;
	private final RabbitRetryRepository rabbitRetryRepository;
	private final ProductCategoriesRepository productCategoriesRepository;
	private final StoreWarehouseRepository storeWarehouseRepository;
	private final ProductHistoryRepository productHistoryRepository;
	private final RmTeamRepository rmTeamRepository;
	private final BrandRepository brandRepository;
	private final SaveProductRecordRowRepository saveProductRecordRowRepository;
	private final SaveProductRecordRepository saveProductRecordRepository;
	private final ProductOverseaDeliveryRepository productOverseaDeliveryRepository;

	private final BusinessMapper businessMapper;
	private final ProductMapper productMapper;
	private final ProductStorePromotionMapper productStorePromotionMapper;
	private final ProductVideoHistoryMapper productVideoHistoryMapper;
	private final ProductImagesHistoryMapper productImagesHistoryMapper;
	private final ProductCategoryHistoryMapMapper productCategoryHistoryMapMapper;
	private final ProductHistoryMapper productHistoryMapper;
	private final ProductAttributesHistoryMapper productAttributesHistoryMapper;
	private final ProductBarcodeRepository productBarcodeRepository;
	private final BundleProductRepository bundleProductRepository;
	private final ProductCartonSizeRepository productCartonSizeRepository;
	private final ProductStoreStatusHistoryRepository productStoreStatusHistoryRepository;

	private final KieContainer kieContainer;

	private final static List<String> ALLOW_TO_MPPS_PRODUCT_READY_METHOD_LIST = List.of(ProductReadyMethodType.STANDARD_DELIVERY_MERCHANT_DELIVER_TO_WAREHOUSE, ProductReadyMethodType.STANDARD_DELIVERY_PICKUP_BY_THIRD_PARTY, ProductReadyMethodType.STANDARD_DELIVERY_SAME_DAY_IN_HUB);

	@Value("${mpps.rabbitmq.sku.info.routing.key}")
	private String rabbitMqRoutingKey;
	@Value("${hktv.image.url}")
	private String hktvImageUrl;
	@Value("${create.promotion.service.name}")
	private String createPromotionServiceName;
	@Value("${create.promotion.service.method}")
	private String createPromotionServiceMethod;
	@Value("${create.product.service.name}")
	private String createProductServerName;
	@Value("${create.product.service.method}")
	private String createProductServerMethod;
	@Value("${update.product.service.name}")
	private String updateProductServerName;
	@Value("${update.product.service.method}")
	private String updateProductServerMethod;
	//TODO After Phase 2, confirm with WMS whether it can be deleted.
	@Value("${feature.toggle.send.mpps.queue}")
	private Boolean featureToggleSendMppsQueue;

	public List<ProductMasterReturnDto> createOrUpdateProducts(UserDto userDto, List<ProductMasterMqDto> productMasterMqDto) {
		List<ProductMasterReturnDto> results = new ArrayList<>();

		SaveProductRecordDo saveProductRecordDo = saveProductRecordRepository.findByRecordRowId(productMasterMqDto.get(0).getRecordRowId()).orElse(null);
		if (saveProductRecordDo == null) {
			productMasterMqDto.forEach(data -> results.add(generateResult(data, List.of(messageSource.getMessage("message340", new String[]{data.getRecordRowId().toString()}, null)))));
			return results;
		}

		//make primary sku sync first ("Y" sync before "N")
		productMasterMqDto.sort((p1, p2) -> p2.getIsPrimarySku().compareTo(p1.getIsPrimarySku()));

		Map<Long, Set<Long>> dependRecordRowIdMap = saveProductRecordRowHelper.findDependRecordRowIdMap(saveProductRecordDo, productMasterMqDto.stream().map(ProductMasterMqDto::getRecordRowId).collect(Collectors.toSet()));
		Set<Long> failedRecordRowId = new HashSet<>();
		Map<String, Long> uuidToRecordRowIdMap = new HashMap<>();
		SaveProductData saveProductData = exchangeRateHelper.generateSaveProductData(
			saveProductRecordDo.getUploadType(),
			productMasterMqDto.stream()
			.map(ProductMasterMqDto::getStorefrontStoreCode)
			.collect(Collectors.toSet()));

		for (ProductMasterMqDto product : productMasterMqDto) {
			log.info("saveProduct to hybris uuid: {}, version: {}, record row id: {}", product.getUuid(), product.getVersion(), product.getRecordRowId());
			uuidToRecordRowIdMap.put(product.getUuid(), product.getRecordRowId());

			//turn create/update result to fail if depend-on record row is failed
			if (dependRecordRowIdMap.containsKey(product.getRecordRowId()) &&
				dependRecordRowIdMap.get(product.getRecordRowId()).stream().anyMatch(failedRecordRowId::contains)) {
				log.info("variant sku already failed. skip update uuid: {}", product.getUuid());
				results.add(generateResult(product, List.of(messageSource.getMessage("message337", null, null))));
				failedRecordRowId.add(product.getRecordRowId());
				continue;
			}

			ProductMasterReturnDto result = SpringBeanProvider.getBean(SaveProductHelper.class).createOrUpdateProduct(userDto, product, saveProductRecordDo, saveProductData);
			results.add(result);
			if (StatusCodeEnum.FAIL.name().equals(result.getResult())) {
				failedRecordRowId.add(product.getRecordRowId());
			}
		}

		//turn create/update result to fail if depend-on record row is failed
		results.stream()
			.filter(data -> {
				if (!StatusCodeEnum.SUCCESS.name().equals(data.getResult())) {
					return false;
				}
				Long recordRowId = uuidToRecordRowIdMap.get(data.getUuid());
				return dependRecordRowIdMap.containsKey(recordRowId) &&
					dependRecordRowIdMap.get(recordRowId).stream().anyMatch(failedRecordRowId::contains);
			})
			.forEach(data -> {
				data.setResult(StatusCodeEnum.FAIL.name());
				data.setFailedReason(List.of(messageSource.getMessage("message337", null, null)));
				data.setForceRollback(true);
			});


		return results;
	}

	@Transactional
	public ProductMasterReturnDto createOrUpdateProduct(UserDto userDto, ProductMasterMqDto productMasterMqDto, SaveProductRecordDo saveProductRecordDo, SaveProductData saveProductData) {
		long startTime = System.currentTimeMillis();
		ProductMasterReturnDto result;
		//normal product
		if (productMasterMqDto.getBundleSetting() == null) {
			Optional<ProductDo> productDoOpt = productRepository.findByUuid(productMasterMqDto.getUuid());
			if (productDoOpt.isEmpty()) {
				result = createProduct(userDto, productMasterMqDto, saveProductRecordDo, saveProductData);
			} else {
				result = updateProduct(userDto, productMasterMqDto, productDoOpt.get(), saveProductRecordDo, saveProductData);
			}

		//bundle product
		} else {
			Optional<BundleProductDo> bundleProductDoOpt = bundleProductRepository.findByUuid(productMasterMqDto.getUuid());
			if (bundleProductDoOpt.isEmpty()) {
				log.info("create bundle sku [{}]", productMasterMqDto.getUuid());
				result = createBundle(userDto, productMasterMqDto, saveProductRecordDo, saveProductData);
			} else {
				log.info("update bundle sku [{}]", productMasterMqDto.getUuid());
				result = updateBundle(userDto, productMasterMqDto, bundleProductDoOpt.get(), saveProductRecordDo, saveProductData);
			}
		}

		long endTime = System.currentTimeMillis();
		log.info("Time taken by SaveProductHelper createProduct method : {} milliseconds for sku uuid : {}", (endTime - startTime), productMasterMqDto.getUuid());
		return result;
	}

	@Transactional
	public ProductMasterReturnDto createProduct(UserDto userDto, ProductMasterMqDto productMasterMqDto, SaveProductRecordDo saveProductRecordDo, SaveProductData saveProductData) {
		List<String> errorMessageList = new ArrayList<>();
		boolean isSyncHybrisSuccess = false;

		String storeSkuId = productMasterMqDto.getStoreSkuId();

		boolean isStoreSkuExists = productStoreStatusRepository.findByStoreSkuId(
			storeSkuId).isPresent();

		if (isStoreSkuExists) {
			log.info("STORE SKU ID: {} already exists in the store.", storeSkuId);
			errorMessageList.add(messageSource.getMessage("message365", new String[]{storeSkuId}, null));
			return generateResult(productMasterMqDto, errorMessageList);
		}

		try {
			BusinessPlatformDo businessPlatformDo =
					businessMapper.findByBusinessCode(ConstantType.PLATFORM_CODE_HKTV).orElseThrow(() -> new SystemException("Business or platform doesn't exist"));

			resetProductParameter(businessPlatformDo, productMasterMqDto, true);
			ProductAttributesDo productAttributesDo = saveProductAttribute(null, productMasterMqDto);
			ProductDo productDo = saveProduct(userDto, productMasterMqDto, null, productAttributesDo);
			saveProductBuStatus(userDto, businessPlatformDo, productDo);

			boolean isProductStoreMatchContractProductTerm = saveProductStoreStatus(userDto, productMasterMqDto, productDo).getLeft();
			if (!isProductStoreMatchContractProductTerm) {
				errorMessageList.add(messageSource.getMessage("message12", null, null));
				return generateErrorResponse(productMasterMqDto.getUuid(), errorMessageList);
			}

			saveProductImages(userDto, productMasterMqDto, productDo, true);

			SaveVideoResultDto saveVideoResultDto = saveProductVideos(userDto, productMasterMqDto, productDo);
			if (!saveVideoResultDto.isSuccess()) {
				errorMessageList.add(messageSource.getMessage("message103", null, null));
			}
			saveProductOverseaDelivery(userDto, productMasterMqDto, productDo);
			saveProductBarcode(userDto, productMasterMqDto, productDo);
			saveCartonSizes(userDto, productMasterMqDto, productDo);

			if (Boolean.TRUE.equals(featureToggleSendMppsQueue)) {
				ProductMppsRabbitDto productMppsRabbitDto = generateProductMppRabbitDto(productMasterMqDto);
				sendProductMppsToRabbitMq(productDo, productMppsRabbitDto);
			}
			syncVariantProductCategories(userDto, businessPlatformDo, productDo, productMasterMqDto);

			if (saveVideoResultDto.isSuccess()) {
				ExchangeRatePriceDto exchangeRatePriceDto = exchangeRateHelper.checkCurrencyAndGenerateHybrisPrice(productDo.getSkuCode(), saveProductData, productDo.getCurrencyCode(), productDo.getOriginalPrice(), productDo.getSellingPrice());
				HybrisSyncData hybrisSyncData = new HybrisSyncData().generateCreateSyncData(userDto, businessPlatformDo, productDo, productMasterMqDto, saveProductRecordDo.getUploadType(), exchangeRatePriceDto);
				SyncResponseDto<Void> syncHybrisResponse = syncProductToHybris(hybrisSyncData);
				isSyncHybrisSuccess = syncHybrisResponse.isSyncedTo(SyncService.HYBRIS);
				errorMessageList.addAll(syncHybrisResponse.getErrorMessageList());

				if (!isSyncHybrisSuccess) {
					log.error("Sync Hybris fail ,transaction rollback uuid :{},sku :{}", productMasterMqDto.getUuid(), productMasterMqDto.getSkuId());
					TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
				}
			}
		} catch (SystemException e) {
			if (!isSyncHybrisSuccess) {
				TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			}
			errorMessageList.add(e.getMessage());
			log.error(e.getMessage(), e);
		} catch (NullPointerException e) {
			if (!isSyncHybrisSuccess) {
				TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			}
			errorMessageList.add(messageSource.getMessage("message10", new Object[]{ErrorMessageTypeCode.NULL_POINTER_EXCEPTION}, null));
			log.error(e.getMessage(), e);
		} catch (DataIntegrityViolationException e) {
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			errorMessageList.add("Data exception: " + ExceptionUtils.getRootCauseMessage(e));
		}

		ProductMasterReturnDto result = generateResult(productMasterMqDto, errorMessageList);
		if (StatusCodeEnum.FAIL.name().equals(result.getResult()) && isSyncHybrisSuccess) {
			result.setForceRollback(true);
		}
		return result;
	}

	@Transactional
	public ProductMasterReturnDto updateProduct(UserDto userDto, ProductMasterMqDto productMasterMqDto, ProductDo productDo, SaveProductRecordDo saveProductRecordDo, SaveProductData saveProductData) {
		List<String> errorMessageList = new ArrayList<>();
		boolean isSyncHybrisSuccess = false;
		try {
			BusinessPlatformDo businessPlatformDo =
					businessMapper.findByBusinessCode(ConstantType.PLATFORM_CODE_HKTV).orElseThrow(() -> new SystemException("Business or platform doesn't exist"));

			if (saveProductRecordDo.getUploadType() == SaveProductType.BATCH_EDIT_DEPRECATED_PRODUCT_IMAGES) {
				log.info("uuid :{} is deprecated product, update product images only.", productMasterMqDto.getUuid());
				saveProductImages(userDto, productMasterMqDto, productDo, false);
				return generateResult(productMasterMqDto, errorMessageList);
			}
			SaveProductRecordRowDo saveProductRecordRowDo = saveProductRecordRowRepository.findById(productMasterMqDto.getRecordRowId())
				.orElseThrow(() -> new SystemException(messageSource.getMessage("message340", new String[]{productMasterMqDto.getRecordRowId().toString()}, null)));

			resetProductParameter(businessPlatformDo, productMasterMqDto, false);
			ProductAttributesDo productAttributesDo = saveProductAttribute(productDo.getAttributesId(), productMasterMqDto);
			resetUpdateProductParameter(productMasterMqDto, productDo);
			// check isPromotionChanged here, because method saveProduct will change productDo parameter
			HybrisSyncData hybrisSyncData = new HybrisSyncData();
			hybrisSyncData.setPromotionChanged(hybrisSyncData.isPromotionChanged(productMasterMqDto, productDo));

			updateProductPrimarySku(userDto, productMasterMqDto, productDo);
			ProductStoreStatusHistoryDo productStoreStatusHistoryDo = generateProductStoreStatusHistory(productDo, productMasterMqDto);

			productDo = saveProduct(userDto, productMasterMqDto, productDo, productAttributesDo);
			Pair<Boolean, Boolean> productStoreStatusPair = saveProductStoreStatus(userDto, productMasterMqDto, productDo);

			//if contract product term not match &  product is online originally, make this sku's parent bundle offline
			boolean isContractProductTermMatched = productStoreStatusPair.getLeft();
			boolean isOnlineToNoMatchOffline = productStoreStatusPair.getRight();
			if (!isContractProductTermMatched && isOnlineToNoMatchOffline) {
				log.warn("contract product term not match, make this sku's parent bundle offline, uuid :{}", productMasterMqDto.getUuid());
				updateRecordRowCheckOfflineBundleFlag(saveProductRecordRowDo);
			}

			List<String> oldImageUrlIdList = saveProductImages(userDto, productMasterMqDto, productDo, true);

			SaveVideoResultDto saveVideoResultDto = saveProductVideos(userDto, productMasterMqDto, productDo);
			if (!saveVideoResultDto.isSuccess()) {
				errorMessageList.add(messageSource.getMessage("message103", null, null));
			}

			saveProductOverseaDelivery(userDto, productMasterMqDto, productDo);
			saveProductBarcode(userDto, productMasterMqDto, productDo);
			saveCartonSizes(userDto, productMasterMqDto, productDo);

			if (Boolean.TRUE.equals(featureToggleSendMppsQueue)) {
				ProductMppsRabbitDto productMppsRabbitDto = generateProductMppRabbitDto(productMasterMqDto);
				sendProductMppsToRabbitMq(productDo, productMppsRabbitDto);
			}
			syncVariantProductCategories(userDto, businessPlatformDo, productDo, productMasterMqDto);

			if (saveVideoResultDto.isSuccess()) {
				if (!saveProductRecordRowDo.isNotSyncHybris()) {
					ExchangeRatePriceDto exchangeRatePriceDto = exchangeRateHelper.checkCurrencyAndGenerateHybrisPrice(productDo.getSkuCode(), saveProductData, productDo.getCurrencyCode(), productDo.getOriginalPrice(), productDo.getSellingPrice());
					hybrisSyncData = hybrisSyncData.generateUpdateSyncData(userDto, businessPlatformDo, productDo, productMasterMqDto, saveProductRecordDo.getUploadType(), productStoreStatusHistoryDo, exchangeRatePriceDto);
					SyncResponseDto<Void> syncHybrisResponse = syncProductToHybris(hybrisSyncData);
					isSyncHybrisSuccess = syncHybrisResponse.isSyncedTo(SyncService.HYBRIS);
					errorMessageList.addAll(syncHybrisResponse.getErrorMessageList());

					if (!isSyncHybrisSuccess) {
						log.error("Sync Hybris fail ,transaction rollback uuid :{},sku :{}", productMasterMqDto.getUuid(), productMasterMqDto.getSkuId());
						TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
					}
				} else {
					log.info("{} is variant sku sync, not sync hybris", productMasterMqDto.getUuid());
				}
			}

			// When product master sync status is successful, will delete the old videos and images
			if (isSyncHybrisSuccess) {
				SingleEditProductDto rowContent = gson.fromJson(saveProductRecordRowDo.getContent(), SingleEditProductDto.class);
				rowContent.setProductOldMultimedia(ProductOldMultimediaDto.builder()
					.userCode(userDto.getUserCode())
					.oldVideoFileNames(saveVideoResultDto.getOldVideoFileNameList())
					.oldImageUrlIds(oldImageUrlIdList)
					.build());
				saveProductRecordRowRepository.updateRowContentByRecordRowId(saveProductRecordRowDo.getId(), gson.toJson(rowContent));
			}
		} catch (NullPointerException e) {
			if (!isSyncHybrisSuccess) {
				TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			}
			errorMessageList.add(messageSource.getMessage("message10", new Object[]{ErrorMessageTypeCode.NULL_POINTER_EXCEPTION}, null));
			log.error(e.getMessage(), e);
		} catch (SystemException e) {
			if (!isSyncHybrisSuccess) {
				TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			}
			errorMessageList.add(e.getMessage());
			log.error(e.getMessage(), e);
		}

		ProductMasterReturnDto result = generateResult(productMasterMqDto, errorMessageList);
		if (StatusCodeEnum.FAIL.name().equals(result.getResult()) && isSyncHybrisSuccess) {
			result.setForceRollback(true);
		}
		return result;
	}

	private void updateRecordRowCheckOfflineBundleFlag(SaveProductRecordRowDo productRecordRowDo) {
		SingleEditProductDto singleEditProductDto = gson.fromJson(productRecordRowDo.getContent(), SingleEditProductDto.class);
		singleEditProductDto.getProduct().getAdditional().getHktv().setCheckOffLineBundle(true);
		saveProductRecordRowRepository.updateRowContentByRecordRowId(productRecordRowDo.getId(), gson.toJson(singleEditProductDto));
	}

	private ProductMasterReturnDto createBundle(UserDto userDto, ProductMasterMqDto productMasterMqDto, SaveProductRecordDo saveProductRecordDo, SaveProductData saveProductData) {
		List<String> errorMessageList = new ArrayList<>();
		try {
			//validating data
			ResponseDto<BundleValidationData> bundleValidationDataResult = validateAndPrepareProductRelatedData(productMasterMqDto);
			if (!bundleValidationDataResult.getData().getUnmatchedChildSkuIds().isEmpty()) {
				bundleValidationDataResult.getErrorMessageList().add(messageSource.getMessage("message166", new String[]{bundleValidationDataResult.getData().getUnmatchedChildSkuIds().toString()}, null));
			}
			if (!bundleValidationDataResult.getErrorMessageList().isEmpty()) {
				return generateResult(productMasterMqDto, errorMessageList);
			}
			BundleValidationData bundleValidationData = bundleValidationDataResult.getData();

			//processing image and video data
			Set<String> imageUrls = getAllPhotoUrls(productMasterMqDto);
			Set<String> videoUrls = getAllVideoUrls(productMasterMqDto);
			if (!imageUrls.isEmpty()) {
				HttpStatus httpStatus = productImageHelper.updateServerImageStatus(userDto.getUserCode(), productImageHelper.generateUrlId(new ArrayList<>(imageUrls)));
				if (httpStatus == null || httpStatus.isError()) {
					errorMessageList.add(messageSource.getMessage("message157", null, null));
				}
			}
			if (!videoUrls.isEmpty()) {
				boolean isSyncSuccess = productVideoHelper.requestUploadVideoStatus(userDto.getUserCode(), productImageHelper.generateUrlId(new ArrayList<>(videoUrls)));
				if (!isSyncSuccess) {
					errorMessageList.add(messageSource.getMessage("message103", null, null));
				}
			}

			//save bundle product
			BundleProductDo bundleProductDo = BundleProductDo.generateCreateBundleProductDo(userDto, productMasterMqDto, bundleValidationData, imageUrls, videoUrls);
			bundleProductRepository.save(bundleProductDo);

			//sync product to hybris
			ResponseDto<Void> syncHybrisResponse = syncBundleToHybris(ProductActionEnum.CREATE, userDto, bundleValidationData, productMasterMqDto, HybrisAction.getAction(saveProductRecordDo.getUploadType()), saveProductData);
			boolean isSyncHybrisSuccess = syncHybrisResponse.getStatus() == 1;
			errorMessageList.addAll(syncHybrisResponse.getErrorMessageList());

			if (!isSyncHybrisSuccess) {
				log.error("Sync Hybris fail ,transaction rollback uuid :{},sku :{}", productMasterMqDto.getUuid(), productMasterMqDto.getSkuId());
				TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			}
		} catch (NullPointerException e) {
			errorMessageList.add(messageSource.getMessage("message10", new Object[]{ErrorMessageTypeCode.NULL_POINTER_EXCEPTION}, null));
			log.error(e.getMessage(), e);
		} catch (SystemException e) {
			errorMessageList.add(e.getMessage());
			log.error(e.getMessage(), e);
		}
		return generateResult(productMasterMqDto, errorMessageList);
	}

	private ProductMasterReturnDto updateBundle(UserDto userDto, ProductMasterMqDto productMasterMqDto, BundleProductDo bundleProductDo, SaveProductRecordDo saveProductRecordDo, SaveProductData saveProductData) {
		List<String> errorMessageList = new ArrayList<>();
		try {
			//validating data
			ResponseDto<BundleValidationData> bundleValidationDataResult = validateAndPrepareProductRelatedData(productMasterMqDto) ;
			if(!bundleValidationDataResult.getErrorMessageList().isEmpty()) {
				return generateResult(productMasterMqDto, errorMessageList);
			}

			bundleProductDo.setBundleSetting(BundleProductBundleSettingData.generateBundleProductBundleSettingData(productMasterMqDto.getBundleSetting()));

			//processing image and video data
			BundleUrlData bundleUrlData = updateBundleProductImageAndVideoUrls(productMasterMqDto, bundleProductDo);

			if (!bundleUrlData.getNewImageUrls().isEmpty()) {
				HttpStatus httpStatus = productImageHelper.updateServerImageStatus(userDto.getUserCode(), productImageHelper.generateUrlId(bundleUrlData.getNewImageUrls()));
				if (httpStatus == null || httpStatus.isError()) {
					errorMessageList.add(messageSource.getMessage("message157", null, null));
				}
			}
			if (!bundleUrlData.getNewVideoUrls().isEmpty()) {
				boolean isSyncSuccess = productVideoHelper.requestUploadVideoStatus(userDto.getUserCode(), productImageHelper.generateUrlId(bundleUrlData.getNewVideoUrls()));
				if (!isSyncSuccess) {
					errorMessageList.add(messageSource.getMessage("message103", null, null));
				}
			}

			bundleProductDo.setLastUpdatedBy(userDto.getUserCode());
			bundleProductRepository.save(bundleProductDo);

			//sync product to hybris
			ResponseDto<Void> syncHybrisResponse = syncBundleToHybris(ProductActionEnum.UPDATE, userDto, bundleValidationDataResult.getData(), productMasterMqDto, HybrisAction.getAction(saveProductRecordDo.getUploadType()), saveProductData);
			boolean isSyncHybrisSuccess = syncHybrisResponse.getStatus() == 1;
			errorMessageList.addAll(syncHybrisResponse.getErrorMessageList());

			if (!isSyncHybrisSuccess) {
				log.error("Sync Hybris fail ,transaction rollback uuid :{},sku :{}", productMasterMqDto.getUuid(), productMasterMqDto.getSkuId());
				TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			}
		} catch (NullPointerException e) {
			errorMessageList.add(messageSource.getMessage("message10", new Object[]{ErrorMessageTypeCode.NULL_POINTER_EXCEPTION}, null));
			log.error(e.getMessage(), e);
		} catch (SystemException e) {
			errorMessageList.add(e.getMessage());
			log.error(e.getMessage(), e);
		}
		return generateResult(productMasterMqDto, errorMessageList);
	}

	BundleUrlData updateBundleProductImageAndVideoUrls(ProductMasterMqDto productMasterMqDto, BundleProductDo bundleProductDo) {
		Set<String> lastImageUrls = bundleProductDo.getProductInfo().getImageUrls().getCurrentUrls();
		Set<String> lastVideoUrls = bundleProductDo.getProductInfo().getVideoUrls().getCurrentUrls();
		lastImageUrls.addAll(bundleProductDo.getProductInfo().getImageUrls().getLastUrls());
		lastVideoUrls.addAll(bundleProductDo.getProductInfo().getVideoUrls().getLastUrls());
		Set<String> currentImageUrls = getAllPhotoUrls(productMasterMqDto);
		Set<String> currentVideoUrls = getAllVideoUrls(productMasterMqDto);

		//find need deleted urls
		lastImageUrls.removeAll(currentImageUrls);
		lastVideoUrls.removeAll(currentVideoUrls);

		//find newly added urls
		List<String> newAddImageUrls = new ArrayList<>(currentImageUrls);
		newAddImageUrls.removeAll(lastImageUrls);
		List<String> newAddVideoUrls = new ArrayList<>(currentVideoUrls);
		newAddVideoUrls.removeAll(lastVideoUrls);

		//save bundle product
		bundleProductDo.getProductInfo().getImageUrls().setCurrentUrls(currentImageUrls);
		bundleProductDo.getProductInfo().getImageUrls().setLastUrls(lastImageUrls);
		bundleProductDo.getProductInfo().getVideoUrls().setCurrentUrls(currentVideoUrls);
		bundleProductDo.getProductInfo().getVideoUrls().setLastUrls(lastVideoUrls);

		return BundleUrlData.builder()
			.newImageUrls(newAddImageUrls)
			.newVideoUrls(newAddVideoUrls)
			.build();
	}

	private ResponseDto<BundleValidationData> validateAndPrepareProductRelatedData(ProductMasterMqDto productMasterMqDto) {
		List<String> errorMessageList = new ArrayList<>();
		BusinessPlatformDo businessPlatformDo = businessMapper.findByBusinessCode(BuCodeEnum.HKTV.name()).orElse(null);
		if (businessPlatformDo == null) {
			errorMessageList.add(messageSource.getMessage("message159", null, null));
		}

		ContractDo contractDo = contractRepository.findById(productMasterMqDto.getContractNo()).orElse(null);
		if (contractDo == null) {
			errorMessageList.add(messageSource.getMessage("message14", null, null));
		}

		StoreDo storeDo = storeRepository.findHktvStoreByStoreCode(productMasterMqDto.getStores()).orElse(null);
		if (storeDo == null) {
			errorMessageList.add(messageSource.getMessage("message69", null, null));
		}

		//if bundle is online, check child sku status
		List<String> unmatchedChildSkuIds = List.of();
		if (OnlineStatusEnum.ONLINE == productMasterMqDto.getOnlineStatus()) {
			List<String> childSkuUuids = productMasterMqDto.getBundleSetting().getChildSkuInfo().stream()
				.map(BundleChildSkuDto::getUuid)
				.collect(Collectors.toList());
			unmatchedChildSkuIds = productStorePromotionMapper.findProductStatusByUuids(childSkuUuids).stream()
				.filter(Predicate.not(skuStatus -> ProductContractMatchStatusEnum.MATCH.getCode().equals(skuStatus.getStatus())))
				.map(ProductStatusDto::getSkuCode)
				.collect(Collectors.toList());
		}

		BundleValidationData bundleValidationData = BundleValidationData.builder()
			.businessPlatformDo(businessPlatformDo)
			.contractDo(contractDo)
			.storeDo(storeDo)
			.unmatchedChildSkuIds(unmatchedChildSkuIds)
			.build();

		return ResponseDto.generate(bundleValidationData, errorMessageList);
	}

	Set<String> getAllPhotoUrls(ProductMasterMqDto productMasterMqDto) {
		Set<String> photoUrls = new HashSet<>();
		if (productMasterMqDto.getMainPhoto() != null) {
			photoUrls.add(productMasterMqDto.getMainPhoto());
		}
		if (productMasterMqDto.getAdvertisingPhoto() != null) {
			photoUrls.add(productMasterMqDto.getAdvertisingPhoto());
		}
		if (productMasterMqDto.getOtherPhoto() != null) {
			photoUrls.addAll(productMasterMqDto.getOtherPhoto());
		}
		if (productMasterMqDto.getVariantProductPhoto() != null) {
			photoUrls.addAll(productMasterMqDto.getVariantProductPhoto());
		}
		return photoUrls;
	}

	Set<String> getAllVideoUrls(ProductMasterMqDto productMasterMqDto) {
		Set<String> videoUrls = new HashSet<>();
		if (productMasterMqDto.getMainVideo() != null) {
			videoUrls.add(productMasterMqDto.getMainVideo());
		}
		if (productMasterMqDto.getThumbnailVideo() != null) {
			videoUrls.add(productMasterMqDto.getThumbnailVideo());
		}
		return videoUrls;
	}

	@Transactional
	public ProductMasterReturnDto updateProductOffline(UserDto userDto, ProductMasterMqDto productMasterMqDto) {
		long startTime = System.currentTimeMillis();
		List<String> errorMessageList = new ArrayList<>();

		//normal product
		if (productMasterMqDto.getBundleSetting() == null) {
			Optional<ProductDo> productDoOpt = productRepository.findByUuid(productMasterMqDto.getUuid());
			BusinessPlatformDo businessPlatformDo =
				businessMapper.findByBusinessCode(BuCodeEnum.HKTV.name()).orElseThrow(() -> new SystemException("Business or platform doesn't exist"));
			if (productDoOpt.isPresent()) {
				ResponseDto<Void> syncHybrisResponse = offlineProductToHybris(userDto, businessPlatformDo, productDoOpt.get(), productMasterMqDto, HybrisAction.PRODUCT_SYNC_MODE_ONOFFLINE);
				errorMessageList.addAll(syncHybrisResponse.getErrorMessageList());
			} else {
				errorMessageList.add(messageSource.getMessage("message13", null, null));
			}

		//bundle product
		} else {
			Optional<BundleProductDo> bundleProductDoOpt = bundleProductRepository.findByUuid(productMasterMqDto.getUuid());
			if (bundleProductDoOpt.isPresent()) {
				log.info("offline bundle sku [{}]", productMasterMqDto.getUuid());
				ResponseDto<Void> syncHybrisResponse = offlineBundleToHybris(userDto, bundleProductDoOpt.get(), productMasterMqDto);
				errorMessageList.addAll(syncHybrisResponse.getErrorMessageList());
			} else {
				errorMessageList.add(messageSource.getMessage("message164", new String[]{productMasterMqDto.getUuid()}, null));
			}
		}

		long endTime = System.currentTimeMillis();
		log.info("Time taken by SaveProductHelper updateProductOffline method : {} milliseconds", (endTime - startTime));
		return generateResult(productMasterMqDto, errorMessageList);
	}

	private ProductMppsRabbitDto generateProductMppRabbitDto(ProductMasterMqDto productMasterMqDto) {
		if (!ALLOW_TO_MPPS_PRODUCT_READY_METHOD_LIST.contains(productMasterMqDto.getProductReadyMethod())) {
			log.info("Product ready method not match do not send mpps, product ready method : {}", productMasterMqDto.getProductReadyMethod());
			return null;
		}

		MppsProductDo mppsProductDo = productMapper.findMppsProduct(productMasterMqDto.getUuid(), productMasterMqDto.getSkuId(), 85);

		String newSkuName = productMasterMqDto.getSkuNameEn();
		String newSkuNameTchi = productMasterMqDto.getSkuNameCh();
		BigDecimal newWeight = Optional.of(productMasterMqDto.getWeight()).orElse(new BigDecimal(0));
		String newWeightUnit = productMasterMqDto.getWeightUnit();
		BigDecimal newPackHeight = Optional.of(productMasterMqDto.getPackingHeight()).orElse(new BigDecimal(0));
		BigDecimal newPackLength = Optional.of(productMasterMqDto.getPackingLength()).orElse(new BigDecimal(0));
		BigDecimal newPackDepth = Optional.of(productMasterMqDto.getPackingDepth()).orElse(new BigDecimal(0));
		String newPackDimensionUnit = productMasterMqDto.getPackingDimensionUnit();
		List<String> newBarcodeList = productMasterMqDto.getBarcodes().stream().map(ProductBarcodeDto::getEan).collect(Collectors.toList());
		String newImage = productMasterMqDto.getMainPhoto();
		String newVideo = productMasterMqDto.getMainVideo();

		ProductMppsRabbitSkuDto productMppsRabbitSkuDto = new ProductMppsRabbitSkuDto();
		ProductMppsRabbitDto productMppsRabbitDto = new ProductMppsRabbitDto();
		List<ProductMppsRabbitSkuDto> productMppsRabbitSkuDtoList = new ArrayList<>();
		productMppsRabbitSkuDto.setStore_code(mppsProductDo.getStoreCode());
		productMppsRabbitSkuDto.setSku_id(mppsProductDo.getSkuId());
		productMppsRabbitSkuDto.setSku_name_zh(newSkuNameTchi);
		productMppsRabbitSkuDto.setSku_name_en(newSkuName);
		productMppsRabbitSkuDto.setMain_photo(newImage);
		productMppsRabbitSkuDto.setMain_video(newVideo);
		productMppsRabbitSkuDto.setWeight(newWeight);
		productMppsRabbitSkuDto.setWeight_unit(newWeightUnit);
		productMppsRabbitSkuDto.setPacking_height(newPackHeight);
		productMppsRabbitSkuDto.setPacking_length(newPackLength);
		productMppsRabbitSkuDto.setPacking_depth(newPackDepth);
		productMppsRabbitSkuDto.setPacking_dimension_unit(newPackDimensionUnit);
		productMppsRabbitSkuDto.setBarcode(newBarcodeList);
		productMppsRabbitSkuDto.setFetch_time(System.currentTimeMillis());
		productMppsRabbitSkuDtoList.add(productMppsRabbitSkuDto);
		productMppsRabbitDto.setData(productMppsRabbitSkuDtoList);
		return productMppsRabbitDto;
	}

	private void resetProductParameter(
			BusinessPlatformDo businessPlatformDo, ProductMasterMqDto productMasterMqDto, boolean isNewProduct) {
		if (isNewProduct) {
			List<SysParmDo> productReadyDayList = sysParmRepository.findBySegmentAndPlatformId(
					"PRODUCT_READY_DAYS_DEFAULT", businessPlatformDo.getPlatformId());
			String productReadyDays = productMasterMqDto.getProductReadyDays();
			for (SysParmDo productReadyDay : productReadyDayList) {
				if (productReadyDay.getCode().equalsIgnoreCase(productMasterMqDto.getProductReadyMethod())) {
					productReadyDays = productReadyDay.getParmValue();
					break;
				}
			}
			productMasterMqDto.setProductReadyDays(productReadyDays);
		}

		if (!"RELATIVELY".equalsIgnoreCase(productMasterMqDto.getExpiryType())) {
			productMasterMqDto.setUponPurchaseDate(null);
		}
	}

	public void resetUpdateProductParameter(
			ProductMasterMqDto productMasterMqDto, ProductDo productDo) {
		//檢查product是否有promotion，有的話不能更新(refrence from hktv-mms resetBasePromotion func)
		if (productStorePromotionMapper.countProductStorePromotionByUuId(productMasterMqDto.getUuid()) > 0) {
			boolean productDoFlag = productDo != null;
			productMasterMqDto.setMallDollar(productDoFlag ? productDo.getMallDollar() : null);
			productMasterMqDto.setVipMallDollar(productDoFlag ? productDo.getMallDollarVip() : null);
			productMasterMqDto.setSellingPrice(productDoFlag ? productDo.getSellingPrice() : null);
			productMasterMqDto.setStyle(productDoFlag ? productDo.getStyle() : null);
			productMasterMqDto.setDiscountTextCh(productDoFlag ? productDo.getDiscountTextTchi() : null);
			productMasterMqDto.setDiscountTextEn(productDoFlag ? productDo.getDiscountText() : null);
			productMasterMqDto.setDiscountTextSc(productDoFlag ? productDo.getDiscountTextSchi() : null);
		}
	}

	private void updateProductPrimarySku(UserDto userDto, ProductMasterMqDto productMasterMqDto, ProductDo productDo) {
		if (productDo.getIsPrimarySku().equals(productMasterMqDto.getIsPrimarySku())) {
			return;
		}
		List<ProductDo> productList = productRepository.findByProductCodeAndMerchantIdAndContractId(
				productMasterMqDto.getProductId(), productMasterMqDto.getMerchantId(), productMasterMqDto.getContractNo()
		);
		List<ProductDo> changePrimarySkuProductList = new ArrayList<>();
		for (ProductDo product : productList) {
			if (product.getIsPrimarySku().equals("Y")) {
				product.setIsPrimarySku("N");
				product.setLastUpdatedBy(userDto.getUserCode());
				product.setLastUpdatedDate(new Date());
				changePrimarySkuProductList.add(product);
			}
		}
		if (CollectionUtil.isNotEmpty(changePrimarySkuProductList)) {
			productRepository.saveAll(changePrimarySkuProductList);
		}
	}

	private ProductAttributesDo generateProductAttributesDo(Integer productAttributeId, ProductMasterMqDto productMasterMqDto) {
		ProductAttributesDo productAttributesDo = null;
		if (productAttributeId != null) {
			productAttributesDo = productAttributeRepository.findById(productAttributeId).orElse(null);
		}
		if (productAttributesDo == null) {
			productAttributesDo = new ProductAttributesDo();
		}
		productAttributesDo.setGoodsType(productMasterMqDto.getGoodsType());
		productAttributesDo.setWarrantyPeriodUnit(productMasterMqDto.getWarrantyPeriodUnit());
		productAttributesDo.setWarrantyPeriod(productMasterMqDto.getWarrantyPeriod());
		productAttributesDo.setWarrantyRemarkCh(productMasterMqDto.getWarrantyRemarkCh());
		productAttributesDo.setWarrantyRemarkEn(productMasterMqDto.getWarrantyRemarkEn());
		productAttributesDo.setWarrantyRemarkSc(productMasterMqDto.getWarrantyRemarkSc());
		productAttributesDo.setWarrantySupplierCh(productMasterMqDto.getWarrantySupplierCh());
		productAttributesDo.setWarrantySupplierEn(productMasterMqDto.getWarrantySupplierEn());
		productAttributesDo.setWarrantySupplierSc(productMasterMqDto.getWarrantySupplierSc());
		productAttributesDo.setServiceCentreAddressCh(productMasterMqDto.getServiceCentreAddressCh());
		productAttributesDo.setServiceCentreAddressEn(productMasterMqDto.getServiceCentreAddressEn());
		productAttributesDo.setServiceCentreAddressSc(productMasterMqDto.getServiceCentreAddressSc());
		productAttributesDo.setServiceCentreContact(productMasterMqDto.getServiceCentreContact());
		productAttributesDo.setServiceCentreEmail(productMasterMqDto.getServiceCentreEmail());
		productAttributesDo.setVoucherTemplateType(productMasterMqDto.getVoucherTemplateType());
		productAttributesDo.setMinimumShelfLife(productMasterMqDto.getMinimumShelfLife());
		productAttributesDo.setVirtualStore(productMasterMqDto.getVirtualStore());
		productAttributesDo.setRmCode(productMasterMqDto.getRmCode());
		productAttributesDo.setPreSellFruit(productMasterMqDto.getPreSellFruit());
		productAttributesDo.setPhysicalStore(productMasterMqDto.getPhysicalStore());
		productAttributesDo.setStorageType(productMasterMqDto.getStorageType());
		productAttributesDo.setVideoLink2(productMasterMqDto.getVideoLink2());
		productAttributesDo.setVideoLinkEn2(productMasterMqDto.getVideoLinkTextEn2());
		productAttributesDo.setVideoLinkCh2(productMasterMqDto.getVideoLinkTextCh2());
		productAttributesDo.setVideoLinkSc2(productMasterMqDto.getVideoLinkTextSc2());
		productAttributesDo.setVideoLink3(productMasterMqDto.getVideoLink3());
		productAttributesDo.setVideoLinkEn3(productMasterMqDto.getVideoLinkTextEn3());
		productAttributesDo.setVideoLinkCh3(productMasterMqDto.getVideoLinkTextCh3());
		productAttributesDo.setVideoLinkSc3(productMasterMqDto.getVideoLinkTextSc3());
		productAttributesDo.setVideoLink4(productMasterMqDto.getVideoLink4());
		productAttributesDo.setVideoLinkEn4(productMasterMqDto.getVideoLinkTextEn4());
		productAttributesDo.setVideoLinkCh4(productMasterMqDto.getVideoLinkTextCh4());
		productAttributesDo.setVideoLinkSc4(productMasterMqDto.getVideoLinkTextSc4());
		productAttributesDo.setVideoLink5(productMasterMqDto.getVideoLink5());
		productAttributesDo.setVideoLinkEn5(productMasterMqDto.getVideoLinkTextEn5());
		productAttributesDo.setVideoLinkCh5(productMasterMqDto.getVideoLinkTextCh5());
		productAttributesDo.setVideoLinkSc5(productMasterMqDto.getVideoLinkTextSc5());

		if (Optional.of(productMasterMqDto)
			.map(ProductMasterMqDto::getPartnerInfo)
			.map(ProductPartnerInfoDto::getEveruts)
			.isPresent()) {
			productAttributesDo.setEverutsBuyerId(productMasterMqDto.getPartnerInfo().getEveruts().getBuyerId());
			productAttributesDo.setEverutsSkuId(productMasterMqDto.getPartnerInfo().getEveruts().getSkuId());
		}
		return productAttributesDo;
	}

	private ProductAttributesDo saveProductAttribute(Integer productAttributeId, ProductMasterMqDto productMasterMqDto) {
		ProductAttributesDo productAttributesDo = generateProductAttributesDo(productAttributeId, productMasterMqDto);
		return productAttributeRepository.save(productAttributesDo);
	}

	private ProductDo saveProduct(UserDto userDto, ProductMasterMqDto productMasterMqDto, ProductDo productDo, ProductAttributesDo productAttributesDo) {
		productDo = generateProductDo(userDto, productMasterMqDto, productDo, productAttributesDo);
		return productRepository.save(productDo);
	}

	private ProductDo generateProductDo(UserDto userDto, ProductMasterMqDto productMasterMqDto, ProductDo productDo, ProductAttributesDo productAttributesDo) {
		if (productDo == null) {
			productDo = new ProductDo();
			productDo.setCreatedBy(userDto.getUserCode());
			productDo.setCreatedDate(new Date());
		}
		productDo.setUuid(productMasterMqDto.getUuid());
		productDo.setLastUpdatedDate(new Date());
		productDo.setLastUpdatedBy(userDto.getUserCode());
		setPriceRelatedField(productMasterMqDto, productDo);
		if (productMasterMqDto.getMerchantId() != null) {
			productDo.setMerchantId(productMasterMqDto.getMerchantId());
		}
		// 這個productId是DB的productCode
		if (productMasterMqDto.getProductId() != null) {
			productDo.setProductCode(productMasterMqDto.getProductId());
		}
		if (productMasterMqDto.getContractNo() != null) {
			productDo.setContractId(productMasterMqDto.getContractNo());

			if (productDo.getId() == null) {
				int count = contractRepository.countIsBCContract(productMasterMqDto.getContractNo());
				if (count > 0) {
					productDo.setStatus(ProductStatus.DRAFT);
				}
			}
		}
		if (productMasterMqDto.getSkuId() != null) {
			productDo.setSkuCode(productMasterMqDto.getSkuId());
		}
		if (productMasterMqDto.getIsPrimarySku() != null) {
			productDo.setIsPrimarySku(productMasterMqDto.getIsPrimarySku());
		}
		if (productMasterMqDto.getSkuNameEn() != null) {
			productDo.setSkuName(productMasterMqDto.getSkuNameEn());
		}
		if (productMasterMqDto.getSkuNameCh() != null) {
			productDo.setSkuNameTchi(productMasterMqDto.getSkuNameCh());
		}
		if (productMasterMqDto.getSkuNameSc() != null) {
			productDo.setSkuNameSchi(productMasterMqDto.getSkuNameSc());
		}
		if (productMasterMqDto.getSkuShortDescriptionEn() != null) {
			productDo.setSkuSDescHktvEn(productMasterMqDto.getSkuShortDescriptionEn());
		}
		if (productMasterMqDto.getSkuShortDescriptionCh() != null) {
			productDo.setSkuSDescHktvCh(productMasterMqDto.getSkuShortDescriptionCh());
		}
		if (productMasterMqDto.getSkuShortDescriptionSc() != null) {
			productDo.setSkuSDescHktvSc(productMasterMqDto.getSkuShortDescriptionSc());
		}
		if (productMasterMqDto.getSkuLongDescriptionEn() != null) {
			productDo.setSkuLDescHktvEn(productMasterMqDto.getSkuLongDescriptionEn());
		}
		if (productMasterMqDto.getSkuLongDescriptionCh() != null) {
			productDo.setSkuLDescHktvCh(productMasterMqDto.getSkuLongDescriptionCh());
		}
		if (productMasterMqDto.getSkuLongDescriptionSc() != null) {
			productDo.setSkuLDescHktvSc(productMasterMqDto.getSkuLongDescriptionSc());
		}
		productDo.setInvoiceRemarksEn(productMasterMqDto.getInvoiceRemarksEn());
		productDo.setInvoiceRemarksCh(productMasterMqDto.getInvoiceRemarksCh());
		productDo.setInvoiceRemarksSc(productMasterMqDto.getInvoiceRemarksSc());
		productDo.setVideoLink(productMasterMqDto.getVideoLink());
		productDo.setVideoLinkEn(productMasterMqDto.getVideoLinkTextEn());
		productDo.setVideoLinkCh(productMasterMqDto.getVideoLinkTextCh());
		productDo.setVideoLinkSc(productMasterMqDto.getVideoLinkTextSc());
		if (productMasterMqDto.getBrandId() != null) {
			productDo.setBrandId(productMasterMqDto.getBrandId());
		}
		if (CollectionUtil.isNotEmpty(productMasterMqDto.getBarcodes())) {
			productDo.setBarcode(productMasterMqDto.getBarcodes().stream().filter(productBarcodeDto -> productBarcodeDto.getSequenceNo() == 1).map(ProductBarcodeDto::getEan).findFirst().orElse(""));
		}
		if (productMasterMqDto.getManufacturedCountry() != null) {
			productDo.setManuCountry(productMasterMqDto.getManufacturedCountry());
		}
		productDo.setWeight(productMasterMqDto.getWeight());
		productDo.setWeightUnit(productMasterMqDto.getWeightUnit());
		productDo.setPackHeight(productMasterMqDto.getPackingHeight());
		productDo.setPackLength(productMasterMqDto.getPackingLength());
		productDo.setPackDepth(productMasterMqDto.getPackingDepth());
		productDo.setPackDimensionUnit(productMasterMqDto.getPackingDimensionUnit());
		productDo.setPackBoxType(productMasterMqDto.getPackingBoxType());
		productDo.setPackSpecEn(productMasterMqDto.getPackingSpecEn());
		productDo.setPackSpecCh(productMasterMqDto.getPackingSpecCh());
		productDo.setPackSpecSc(productMasterMqDto.getPackingSpecSc());
		productDo.setCurrencyCode(productMasterMqDto.getCurrency());
		if (productMasterMqDto.getMallDollar() != null) {
			productDo.setMallDollar(productMasterMqDto.getMallDollar());
		}
		if (productMasterMqDto.getVipMallDollar() != null) {
			productDo.setMallDollarVip(productMasterMqDto.getVipMallDollar());
		}
		if (productMasterMqDto.getProductReadyMethod() != null) {
			productDo.setProductReadyMethod(productMasterMqDto.getProductReadyMethod());
		}
		if (productMasterMqDto.getDeliveryMethod() != null) {
			productDo.setDeliveryMethod(productMasterMqDto.getDeliveryMethod());
		}
		try {
			// convert yyyy-MM-dd'T'HH:mm:ss to yyyy-MM-dd HH:mm:ss
			String redeemEndDate = productSimpleDateFormat.format(productMasterSimpleDateFormat.parse(productMasterMqDto.getFixedRedemptionDate()));
			Date date = parseStringIntoDate(redeemEndDate, productSimpleDateFormat);
			productDo.setRedeemEndDate(date);
		} catch (Exception e) {
			productDo.setRedeemEndDate(null);
		}

		if (productMasterMqDto.getPickupDays() != null) {
			productDo.setPickupDays(productMasterMqDto.getPickupDays());
		}
		if (productMasterMqDto.getPickupTimeslot() != null) {
			productDo.setPickupTimeslot(productMasterMqDto.getPickupTimeslot());
		}
		if (productMasterMqDto.getReturnDays() != null) {
			productDo.setReturnDays(productMasterMqDto.getReturnDays());
		}
		if (productMasterMqDto.getProductReadyDays() != null) {
			productDo.setProductReadyDays(productMasterMqDto.getProductReadyDays());
		}
		String colorEn = getColorEnOrCh(productMasterMqDto.getColor());
		productDo.setColorEn(colorEn);
		List<SysParmDo> sysParmList = sysParmRepository.findBySegmentAndCode(SysParmSegmentEnum.COLOR.name(), colorEn);
		productDo.setColorCh(sysParmList.stream()
				.findFirst()
				.map(SysParmDo::getShortDescTc)
				.orElse(null));
		productDo.setColorFamiliar(productMasterMqDto.getColourFamilies());
		productDo.setSize(productMasterMqDto.getSize());
		productDo.setSizeSystem(productMasterMqDto.getSizeSystem());
		productDo.setInvisibleFlag(StringUtil.toggleYN(productMasterMqDto.getVisibility()));

		try {
			// convert yyyy-MM-dd'T'HH:mm:ss to yyyy-MM-dd HH:mm:ss
			String featureStartTime = productSimpleDateFormat.format(productMasterSimpleDateFormat.parse(productMasterMqDto.getFeatureStartTime()));
			Date featureEndDate = parseStringIntoDate(featureStartTime, productSimpleDateFormat);
			productDo.setFeatureStartTime(featureEndDate);
		} catch (Exception e) {
			productDo.setFeatureStartTime(null);
		}
		try {
			// convert yyyy-MM-dd'T'HH:mm:ss to yyyy-MM-dd HH:mm:ss
			String featureEndTime = productSimpleDateFormat.format(productMasterSimpleDateFormat.parse(productMasterMqDto.getFeatureEndTime()));
			Date featureEndDate = parseStringIntoDate(featureEndTime, productSimpleDateFormat);
			productDo.setFeatureEndTime(featureEndDate);
		} catch (Exception e) {
			productDo.setFeatureEndTime(null);
		}
		if (productMasterMqDto.getVoucherType() != null) {
			productDo.setVoucherType(productMasterMqDto.getVoucherType());
		}
		if (productMasterMqDto.getVoucherDisplayType() != null) {
			productDo.setVoucherDisplayType(productMasterMqDto.getVoucherDisplayType());
		}
		productDo.setUserMax(productMasterMqDto.getUserMax() != null ? new BigDecimal(productMasterMqDto.getUserMax()) : null);
		try {
			// convert yyyy-MM-dd'T'HH:mm:ss to yyyy-MM-dd HH:mm:ss
			String redeemStartDateString = productSimpleDateFormat.format(productMasterSimpleDateFormat.parse(productMasterMqDto.getRedeemStartDate()));
			Date redeemStartDate = parseStringIntoDate(redeemStartDateString, productSimpleDateFormat);
			productDo.setRedeemStartDate(redeemStartDate);
		} catch (Exception e) {
			productDo.setRedeemStartDate(null);
		}
		if (productMasterMqDto.getUrgent() != null) {
			productDo.setUrgent(productMasterMqDto.getUrgent());
		}
		if (productMasterMqDto.getExpiryType() != null) {
			productDo.setExpiryType(productMasterMqDto.getExpiryType());
		}
		if (productMasterMqDto.getUponPurchaseDate() != null) {
			productDo.setUponPurchaseDate(productMasterMqDto.getUponPurchaseDate());
		}
		productDo.setFinePrintEn(productMasterMqDto.getFinePrintEn());
		productDo.setFinePrintCh(productMasterMqDto.getFinePrintCh());
		productDo.setFinePrintSc(productMasterMqDto.getFinePrintSc());
		if (productMasterMqDto.getNeedRemovalServices() != null) {
			productDo.setRemovalServices(productMasterMqDto.getNeedRemovalServices());
		}
		if (productMasterMqDto.getCost() != null) {
			productDo.setCost(productMasterMqDto.getCost());
		}
		productDo.setField1(productMasterMqDto.getOption1());
		productDo.setValue1(productMasterMqDto.getOption1Value());
		productDo.setField2(productMasterMqDto.getOption2());
		productDo.setValue2(productMasterMqDto.getOption2Value());
		productDo.setField3(productMasterMqDto.getOption3());
		productDo.setValue3(productMasterMqDto.getOption3Value());
		productDo.setAffiliateUrl(productMasterMqDto.getAffiliateUrl());
		productDo.setStorageTemperatureCode(productMasterMqDto.getStorageTemperature());
		productDo.setAttributesId(productAttributesDo.getId());
		productDo.setEwPercentageSetting(productMasterMqDto.getEwPercentageSetting());
		productDo.setClaimLinkEn(productMasterMqDto.getClaimLinkEn());
		productDo.setClaimLinkCh(productMasterMqDto.getClaimLinkCh());
		productDo.setClaimLinkSc(productMasterMqDto.getClaimLinkSc());
		return productDo;
	}

	public void setPriceRelatedField(ProductMasterMqDto productMasterMqDto, ProductDo productDo) {
		if (productMasterMqDto.getOriginalPrice() != null) {
			productDo.setOriginalPrice(productMasterMqDto.getOriginalPrice());
		}
		if (productMasterMqDto.getSellingPrice() != null) {
			productDo.setSellingPrice(productMasterMqDto.getSellingPrice());
		}
		if (productMasterMqDto.getDiscountTextEn() != null) {
			productDo.setDiscountText(productMasterMqDto.getDiscountTextEn());
		}
		if (productMasterMqDto.getDiscountTextCh() != null) {
			productDo.setDiscountTextTchi(productMasterMqDto.getDiscountTextCh());
		}
		if (productMasterMqDto.getDiscountTextSc() != null) {
			productDo.setDiscountTextSchi(productMasterMqDto.getDiscountTextSc());
		}
		if (productMasterMqDto.getStyle() != null) {
			productDo.setStyle(productMasterMqDto.getStyle());
		}
	}

	private void saveProductBuStatus(UserDto userDto, BusinessPlatformDo businessPlatformDo, ProductDo productDo) {
		// 因為只使用在新 product，無 id 屬例外狀況，不該存其他資料
		if (productDo.getId() == null)
			throw new ServiceException("Save product error.");
		ProductBuStatusDo productBuStatusDo = new ProductBuStatusDo();
		productBuStatusDo.setBusUnitId(businessPlatformDo.getBusinessId());
		productBuStatusDo.setProductId(productDo.getId());
		productBuStatusDo.setStatus("A");
		productBuStatusDo.setCreatedBy(userDto.getUserCode());
		productBuStatusDo.setCreatedDate(new Date());
		productBuStatusDo.setLastUpdatedBy(userDto.getUserCode());
		productBuStatusDo.setLastUpdatedDate(new Date());
		productBuStatusRepository.save(productBuStatusDo);
	}

	private Pair<Boolean, Boolean> saveProductStoreStatus(UserDto userDto, ProductMasterMqDto productMasterMqDto, ProductDo productDo) {
		ProductStoreStatusDo productStoreStatusDo;
		boolean isOnlineToNoMatchOffline = false;
		if (productMasterMqDto.getStores() != null) {
			StoreDo storeDo = storeRepository.findHktvStoreByStoreCode(productMasterMqDto.getStores()).orElse(null);
			if (storeDo == null) {
				return Pair.of(false, false);
			}

			Pair<ProductStoreStatusDo, String> pair = generateProductStoreStatusDo(userDto, productMasterMqDto, productDo, storeDo);
			productStoreStatusDo = pair.getLeft();
			String originalOnlineStatus = pair.getRight();
			if(OnlineStatusEnum.ONLINE.name().equals(originalOnlineStatus) &&
				ProductContractMatchStatusEnum.NO_MATCH.getCode().equals(productStoreStatusDo.getStatus())) {
				isOnlineToNoMatchOffline = true;
			}

			productStoreStatusRepository.save(productStoreStatusDo);
		} else {
			return Pair.of(false, false);
		}
		return Pair.of(ProductContractMatchStatusEnum.MATCH.getCode().equals(productStoreStatusDo.getStatus()), isOnlineToNoMatchOffline);
	}

	private Pair<ProductStoreStatusDo, String> generateProductStoreStatusDo(UserDto userDto, ProductMasterMqDto productMasterMqDto, ProductDo productDo, StoreDo storeDo) {

		Integer storeWarehouseId = productMasterMqDto.getWarehouseId();
		List<ProductStoreStatusDo> productStoreStatusList =
			productStoreStatusRepository.findByUuIdAndStoreIdAndStoreSkuId(productMasterMqDto.getUuid(), storeDo.getId(), productMasterMqDto.getStoreSkuId());
		ProductStoreStatusDo productStoreStatusDo;
		if (productStoreStatusList.isEmpty()) {
			productStoreStatusDo = new ProductStoreStatusDo();
			productStoreStatusDo.setCreatedBy(userDto.getUserCode());
			productStoreStatusDo.setCreatedDate(new Date());
		} else {
			productStoreStatusDo = productStoreStatusList.get(0);
		}
		String originalOnlineStatus = productStoreStatusDo.getOnlineStatus();
		productStoreStatusDo.setLastUpdatedBy(userDto.getUserCode());
		productStoreStatusDo.setLastUpdatedDate(new Date());
		productStoreStatusDo.setStoreWarehouseId(storeWarehouseId);
		productStoreStatusDo.setStoreId(storeDo.getId());
		String storeFrontStoreCode = storeDo.getStorefrontStoreCode();
		productStoreStatusDo.setStoreSkuId(storeFrontStoreCode + "_S_" + productMasterMqDto.getSkuId());
		productStoreStatusDo.setProductId(productDo.getId());
		productStoreStatusDo.setShareStockUuid(productDo.getUuid());

		ContractProdTermsDo contractProductTerm = findContractProductTerm(
			productMasterMqDto.getContractNo(), productStoreStatusDo.getStoreId(), productMasterMqDto.getPrimaryCategoryCode(), productMasterMqDto.getProductReadyMethod(),
			productMasterMqDto.getBrandId(), productMasterMqDto.getSkuId(), productMasterMqDto.getTermName());

		if (contractProductTerm != null) {
			log.info("sku：{}, storeSkuId：{}, contractProductTermId：{}, commissionRate：{}",
				productMasterMqDto.getSkuId(), productMasterMqDto.getStoreSkuId(), contractProductTerm.getId(), contractProductTerm.getCommissionRate());
			productStoreStatusDo.setStatus("Matched");
			productStoreStatusDo.setOnlineStatus(productMasterMqDto.getOnlineStatus().name());
			productStoreStatusDo.setCommissionRate(contractProductTerm.getCommissionRate());
			productStoreStatusDo.setContractProdTermsId(contractProductTerm.getId());
		} else {
			productStoreStatusDo.setStatus("NoMatched");
			productStoreStatusDo.setOnlineStatus(OnlineStatusEnum.OFFLINE.name());
		}

		return Pair.of(productStoreStatusDo, originalOnlineStatus);
	}

	private ProductStoreStatusDo generateBundleProductStoreStatusDo(UserDto userDto, ProductMasterMqDto productMasterMqDto, ProductDo productDo, BundleValidationData bundleValidationData) {
		ProductStoreStatusDo productStoreStatusDo = generateProductStoreStatusDo(userDto, productMasterMqDto, productDo, bundleValidationData.getStoreDo()).getLeft();

		if (bundleValidationData.getUnmatchedChildSkuIds().isEmpty()) {
			productStoreStatusDo.setStatus(ProductContractMatchStatusEnum.MATCH.getCode());
			productStoreStatusDo.setOnlineStatus(productMasterMqDto.getOnlineStatus().name());
			productStoreStatusDo.setCommissionRate(BigDecimal.ONE); //set default commission rate
		} else {
			log.warn("bundle sku [{}] contain unmatched child sku [{}], force offline", productMasterMqDto.getUuid(), bundleValidationData.getUnmatchedChildSkuIds());
			productStoreStatusDo.setStatus(ProductContractMatchStatusEnum.NO_MATCH.getCode());
			productStoreStatusDo.setOnlineStatus(OnlineStatusEnum.OFFLINE.name());
		}

		productStoreStatusDo.setLastSynchronizeDate(new Date());
		return productStoreStatusDo;
	}

	public ContractProdTermsDo findContractProductTerm(
			Integer contractId, Integer storeId, String primaryHktvCatCode, String productReadyMethod,
			Integer brandId, String skuCode, String insurranceContractProdTerm) {
		log.info(String.format("contractId = %d, storeId = %d, primaryHktvCatCode = %s, productReadyMethod = %s, brandId = %d, skuCode = %s, contractProdTermsName = %s",
				contractId, storeId, primaryHktvCatCode, productReadyMethod,
				brandId, skuCode, insurranceContractProdTerm
		));

		if (contractId == null || StringUtil.isNullOrBlank(primaryHktvCatCode)) {
			return null;
		}

		//If select contract type is “Fixed cost contract”
		//Need to bypass the lookup terms logic, direct return the user selected terms to screen
		boolean isFixedCostType = isFixedCostContractType(contractId);
		List<ContractDo> supplementaryContractList = contractRepository.findSupplementaryContractList(contractId, new Date());
		Integer lookupContractId = contractId;
		if (supplementaryContractList != null && !supplementaryContractList.isEmpty()) {
			lookupContractId = supplementaryContractList.get(0).getId();
		}
		if (isFixedCostType) {
			if (StringUtil.isEmpty(insurranceContractProdTerm)) {
				return null;
			}
			String termName = insurranceContractProdTerm.split("\\(")[0];
			List<ContractProdTermsDo> contractProdTermsDoList = contractProdTermsRepository.findByTermsNameAndContractIdAndStoreId(termName, lookupContractId, storeId, Sort.by(Sort.Direction.DESC, "id"));
			if (CollectionUtil.isNotEmpty(contractProdTermsDoList)) {
				String fixedCostValue = insurranceContractProdTerm.split("\\(")[1].split("\\)")[0];
				if (StringUtil.isNotEmpty(fixedCostValue)) {
					BigDecimal fixedCost = new BigDecimal(fixedCostValue);
					ContractProdTermsDo fixedCostContractProdTerm = contractProdTermsDoList.stream()
							.filter(contractProdTerms -> contractProdTerms.getFixedCost().compareTo(fixedCost) == 0)
							.findFirst()
							.orElse(null);
					if (fixedCostContractProdTerm != null) {
						return fixedCostContractProdTerm;
					}
				}
				return contractProdTermsDoList.get(0);
			} else {
				return null;
			}
		}

		List<ContractProdTermsDo> contractProdTermsDoList =
				contractProdTermsRepository.findMatchStore(
						lookupContractId, storeId, skuCode, brandId, productReadyMethod, primaryHktvCatCode);
		if (CollectionUtil.isEmpty(contractProdTermsDoList)) {
			return null;
		}
		return contractProdTermsDoList.get(0);
	}

	private boolean isFixedCostContractType(Integer contractId) {
		ContractTypeDo contractTypeDo = contractTypeRepository.findByContractId(contractId);
		if (contractTypeDo == null) {
			return false;
		}
		return "FC".equalsIgnoreCase(contractTypeDo.getCode());
	}

	private ProductMasterReturnDto generateErrorResponse(String uuid, List<String> errorMessageList) {
		return ProductMasterReturnDto.builder().uuid(uuid).result("FAIL").failedReason(errorMessageList).build();
	}

	private List<String> saveProductImages(UserDto userDto, ProductMasterMqDto productMasterMqDto, ProductDo productDo, boolean callImageServer) {
		try {
			List<ProductImageDto> imagesList = new ArrayList<>();
			imagesList.add(createProductImageDto(productDo.getProductCode(), productMasterMqDto.getMainPhoto()));

			List<ProductImageDto> imagesOthAList = new ArrayList<>();
			if (CollectionUtil.isNotEmpty(productMasterMqDto.getVariantProductPhoto())) {
				for (String photo : productMasterMqDto.getVariantProductPhoto()) {
					imagesOthAList.add(createProductImageDto(productDo.getProductCode(), photo));
				}
			}

			List<ProductImageDto> imagesOthBList = new ArrayList<>();
			if (CollectionUtil.isNotEmpty(productMasterMqDto.getOtherPhoto())) {
				for (String photo : productMasterMqDto.getOtherPhoto()) {
					imagesOthBList.add(createProductImageDto(productDo.getProductCode(), photo));
				}
			}

			List<ProductImageDto> imageAdvertisingList = new ArrayList<>();
			if (StringUtil.isNotEmpty(productMasterMqDto.getAdvertisingPhoto())) {
				imageAdvertisingList.add(createProductImageDto(productDo.getProductCode(), productMasterMqDto.getAdvertisingPhoto()));
			}

			List<String> oldMainImageFilePathList = saveProductImagesByType(userDto, productDo, imagesList, ProductFileConfig.IMAGE_TYPE_MAIN, callImageServer);
			List<String> oldImageUrlIdList = new ArrayList<>(oldMainImageFilePathList);
			List<String> oldProductImageFilePathList = saveProductImagesByType(userDto, productDo, imagesOthAList, ProductFileConfig.IMAGE_TYPE_OTHER_A, callImageServer);
			oldImageUrlIdList.addAll(oldProductImageFilePathList);
			List<String> oldOtherImageFilePathList = saveProductImagesByType(userDto, productDo, imagesOthBList, ProductFileConfig.IMAGE_TYPE_OTHER_B, callImageServer);
			oldImageUrlIdList.addAll(oldOtherImageFilePathList);
			List<String> oldAdvertisingImageFilePathList = saveProductImagesByType(userDto, productDo, imageAdvertisingList, ProductFileConfig.IMAGE_TYPE_ADVERTISING, callImageServer);
			oldImageUrlIdList.addAll(oldAdvertisingImageFilePathList);
			return oldImageUrlIdList;
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			throw new SystemException("Save product image Fail");
		}
	}

	private ProductImageDto createProductImageDto(String productCode, String photoPath) {
		ProductImageDto productImageDto = new ProductImageDto();
		productImageDto.setFilePath(photoPath);
		productImageDto.setFileName(photoPath.split("/")[photoPath.split("/").length - 1]);
		productImageDto.setProductCode(productCode);
		productImageDto.setUrlId(photoPath.substring(photoPath.lastIndexOf("/") + 1, photoPath.lastIndexOf(".")));
		return productImageDto;
	}

	private ProductVideoDto createProductVideoDto(String productCode, String videoPath, String videoFileName) {
		ProductVideoDto video = new ProductVideoDto();
		video.setFilePath(videoPath);
		video.setProductCode(productCode);
		String fileName = StringUtil.isNotNullOrBlank(videoFileName) ? videoFileName : videoPath.split("/")[videoPath.split("/").length - 1];
		video.setFileName(fileName);
		return video;
	}

	private List<String> saveProductImagesByType(UserDto userDto, ProductDo productDo, List<ProductImageDto> uploadingImageList, String imageType, boolean callImageServer) {
		if (uploadingImageList == null) {
			uploadingImageList = new ArrayList<>();
		}
		List<ProductImagesDo> currentDbImageList =
				productImagesRepository.findByProductIdAndImageType(productDo.getId(), imageType);

		//找出需刪除產品圖片
		Map<String, ProductImageDto> uploadImageFilePathAndImageDtoMap = new HashMap<>();
		for (ProductImageDto imageDto : uploadingImageList) {
			uploadImageFilePathAndImageDtoMap.put(imageDto.getFilePath(), imageDto);
		}
		List<ProductImagesDo> needDeleteImageList = new ArrayList<>();
		for (ProductImagesDo currentImagesDo : currentDbImageList) {
			if (uploadImageFilePathAndImageDtoMap.get(currentImagesDo.getFilePath()) == null) {
				needDeleteImageList.add(currentImagesDo);
			}
		}
		currentDbImageList.removeAll(needDeleteImageList);
		productImagesRepository.deleteAll(needDeleteImageList);

		//找出需新增的產品圖片
		Map<String, ProductImagesDo> currentImageFilePathAndImageDoMap = new HashMap<>();
		for (ProductImagesDo productImagesDo : currentDbImageList) {
			currentImageFilePathAndImageDoMap.put(productImagesDo.getFilePath(), productImagesDo);
		}
		List<ProductImagesDo> needAddImageList = new ArrayList<>();
		for (ProductImageDto productImageDto : uploadingImageList) {
			if (currentImageFilePathAndImageDoMap.get(productImageDto.getFilePath()) == null) {
				ProductImagesDo productImagesDo = new ProductImagesDo();
				productImagesDo.setCreatedBy(userDto.getUserCode());
				productImagesDo.setCreatedDate(new Date());
				productImagesDo.setLastUpdatedBy(userDto.getUserCode());
				productImagesDo.setLastUpdatedDate(new Date());
				productImagesDo.setProductId(productDo.getId());
				productImagesDo.setProductCode(productImageDto.getProductCode());
				productImagesDo.setImageType(imageType);
				productImagesDo.setFileName(productImageDto.getFileName());
				productImagesDo.setFilePath(productImageDto.getFilePath());
				productImagesDo.setUrlId(productImageDto.getUrlId());
				needAddImageList.add(productImagesDo);
			}
		}
		productImagesRepository.saveAll(needAddImageList);
		if (callImageServer) {
			productImageHelper.updateServerImageStatus(userDto.getUserCode(), needAddImageList.stream().map(ProductImagesDo::getUrlId).collect(Collectors.toList()));
		}
		return needDeleteImageList.stream().map(ProductImagesDo::getUrlId).collect(Collectors.toList());
	}

	private SaveVideoResultDto saveProductVideos(
			UserDto userDto, ProductMasterMqDto productMasterMqDto, ProductDo productDo) {
		List<String> oldVideoFileNameList = new ArrayList<>();
		SaveVideoResultDto saveVideoResultDto =
				SaveVideoResultDto.builder().success(true).oldVideoFileNameList(oldVideoFileNameList).build();

		List<ProductVideoDto> thumbnailVideoList = new ArrayList<>();
		List<ProductVideoDto> galleryVideoList = new ArrayList<>();
		if (StringUtil.isNotNullOrBlank(productMasterMqDto.getThumbnailVideo())) {
			thumbnailVideoList.add(createProductVideoDto(productMasterMqDto.getProductId(), productMasterMqDto.getThumbnailVideo(), productMasterMqDto.getVideoFilename()));
		}
		if (StringUtil.isNotNullOrBlank(productMasterMqDto.getMainVideo())) {
			galleryVideoList.add(createProductVideoDto(productMasterMqDto.getProductId(), productMasterMqDto.getMainVideo(), productMasterMqDto.getVideoFilename()));
		}

		//thumbnailVideo
		SaveVideoResultDto saveThumbnailVideoResult = saveProductVideoByType(userDto, productDo, productMasterMqDto, thumbnailVideoList, ConstantType.THUMBNAIL_VIDEO);
		oldVideoFileNameList.addAll(saveThumbnailVideoResult.getOldVideoFileNameList());
		//galleryVideo
		SaveVideoResultDto saveGalleryVideoResult = saveProductVideoByType(userDto, productDo, productMasterMqDto, galleryVideoList, ConstantType.GALLERY_VIDEO);
		oldVideoFileNameList.addAll(saveGalleryVideoResult.getOldVideoFileNameList());

		saveVideoResultDto.setSuccess(saveThumbnailVideoResult.isSuccess() && saveGalleryVideoResult.isSuccess());

		return saveVideoResultDto;
	}

	private void saveProductBarcode(UserDto userDto, ProductMasterMqDto productMasterMqDto, ProductDo productDo) {
		List<ProductBarcodeDto> newBarcodeList = productMasterMqDto.getBarcodes();
		List<ProductBarcodeDo> currentBarcodeList = productBarcodeRepository.findByProductId(productDo.getId());

		List<ProductBarcodeDo> updateBarcodeList = new ArrayList<>();
		for (ProductBarcodeDto newBarcode : newBarcodeList) {
			if (StringUtil.isNotEmpty(newBarcode.getEan())) {
				ProductBarcodeDo currentBarcodeDo = currentBarcodeList.stream().filter(productBarcodeDto -> Objects.equals(productBarcodeDto.getSequenceNo(), newBarcode.getSequenceNo())).findFirst().orElse(new ProductBarcodeDo());
				currentBarcodeList.remove(currentBarcodeDo);

				if (StringUtil.isEmpty(currentBarcodeDo.getBarcode())) {
					ProductBarcodeDo productBarcodeDo = new ProductBarcodeDo();
					productBarcodeDo.setProductId(productDo.getId());
					productBarcodeDo.setBarcode(newBarcode.getEan());
					productBarcodeDo.setSequenceNo(newBarcode.getSequenceNo());
					productBarcodeDo.setCreatedBy(userDto.getUserCode());
					productBarcodeDo.setCreatedDate(new Date());
					productBarcodeDo.setLastUpdatedBy(userDto.getUserCode());
					productBarcodeDo.setLastUpdatedDate(new Date());
					updateBarcodeList.add(productBarcodeDo);
				} else if (!newBarcode.getEan().equals(currentBarcodeDo.getBarcode())) {
					currentBarcodeDo.setBarcode(newBarcode.getEan());
					currentBarcodeDo.setLastUpdatedBy(userDto.getUserCode());
					currentBarcodeDo.setLastUpdatedDate(new Date());
					updateBarcodeList.add(currentBarcodeDo);
				}
			}
		}
		productBarcodeRepository.saveAll(updateBarcodeList);
		productBarcodeRepository.deleteAll(currentBarcodeList);

	}

	private void saveProductOverseaDelivery(UserDto userDto, ProductMasterMqDto productMasterMqDto, ProductDo productDo) {
		List<String> deliveryDistrictList = productMasterMqDto.getDeliveryDistrict();
		if (deliveryDistrictList == null) {
			deliveryDistrictList = new ArrayList<>();
		}

		List<ProductOverseaDeliveryDo> currentDbProductOverseaDeliveryList = productOverseaDeliveryRepository.findByProductId(productDo.getId());

		// 篩選出需刪除的資料
		List<ProductOverseaDeliveryDo> needDeleteProductOverseaDeliveryList = new ArrayList<>();
		for (ProductOverseaDeliveryDo currentDbProductOverseaDelivery : currentDbProductOverseaDeliveryList) {
			if (!deliveryDistrictList.contains(currentDbProductOverseaDelivery.getRegion())) {
				needDeleteProductOverseaDeliveryList.add(currentDbProductOverseaDelivery);
			}
		}
		productOverseaDeliveryRepository.deleteAll(needDeleteProductOverseaDeliveryList);

		// 篩選出需新增的資料
		List<ProductOverseaDeliveryDo> needCreateProductOverseaDeliveryList = new ArrayList<>();
		List<String> currentDbOverseaRegionList = currentDbProductOverseaDeliveryList.stream().map(ProductOverseaDeliveryDo::getRegion).collect(Collectors.toList());
		for (String deliveryDistrict : deliveryDistrictList) {
			if (!currentDbOverseaRegionList.contains(deliveryDistrict)) {
				needCreateProductOverseaDeliveryList.add(generateProductOverseaDeliveryDo(productDo.getId(), deliveryDistrict, userDto.getUserCode()));
			}
		}
		productOverseaDeliveryRepository.saveAll(needCreateProductOverseaDeliveryList);
	}

	private ProductOverseaDeliveryDo generateProductOverseaDeliveryDo(Integer productId, String region, String userCode) {
		ProductOverseaDeliveryDo productOverseaDeliveryDo = new ProductOverseaDeliveryDo();
		productOverseaDeliveryDo.setProductId(productId);
		productOverseaDeliveryDo.setRegion(region);
		productOverseaDeliveryDo.setCreatedBy(userCode);
		productOverseaDeliveryDo.setCreatedDate(new Date());
		productOverseaDeliveryDo.setLastUpdatedBy(userCode);
		productOverseaDeliveryDo.setLastUpdatedDate(new Date());
		return productOverseaDeliveryDo;
	}

	private SaveVideoResultDto saveProductVideoByType(UserDto userDto, ProductDo productDo, ProductMasterMqDto productMasterMqDto, List<ProductVideoDto> uploadVideoList, String fileType) {
		if (uploadVideoList == null) {
			uploadVideoList = new ArrayList<>();
		}
		List<ProductVideoDo> currentVideoDoList = productVideoRepository.findByProductIdAndFileType(productDo.getId(), fileType);

		//找出需刪除產品影片
		Map<String, ProductVideoDto> uploadImageFilePathAndVideoDtoMap = new HashMap<>();
		for (ProductVideoDto videoDto : uploadVideoList) {
			uploadImageFilePathAndVideoDtoMap.put(videoDto.getFilePath(), videoDto);
		}
		List<ProductVideoDo> needDeleteVideoList = new ArrayList<>();
		for (ProductVideoDo currentVideo : currentVideoDoList) {
			if (uploadImageFilePathAndVideoDtoMap.get(currentVideo.getFilePath()) == null) {
				needDeleteVideoList.add(currentVideo);
			}
		}
		currentVideoDoList.removeAll(needDeleteVideoList);
		productVideoRepository.deleteAll(needDeleteVideoList);

		//找出需新增的產品影片
		Map<String, ProductVideoDo> currentFilePathAndVideoDoMap = new HashMap<>();
		for (ProductVideoDo productVideoDo : currentVideoDoList) {
			currentFilePathAndVideoDoMap.put(productVideoDo.getFilePath(), productVideoDo);
		}
		List<ProductVideoDo> needAddVideoList = new ArrayList<>();
		for (ProductVideoDto productVideoDto : uploadVideoList) {
			if (currentFilePathAndVideoDoMap.get(productVideoDto.getFilePath()) == null) {
				var productVideoDo = new ProductVideoDo();
				productVideoDo.setProductId(productDo.getId());
				productVideoDo.setProductCode(productMasterMqDto.getProductId());
				productVideoDo.setFileType(fileType);
				productVideoDo.setCreatedBy(userDto.getUserCode());
				productVideoDo.setCreatedDate(new Date());
				productVideoDo.setLastUpdatedBy(userDto.getUserCode());
				productVideoDo.setLastUpdatedDate(new Date());
				productVideoDo.setFilePath(productVideoDto.getFilePath());
				productVideoDo.setFileName(productVideoDto.getFileName());
				needAddVideoList.add(productVideoDo);
			}
		}
		productVideoRepository.saveAll(needAddVideoList);
		List<String> addVideoFilePathList = getVideoFileNameList(needAddVideoList);
		boolean isUpdateSuccess = productVideoHelper.requestUploadVideoStatus(userDto.getUserCode(), addVideoFilePathList);

		return SaveVideoResultDto.builder().success(isUpdateSuccess).
				oldVideoFileNameList(getVideoFileNameList(needDeleteVideoList))
				.build();
	}

	private List<String> getVideoFileNameList(List<ProductVideoDo> productVideoDoList) {
		return productVideoDoList.stream()
				.map(productVideoDo -> productVideoDo.getFilePath().split("/")[productVideoDo.getFilePath().split("/").length - 1])
				.collect(Collectors.toList());
	}

	private void sendProductMppsToRabbitMq(ProductDo productDo, ProductMppsRabbitDto productMppsRabbitDto) {
		if (productMppsRabbitDto == null || productMppsRabbitDto.getData() == null) {
			log.info("request mpps data is null");
			return;
		}
		//set image and video file path
		for (ProductMppsRabbitSkuDto item : productMppsRabbitDto.getData()) {
			List<ProductImagesDo> productImageList =
					productImagesRepository.findByProductIdAndImageType(productDo.getId(), "main");
			List<ProductVideoDo> productVideoList =
					productVideoRepository.findByProductIdAndFileType(productDo.getId(), ConstantType.GALLERY_VIDEO);
			if (CollectionUtil.isNotEmpty(productImageList)) {
				ProductImagesDo productImageEntity = productImageList.get(0);

				String photoUrl = productImageEntity.getFilePath();
				if (photoUrl != null) {
					String url = photoUrl.substring(0, photoUrl.lastIndexOf("."));
					String imgType = photoUrl.substring(photoUrl.lastIndexOf("."));
					url = url + "_515";
					photoUrl = url + imgType;
				}
				item.setMain_photo(photoUrl);
			}

			if (CollectionUtil.isNotEmpty(productVideoList)) {
				ProductVideoDo productVideoDo = productVideoList.get(0);
				String videoUrl = productVideoDo.getFilePath();
				item.setMain_video(videoUrl);
			}
		}
		try {
			ForkJoinPool.commonPool().submit(() -> callRabbitMQThread(productMppsRabbitDto, rabbitMqRoutingKey, productDo.getUuid()));
		} catch (Exception ex) {
			log.error("call MPPS by Asynchronous notification failure because of ", ex);
		}
	}

	private void callRabbitMQThread(ProductMppsRabbitDto data, String routingKey, String uuid) {
		try {
			log.info("call MPPS by Rabbit Mq start sku uuid: {}", uuid);
			mppsRabbitTemplate.convertAndSend(ConstantType.RABBITMQ_EXCHANGE, routingKey, data);
		} catch (Exception e) {
			log.error("call MPPS by Rabbit Mq failure because of ", e);
			addFailedMessageToRabbitRetryDb(data, routingKey);
		}
	}

	private void addFailedMessageToRabbitRetryDb(Object rabbitData, String routingKey) {
		try {
			List<SysParmDo> sysParmDoList = sysParmRepository.findBySegmentAndCode("MESSAGE_RETRY_COUNT", "COUNT_LIMIT");
			if (CollectionUtil.isEmpty(sysParmDoList)) {
				return;
			}
			int retryCount = Integer.parseInt(sysParmDoList.get(0).getParmValue());
			String voType = rabbitData.getClass().getCanonicalName();
			String voData = gson.toJson(rabbitData);


			RabbitRetryDo rabbitRetryDo =
					rabbitRetryRepository.findByVodataAndVotypeAndExchangeAndRoutingkeyAndActivate(
							voData, voType, ConstantType.RABBITMQ_EXCHANGE, routingKey, "Y");

			if (rabbitRetryDo == null) {
				rabbitRetryDo = new RabbitRetryDo();
				rabbitRetryDo.setVodata(voData);
				rabbitRetryDo.setCreatedDate(new Date());
				rabbitRetryDo.setLastUpdatedDate(new Date());
				rabbitRetryDo.setVotype(voType);
				rabbitRetryDo.setExchange(ConstantType.RABBITMQ_EXCHANGE);
				rabbitRetryDo.setRoutingkey(routingKey);
				rabbitRetryDo.setCreatedDate(new Date());
				rabbitRetryDo.setLastUpdatedDate(new Date());
			} else {
				Integer currentCount = rabbitRetryDo.getRetrycount();
				rabbitRetryDo.setRetrycount(rabbitRetryDo.getRetrycount() + 1);
				if (currentCount >= (retryCount - 1)) {
					rabbitRetryDo.setActivate("N");
				}
				rabbitRetryDo.setLastUpdatedDate(new Date());
			}
			rabbitRetryRepository.save(rabbitRetryDo);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
	}

	private void syncVariantProductCategories(UserDto userDto, BusinessPlatformDo busUnitModel, ProductDo productDo, ProductMasterMqDto baseProductMasterMqDto) {
		Set<String> set = new HashSet<>();
		List<ProductCategoriesDo> needAddList = new ArrayList<>();
		if (CollectionUtil.isNotEmpty(baseProductMasterMqDto.getProductTypeCode())) {
			List<ProductCategoriesDo> productCategoriesList = productCategoriesRepository.findByProductIdAndBusUnitId(productDo.getId(), busUnitModel.getBusinessId());
			productCategoriesRepository.deleteAll(productCategoriesList);
			for (String productTypeCode : baseProductMasterMqDto.getProductTypeCode()) {
				Integer productTypeCodeId = buProductCategoryRepository.findCategoryIdByProductCatCode(ConstantType.PRODUCT_CATEGORY_MMS, ConstantType.PLATFORM_CODE_HKTV, productTypeCode).orElseThrow(() -> new SystemException("CategoryCode Not Found"));
				ProductCategoriesDo productCategoriesDo = new ProductCategoriesDo();
				productCategoriesDo.setCreatedBy(userDto.getUserCode());
				productCategoriesDo.setCreatedDate(new Date());
				productCategoriesDo.setLastUpdatedBy(userDto.getUserCode());
				productCategoriesDo.setLastUpdatedDate(new Date());
				productCategoriesDo.setProductId(productDo.getId());
				productCategoriesDo.setBusUnitId(busUnitModel.getBusinessId());
				productCategoriesDo.setBuProductCategoryId(productTypeCodeId);
				productCategoriesDo.setPrimaryInd("N");
				String key = "N_" + productDo.getId() + "_" + busUnitModel.getBusinessId() + "_" + productTypeCode;
				if (set.add(key)) {
					needAddList.add(productCategoriesDo);
				}
			}
		}

		if (baseProductMasterMqDto.getPrimaryCategoryCode() != null) {
			ProductCategoriesDo productCategoriesDo = new ProductCategoriesDo();
			Integer primaryCategoryId = buProductCategoryRepository.findCategoryIdByProductCatCode(ConstantType.PRODUCT_CATEGORY_STORE, ConstantType.PLATFORM_CODE_HKTV, baseProductMasterMqDto.getPrimaryCategoryCode()).orElseThrow(() -> new SystemException("PrimaryCategoryCode Not Found"));
			productCategoriesDo.setCreatedBy(userDto.getUserCode());
			productCategoriesDo.setCreatedDate(new Date());
			productCategoriesDo.setLastUpdatedBy(userDto.getUserCode());
			productCategoriesDo.setLastUpdatedDate(new Date());
			productCategoriesDo.setProductId(productDo.getId());
			productCategoriesDo.setBusUnitId(busUnitModel.getBusinessId());
			productCategoriesDo.setBuProductCategoryId(primaryCategoryId);
			productCategoriesDo.setPrimaryInd("Y");
			String key = "Y_" + productDo.getId() + "_" + busUnitModel.getBusinessId() + "_" + baseProductMasterMqDto.getProductTypeCode();
			if (set.add(key)) {
				needAddList.add(productCategoriesDo);
			}
		}
		productCategoriesRepository.saveAll(needAddList);
	}

	private SyncResponseDto<Void> syncProductToHybris(HybrisSyncData hybrisSyncData) throws ServiceException {
		List<String> errorMessageList = new ArrayList<>();
		SyncResponseDto<Void> responseDto = SyncResponseDto.<Void>builder().build();

		try {
			Optional<ProductStoreStatusDo> productStoreStatusDoOpt = productStoreStatusRepository.findByProductId(hybrisSyncData.getProductDo().getId());
			if (productStoreStatusDoOpt.isPresent()) {
				responseDto = sendProductToGateway(hybrisSyncData, productStoreStatusDoOpt.get());
				if (CollectionUtil.isNotEmpty(responseDto.getErrorMessageList())) {
					for (String message : responseDto.getErrorMessageList()) {
						log.error("SKU ID " + hybrisSyncData.getProductMasterMqDto().getSkuId() + " sync to hybris: " + message);
					}
					return responseDto;
				}
			}
		} catch (Exception e) {
			log.error(hybrisSyncData.getProductMasterMqDto().getSkuId() + " sync to hybris:" + e.getMessage());

			responseDto.setStatus(StatusCodeEnum.FAIL.getCode());
			errorMessageList.add(e.getMessage());
			responseDto.addAllErrorMessages(errorMessageList);
			return responseDto;
		}
		responseDto.setStatus(StatusCodeEnum.SUCCESS.getCode());
		responseDto.addAllErrorMessages(errorMessageList);
		return responseDto;
	}

	private ResponseDto<Void> syncBundleToHybris(ProductActionEnum productAction, UserDto userDto, BundleValidationData bundleValidationData, ProductMasterMqDto productMasterMqDto, String action, SaveProductData saveProductData) throws ServiceException {
		List<String> errorMessageList = new ArrayList<>();
		ProductStoreStatusDo notSaveProductStoreStatusDo  = null;
		HybrisSyncData hybrisSyncData = new HybrisSyncData();
		try {
			//generate hybris request data
			String requestJson = null;
			ProductAttributesDo notSaveProductAttributesDo = generateProductAttributesDo(null, productMasterMqDto);
			ProductDo notSaveProductDo = generateProductDo(userDto, productMasterMqDto, null, notSaveProductAttributesDo);
			switch (productAction) {
				case CREATE:
				case UPDATE:
					notSaveProductStoreStatusDo = generateBundleProductStoreStatusDo(userDto, productMasterMqDto, notSaveProductDo, bundleValidationData);
					ExchangeRatePriceDto exchangeRatePriceDto = exchangeRateHelper.checkCurrencyAndGenerateHybrisPrice(notSaveProductDo.getSkuCode(), saveProductData, notSaveProductDo.getCurrencyCode(), notSaveProductDo.getOriginalPrice(), notSaveProductDo.getSellingPrice());
					hybrisSyncData = hybrisSyncData.generateBundleSyncData(userDto, bundleValidationData.getBusinessPlatformDo(), notSaveProductDo, productMasterMqDto, action, exchangeRatePriceDto);
					SaveHybrisProductDto saveHybrisProductDto = generateProductToHybrisDto(hybrisSyncData, bundleValidationData.getStoreDo(), notSaveProductStoreStatusDo, bundleValidationData.getContractDo());
					saveHybrisProductDto.setPrimaryHktvCatId(productMasterMqDto.getPrimaryCategoryCode());
					saveHybrisProductDto.setProductHktvCatList(productMasterMqDto.getPrimaryCategoryCode());
					Object hybrisActionObject = mapToHybrisActionObject(action, saveHybrisProductDto);
					requestJson =  gson.toJson(hybrisActionObject);
					break;
				case OFFLINE:
					UpdateHybrisProdctForOnOffLineDto updateHybrisProdctForOnOffLineDto = generateProductOfflineToHybrisDto(
						action, bundleValidationData.getStoreDo(), notSaveProductDo, productMasterMqDto);
					requestJson = gson.toJson(updateHybrisProdctForOnOffLineDto);
					break;
			}

			//send to hybris through gateway
			ResponseDto<MmsgwApiResultDto> gatewayResponse = null;
			switch (productAction) {
				case CREATE:
					gatewayResponse = prepareRequestMmsGateway(
					userDto, bundleValidationData.getBusinessPlatformDo(), createProductServerName, createProductServerMethod, requestJson, notSaveProductDo.getSkuCode());
					break;
				case UPDATE:
				case OFFLINE:
					gatewayResponse = prepareRequestMmsGateway(
						userDto, bundleValidationData.getBusinessPlatformDo(), updateProductServerName, updateProductServerMethod, requestJson, notSaveProductDo.getSkuCode());
					break;
			}

			//handle response
			if (gatewayResponse != null && gatewayResponse.getStatus() == StatusCodeEnum.SUCCESS.getCode()) {
				MmsgwApiResultDto apiResultDto = gatewayResponse.getData();
				if (apiResultDto != null && apiResultDto.getStatus() != null &&
					!StringUtils.equalsIgnoreCase(StatusCodeEnum.SUCCESS.name(), apiResultDto.getStatus())) {
					errorMessageList.add(String.valueOf(apiResultDto.getResult()));
				}

				//update discount price
				if (productAction == ProductActionEnum.CREATE || productAction == ProductActionEnum.UPDATE) {
					boolean openApi = false; // openapi not support bundle yet, once support, need to change this value
					ResponseDto<Void> syncResponseDto = syncProductDiscountPriceRule(hybrisSyncData, bundleValidationData.getStoreDo(),
						notSaveProductStoreStatusDo.getCommissionRate(), openApi);
					if (syncResponseDto.getStatus() == StatusCodeEnum.FAIL.getCode()) {
						errorMessageList.addAll(syncResponseDto.getErrorMessageList());
					}
				}

			} else {
				errorMessageList.addAll(gatewayResponse.getErrorMessageList());
			}
		} catch (Exception e) {
			log.error("{} sync to hybris: {}", productMasterMqDto.getSkuId(), e.getMessage());
			errorMessageList.add(e.getMessage());
		}
		return ResponseDto.generate(null, errorMessageList);
	}

	private ResponseDto<Void> offlineProductToHybris(
			UserDto userDto, BusinessPlatformDo busUnitModel, ProductDo productDo, ProductMasterMqDto productMasterMqDto, String action) throws ServiceException {
		List<String> errorMessageList = new ArrayList<>();

		try {
			Optional<ProductStoreStatusDo> storeStatusDoOpt = productStoreStatusRepository.findByProductId(productDo.getId());
			if (storeStatusDoOpt.isPresent()) {
				ResponseDto<Void> responseDto =
					offlineProductToGateway(userDto, busUnitModel, productDo, productMasterMqDto, storeStatusDoOpt.get(), action);
				Set<String> errorMessageSet = new HashSet<>();
				if (CollectionUtil.isNotEmpty(responseDto.getErrorMessageList())) {
					for (String message : responseDto.getErrorMessageList()) {
						errorMessageSet.add(message);
						log.error("SKU ID " + productDo.getSkuCode() + " sync to hybris: " + message);
					}
					return ResponseDto.<Void>builder().status(-1).errorMessageList(new ArrayList<>(errorMessageSet)).build();
				}
			}
		} catch (Exception e) {
			log.error(productDo.getSkuCode() + " sync to hybris:" + e.getMessage());
			errorMessageList.add(e.getMessage());
			return ResponseDto.<Void>builder().status(-1).errorMessageList(errorMessageList).build();
		}
		return ResponseDto.<Void>builder().status(1).errorMessageList(errorMessageList).build();
	}

	private ResponseDto<Void> offlineBundleToHybris(
		UserDto userDto, BundleProductDo bundleProductDo, ProductMasterMqDto productMasterMqDto) throws ServiceException {
		List<String> errorMessageList = new ArrayList<>();

		ResponseDto<BundleValidationData> bundleValidationDataResult = validateAndPrepareProductRelatedData(productMasterMqDto);
		if (!bundleValidationDataResult.getErrorMessageList().isEmpty()) {
			return ResponseDto.fail(errorMessageList);
		}

		try {
			ResponseDto<Void> syncHybrisResponse = syncBundleToHybris(ProductActionEnum.OFFLINE, userDto, bundleValidationDataResult.getData(), productMasterMqDto, HybrisAction.PRODUCT_SYNC_MODE_ONOFFLINE, null);
			if (CollectionUtil.isNotEmpty(syncHybrisResponse.getErrorMessageList())) {
				Set<String> errorMessageSet = syncHybrisResponse.getErrorMessageList().stream()
					.peek(errorMessage -> log.error("SKU ID {} sync to hybris: {}", productMasterMqDto.getSkuId(), errorMessage))
					.collect(Collectors.toSet());
				errorMessageList.addAll(errorMessageSet);
			}

			bundleProductDo.setLastUpdatedBy(userDto.getUserCode());
			bundleProductRepository.save(bundleProductDo);
		} catch (Exception e) {
			log.error(productMasterMqDto.getSkuId() + " sync to hybris:" + e.getMessage());
			errorMessageList.add(e.getMessage());
		}

		return ResponseDto.generate(null, errorMessageList);
	}

	private SyncResponseDto<Void> sendProductToGateway(HybrisSyncData hybrisSyncData, ProductStoreStatusDo productStoreStatusDo) throws SystemException {
		List<String> errorMessageList = new ArrayList<>();
		SyncResponseDto<MmsgwApiResultDto> gatewayResponse = SyncResponseDto.<MmsgwApiResultDto>builder().build();
		try {

			String userCode = hybrisSyncData.getUserDto().getUserCode();
			ProductDo productDo = hybrisSyncData.getProductDo();
			ProductMasterMqDto productMasterMqDto = hybrisSyncData.getProductMasterMqDto();

			List<ProductBuStatusDo> statusList = productBuStatusRepository.findByProductId(productDo.getId());
			StoreDo storeDo = storeRepository.findById(productStoreStatusDo.getStoreId()).orElse(null);
			if (storeDo == null) {
				return SyncResponseDto.<Void>builder().status(StatusCodeEnum.FAIL.getCode())
					.errorMessageList(List.of(messageSource.getMessage("message69", null, null))).build();
			}
			boolean approved = false;
			for (ProductBuStatusDo status : statusList) {
				if (storeDo.getBusUnitId().intValue() == status.getBusUnitId()) {
					approved = "A".equals(status.getStatus());
				}
			}
			if (!approved) {
				log.error("Product is not approved.");
				return SyncResponseDto.<Void>builder().status(StatusCodeEnum.FAIL.getCode())
					.errorMessageList(List.of(messageSource.getMessage("message55", new String[]{productDo.getProductCode()}, null))).build();
			}

			ContractDo contractDo = contractRepository.findById(productMasterMqDto.getContractNo()).orElse(null);
			if (contractDo == null) {
				return SyncResponseDto.<Void>builder().status(StatusCodeEnum.FAIL.getCode())
					.errorMessageList(List.of(messageSource.getMessage("message14", null, null))).build();
			}

			SaveHybrisProductDto saveHybrisProductDto = generateProductToHybrisDto(hybrisSyncData, storeDo, productStoreStatusDo, contractDo);

			Object hybrisActionObject = mapToHybrisActionObject(hybrisSyncData.getAction(), saveHybrisProductDto);
			String requestJson = gson.toJson(hybrisActionObject);

			SaveProductRecordDo saveProductRecordDo = saveProductRecordRepository.findByRecordRowId(productMasterMqDto.getRecordRowId()).orElseThrow();
			boolean openApi = SaveProductSource.OPEN_API.equals(saveProductRecordDo.getSource());

			if (productStoreStatusDo.getLastSynchronizeDate() == null) {
				gatewayResponse = prepareRequestMmsGateway(
					hybrisSyncData.getUserDto(), hybrisSyncData.getBusUnitModelDo(),
						createProductServerName, createProductServerMethod, requestJson, productDo.getSkuCode(), openApi);
			} else {
				gatewayResponse = prepareRequestMmsGateway(
					hybrisSyncData.getUserDto(), hybrisSyncData.getBusUnitModelDo(),
						updateProductServerName, updateProductServerMethod, requestJson, productDo.getSkuCode(), openApi);
			}

			if (gatewayResponse.getStatus() == StatusCodeEnum.SUCCESS.getCode()) {
				MmsgwApiResultDto apiResultDto = gatewayResponse.getData();

				if (apiResultDto != null && apiResultDto.getStatus() != null
						&& "failed".equals(apiResultDto.getStatus())) {
					errorMessageList.add(String.valueOf(apiResultDto.getResult()));

				} else {
					productStoreStatusDo.setLastSynchronizeDate(new Date());
					productStoreStatusDo.setLastUpdatedBy(userCode);
					productStoreStatusDo.setLastUpdatedDate(new Date());
					productStoreStatusRepository.save(productStoreStatusDo);
					saveProductHistory(productStoreStatusDo.getProductId(), saveProductRecordDo.getUserIp(), hybrisSyncData.getProductStoreStatusHistoryDo());

					// check if need to update discount price and Sync to Hybris
					if (hybrisSyncData.isNeedToUpdateDiscountPriceAndSyncHybris()) {
						SyncResponseDto<Void> syncResponseDto = syncProductDiscountPriceRule(hybrisSyncData, storeDo, productStoreStatusDo.getCommissionRate(), openApi);
						if (syncResponseDto.getStatus() == StatusCodeEnum.FAIL.getCode()) {
							errorMessageList.addAll(syncResponseDto.getErrorMessageList());
						}
					}
				}
			} else {
				errorMessageList.addAll(gatewayResponse.getErrorMessageList());
			}

			int status = errorMessageList.isEmpty() ? StatusCodeEnum.SUCCESS.getCode() : StatusCodeEnum.FAIL.getCode();
			SyncResponseDto<Void> syncResponseDto = SyncResponseDto.<Void>builder().status(status).errorMessageList(errorMessageList).build();
			syncResponseDto.addAllSyncStatus(gatewayResponse.getSyncedStatusMap());
			return syncResponseDto;
		} catch (Exception e) {
			log.error("Send product to Hybris error: " + e.getMessage(), e);
			SyncResponseDto<Void> syncResponseDto = SyncResponseDto.<Void>builder().status(StatusCodeEnum.FAIL.getCode()).errorMessageList(List.of(e.getMessage())).build();
			syncResponseDto.addAllSyncStatus(gatewayResponse.getSyncedStatusMap());
			syncResponseDto.addAllErrorMessages(gatewayResponse.getErrorMessageList());

			return syncResponseDto;
		}
	}

	/**
	 * Maps the given action and SaveHybrisProductDto to a corresponding Hybris action object.
	 * If new hybris action dto is added, a new enum should be added to HybrisActionMapper.
	 */
	private Object mapToHybrisActionObject(String action, SaveHybrisProductDto saveHybrisProductDto) {
		HybrisActionMapper actionMapper = HybrisActionMapper.getActionMapper(action);
		return actionMapper.mapToActionObject(saveHybrisProductDto);
	}

	private ResponseDto<Void> offlineProductToGateway(
			UserDto userDto, BusinessPlatformDo businessPlatformDo, ProductDo productDo, ProductMasterMqDto productMasterMqDto,
			ProductStoreStatusDo productStoreStatusDo, String action
	) throws SystemException {
		List<String> errorMessageList = new ArrayList<>();
		try {

			String userCode = userDto.getUserCode();

			List<ProductBuStatusDo> statusList = productBuStatusRepository.findByProductId(productDo.getId());
			StoreDo storeDo = storeRepository.findById(productStoreStatusDo.getStoreId()).orElse(null);
			if (storeDo == null) {
				return ResponseDto.<Void>builder().status(-1).errorMessageList(List.of(messageSource.getMessage("message69", null, null))).build();
			}
			boolean approved = false;
			for (ProductBuStatusDo status : statusList) {
				if (storeDo.getBusUnitId().intValue() == status.getBusUnitId()) {
					approved = "A".equals(status.getStatus());
				}
			}
			if (!approved) {
				log.error("Product is not approved.");
				return ResponseDto.<Void>builder().status(-1).errorMessageList(List.of(messageSource.getMessage("message55", new String[]{productDo.getProductCode()}, null))).build();
			}
			UpdateHybrisProdctForOnOffLineDto updateHybrisProdctForOnOffLineDto = generateProductOfflineToHybrisDto(
					action, storeDo, productDo, productMasterMqDto);

			String requestJson = gson.toJson(updateHybrisProdctForOnOffLineDto);
			SaveProductRecordDo saveProductRecordDo = saveProductRecordRepository.findByRecordRowId(productMasterMqDto.getRecordRowId()).orElseThrow();
			boolean openApi = SaveProductSource.OPEN_API.equals(saveProductRecordDo.getSource());
			ResponseDto<MmsgwApiResultDto> gatewayResponse;
			gatewayResponse = prepareRequestMmsGateway(
					userDto, businessPlatformDo,
					updateProductServerName, updateProductServerMethod, requestJson, productDo.getSkuCode(), openApi);

			if (gatewayResponse.getStatus() == 1) {
				MmsgwApiResultDto apiResultDto = gatewayResponse.getData();

				if (apiResultDto != null && apiResultDto.getStatus() != null
						&& "failed".equals(apiResultDto.getStatus())) {
					errorMessageList.add(String.valueOf(apiResultDto.getResult()));

				} else {
					productStoreStatusDo.setLastSynchronizeDate(new Date());
					productStoreStatusDo.setLastUpdatedBy(userCode);
					productStoreStatusDo.setLastUpdatedDate(new Date());
					productStoreStatusDo.setOnlineStatus(OnlineStatusEnum.OFFLINE.name());
					productStoreStatusRepository.save(productStoreStatusDo);
					saveProductHistory(productStoreStatusDo.getProductId(), saveProductRecordDo.getUserIp(), null);
				}
			} else {
				errorMessageList.addAll(gatewayResponse.getErrorMessageList());
			}

			int status = errorMessageList.isEmpty() ? 1 : -1;
			return ResponseDto.<Void>builder().status(status).errorMessageList(errorMessageList).build();
		} catch (Exception e) {
			log.error("offlineProduct error: " + e.getMessage(), e);
			throw new SystemException(e.getMessage(), e);
		}
	}

	private SaveHybrisProductDto generateProductToHybrisDto(HybrisSyncData hybrisSyncData, StoreDo storeDo, ProductStoreStatusDo productStoreStatusDo, ContractDo contractDo) {

		ProductDo productDo = hybrisSyncData.getProductDo();
		ProductMasterMqDto productMasterMqDto = hybrisSyncData.getProductMasterMqDto();
		BusinessPlatformDo businessPlatformDo = hybrisSyncData.getBusUnitModelDo();

		Integer platformId = businessPlatformDo.getPlatformId();
		String skuCode = productMasterMqDto.getSkuId();
		String storefrontStoreCode = storeDo.getStorefrontStoreCode();

		SaveHybrisProductDto saveHybrisProductDto = new SaveHybrisProductDto();
		saveHybrisProductDto.setSkuCode(skuCode);
		saveHybrisProductDto.setPickupTimeSlot(generateDefaultStringValue(productDo.getPickupTimeslot()));
		saveHybrisProductDto.setPenalty("Y");
		saveHybrisProductDto.setFixedDeliveryDate("");
		saveHybrisProductDto.setReplicateOtherVariants(productMasterMqDto.getReplicateToOtherVariantsSkus() == null ? "false" : String.valueOf(productMasterMqDto.getReplicateToOtherVariantsSkus()));
		saveHybrisProductDto.setBrandCategoryId("");
		saveHybrisProductDto.setHktvMallId("");
		saveHybrisProductDto.setProductReadyDays(generateDefaultStringValue(productDo.getProductReadyDays()));
		saveHybrisProductDto.setBarcode(generateDefaultStringValue(productDo.getBarcode()));
		saveHybrisProductDto.setColorCh(generateDefaultStringValue(productDo.getColorCh()));
		saveHybrisProductDto.setColorEn(generateDefaultStringValue(productDo.getColorEn()));
		saveHybrisProductDto.setColorZhCN(generateDefaultStringValue(productDo.getColorEn()));
		// Hardcode HKD and do not allow other currencies.(Already confirmed with Hybris RD)
		saveHybrisProductDto.setCurrencyCode(CurrencyEnum.HKD.name());
		saveHybrisProductDto.setDeliveryDetailsCh(generateDefaultStringValue(productDo.getDeliveryDetailsCh()));
		saveHybrisProductDto.setDeliveryDetailsEn(generateDefaultStringValue(productDo.getDeliveryDetailsEn()));
		saveHybrisProductDto.setDeliveryMethod(generateDefaultStringValue(productDo.getDeliveryMethod()));
		saveHybrisProductDto.setDeliveryTitleCh(generateDefaultStringValue(productDo.getDeliveryTitleCh()));
		saveHybrisProductDto.setDeliveryTitleEn(generateDefaultStringValue(productDo.getDeliveryTitleEn()));
		saveHybrisProductDto.setExpiryType(generateDefaultStringValue(productDo.getExpiryType()));
		saveHybrisProductDto.setFinePrintCh(generateDefaultStringValue(productDo.getFinePrintCh()));
		saveHybrisProductDto.setFinePrintEn(generateDefaultStringValue(productDo.getFinePrintEn()));
		saveHybrisProductDto.setFinePrintZhCN(generateDefaultStringValue(productDo.getFinePrintSc()));
		saveHybrisProductDto.setInvoiceRemarksCh(generateDefaultStringValue(productDo.getInvoiceRemarksCh()));
		saveHybrisProductDto.setInvoiceRemarksEn(generateDefaultStringValue(productDo.getInvoiceRemarksEn()));
		saveHybrisProductDto.setInvoiceRemarksZhCN(generateDefaultStringValue(productDo.getInvoiceRemarksSc()));
		saveHybrisProductDto.setInvisibleFlag(generateDefaultStringValue(productDo.getInvisibleFlag()));
		saveHybrisProductDto.setIsPrimarySku(generateDefaultStringValue(productDo.getIsPrimarySku()));
		saveHybrisProductDto.setPackBoxType(generateDefaultStringValue(productDo.getPackBoxType()));
		saveHybrisProductDto.setPackDimensionUnit(generateDefaultStringValue(productDo.getPackDimensionUnit()));
		saveHybrisProductDto.setPackSpecCh(generateDefaultStringValue(productDo.getPackSpecCh()));
		saveHybrisProductDto.setPackSpecEn(generateDefaultStringValue(productDo.getPackSpecEn()));
		saveHybrisProductDto.setPackSpecZhCN(generateDefaultStringValue(productDo.getPackSpecSc()));
		saveHybrisProductDto.setProductCode(generateDefaultStringValue(productDo.getProductCode()));
		saveHybrisProductDto.setProductReadyMethod(generateDefaultStringValue(productDo.getProductReadyMethod()));
		saveHybrisProductDto.setRedeemAddress(generateDefaultStringValue(productDo.getRedeemAddress()));
		saveHybrisProductDto.setRedeemBusName(generateDefaultStringValue(productDo.getRedeemBusName()));
		saveHybrisProductDto.setRedeemId(generateDefaultStringValue(productDo.getRedeemId()));
		saveHybrisProductDto.setRedeemLatitude(generateDefaultStringValue(productDo.getRedeemLatitude()));
		saveHybrisProductDto.setRedeemLocCity(generateDefaultStringValue(productDo.getRedeemLocCity()));
		saveHybrisProductDto.setRedeemLongitude(generateDefaultStringValue(productDo.getRedeemLongitude()));
		saveHybrisProductDto.setRedeemType(generateDefaultStringValue(productDo.getRedeemType()));
		saveHybrisProductDto.setRemarks(generateDefaultStringValue(productDo.getRemarks()));
		saveHybrisProductDto.setSkuLDescHktvCh(generateDefaultStringValue(productDo.getSkuLDescHktvCh()));
		saveHybrisProductDto.setSkuLDescHktvEn(generateDefaultStringValue(productDo.getSkuLDescHktvEn()));
		saveHybrisProductDto.setSkuLDescHktvZhCN(generateDefaultStringValue(productDo.getSkuLDescHktvSc()));
		saveHybrisProductDto.setSkuLTitleHktvCh(generateDefaultStringValue(productDo.getSkuLTitleHktvCh()));
		saveHybrisProductDto.setSkuLTitleHktvEn(generateDefaultStringValue(productDo.getSkuLTitleHktvEn()));
		saveHybrisProductDto.setSkuName(generateDefaultStringValue(productDo.getSkuName()));
		saveHybrisProductDto.setSkuNameTchi(generateDefaultStringValue(productDo.getSkuNameTchi()));
		saveHybrisProductDto.setSkuNameZhCN(generateDefaultStringValue(productDo.getSkuNameSchi()));
		saveHybrisProductDto.setSkuSDescHktvCh(generateDefaultStringValue(productDo.getSkuSDescHktvCh()));
		saveHybrisProductDto.setSkuSDescHktvEn(generateDefaultStringValue(productDo.getSkuSDescHktvEn()));
		saveHybrisProductDto.setSkuSDescHktvZhCN(generateDefaultStringValue(productDo.getSkuSDescHktvSc()));
		saveHybrisProductDto.setSkuSTitleHktvCh(generateDefaultStringValue(productDo.getSkuSTitleHktvCh()));
		saveHybrisProductDto.setSkuSTitleHktvEn(generateDefaultStringValue(productDo.getSkuSTitleHktvEn()));
		saveHybrisProductDto.setStatus(generateDefaultStringValue(productDo.getStatus()));
		saveHybrisProductDto.setUrgent(generateDefaultStringValue(productDo.getUrgent()));
		saveHybrisProductDto.setVideoLinkCh(generateDefaultStringValue(productDo.getVideoLinkCh()));
		saveHybrisProductDto.setVideoLinkEn(generateDefaultStringValue(productDo.getVideoLinkEn()));
		saveHybrisProductDto.setVideoLinkZhCN(generateDefaultStringValue(productDo.getVideoLinkSc()));
		saveHybrisProductDto.setVoucherType(generateDefaultStringValue(productDo.getVoucherType()));
		saveHybrisProductDto.setWebsite(generateDefaultStringValue(productDo.getWebsite()));
		saveHybrisProductDto.setWeightUnit(generateDefaultStringValue(productDo.getWeightUnit()));
		saveHybrisProductDto.setRemovalServices(generateDefaultStringValue(productDo.getRemovalServices()));
		saveHybrisProductDto.setField1(generateDefaultStringValue(productDo.getField1()));
		saveHybrisProductDto.setField2(generateDefaultStringValue(productDo.getField2()));
		saveHybrisProductDto.setField3(generateDefaultStringValue(productDo.getField3()));
		saveHybrisProductDto.setGoodsType(generateDefaultStringValue(productMasterMqDto.getGoodsType()));
		saveHybrisProductDto.setWarrantyPeriodUnit(generateDefaultStringValue(productMasterMqDto.getWarrantyPeriodUnit()));
		saveHybrisProductDto.setWarrantySupplierCh(generateDefaultStringValue(productMasterMqDto.getWarrantySupplierCh()));
		saveHybrisProductDto.setWarrantySupplierEn(generateDefaultStringValue(productMasterMqDto.getWarrantySupplierEn()));
		saveHybrisProductDto.setWarrantySupplierZhCN(generateDefaultStringValue(productMasterMqDto.getWarrantySupplierSc()));
		saveHybrisProductDto.setServiceCentreAddressCh(generateDefaultStringValue(productMasterMqDto.getServiceCentreAddressCh()));
		saveHybrisProductDto.setServiceCentreAddressEn(generateDefaultStringValue(productMasterMqDto.getServiceCentreAddressEn()));
		saveHybrisProductDto.setServiceCentreAddressZhCN(generateDefaultStringValue(productMasterMqDto.getServiceCentreAddressSc()));
		saveHybrisProductDto.setServiceCentreEmail(generateDefaultStringValue(productMasterMqDto.getServiceCentreEmail()));
		saveHybrisProductDto.setServiceCentreContact(generateDefaultStringValue(productMasterMqDto.getServiceCentreContact()));
		saveHybrisProductDto.setWarrantyRemarkCh(generateDefaultStringValue(productMasterMqDto.getWarrantyRemarkCh()));
		saveHybrisProductDto.setWarrantyRemarkEn(generateDefaultStringValue(productMasterMqDto.getWarrantyRemarkEn()));
		saveHybrisProductDto.setWarrantyRemarkZhCN(generateDefaultStringValue(productMasterMqDto.getWarrantyRemarkSc()));
		saveHybrisProductDto.setEnableWarehouseFunction("Y");
		saveHybrisProductDto.setWarehouseSeqId("");
		saveHybrisProductDto.setBuStatus("");
		saveHybrisProductDto.setConsumable("");
		saveHybrisProductDto.setFinePrintTitleCh("");
		saveHybrisProductDto.setFinePrintTitleEn("");
		saveHybrisProductDto.setFixedDeliveryTimeslot("");
		saveHybrisProductDto.setFlashSale("");
		saveHybrisProductDto.setForceOutOfStock("");
		saveHybrisProductDto.setRecomSellingPrice("");
		saveHybrisProductDto.setRecomSellingPriceEn("");
		saveHybrisProductDto.setRsp("");
		saveHybrisProductDto.setRspFontBakColor("");
		saveHybrisProductDto.setRspFontColor("");
		saveHybrisProductDto.setOnlineDate("");
		saveHybrisProductDto.setOptionDisplayOrder("");
		saveHybrisProductDto.setSkuLDescHkbCh("");
		saveHybrisProductDto.setSkuLDescHkbEn("");
		saveHybrisProductDto.setSkuLTitleHkbCh("");
		saveHybrisProductDto.setSkuLTitleHkbEn("");
		saveHybrisProductDto.setSkuSDescHkbCh("");
		saveHybrisProductDto.setSkuSDescHkbEn("");
		saveHybrisProductDto.setSkuSTitleHkbCh("");
		saveHybrisProductDto.setSkuSTitleHkbEn("");
		saveHybrisProductDto.setProductHkbCatList("");
		saveHybrisProductDto.setPrimaryHkbCatId("");
		saveHybrisProductDto.setExternalAffiliateUrl(productMasterMqDto.getAffiliateUrl() == null ? "" : productMasterMqDto.getAffiliateUrl());

		saveHybrisProductDto.setBuyMax(generateDefaultNonStringValue(productDo.getBuyMax()));
		saveHybrisProductDto.setReturnDays(generateDefaultNonStringValue(productDo.getReturnDays()));
		saveHybrisProductDto.setSellingPrice(generateDefaultNonStringValue(exchangeRateHelper.checkCurrencyGenerateSellingPrice(productDo.getCurrencyCode(), hybrisSyncData.getExchangeRatePriceDto().getRmbToHkdExchangeRate(), productDo.getSellingPrice())));
		saveHybrisProductDto.setMallDollarVip(generateDefaultNonStringValue(productDo.getMallDollarVip()));
		saveHybrisProductDto.setMallDollar(generateDefaultNonStringValue(productDo.getMallDollar()));
		saveHybrisProductDto.setPackHeight(generateDefaultNonStringValue(productDo.getPackHeight()));
		saveHybrisProductDto.setPackDepth(generateDefaultNonStringValue(productDo.getPackDepth()));
		saveHybrisProductDto.setStorageFee(generateDefaultNonStringValue(productDo.getStorageFee()));
		saveHybrisProductDto.setPackLength(generateDefaultNonStringValue(productDo.getPackLength()));
		saveHybrisProductDto.setWeight(generateDefaultNonStringValue(productDo.getWeight()));
		saveHybrisProductDto.setUponPurchaseDate(generateDefaultNonStringValue(productMasterMqDto.getUponPurchaseDate()));
		saveHybrisProductDto.setWarrantyPeriod(generateDefaultNonStringValue(productMasterMqDto.getWarrantyPeriod()));
		saveHybrisProductDto.setCost(generateDefaultNonStringValue(productDo.getCost()));

		saveHybrisProductDto.setPricePercentage(generateDefaultNonStringValue(productMasterMqDto.getEwPercentageSetting()));
		saveHybrisProductDto.setClaimLinkEn(generateDefaultStringValue(productMasterMqDto.getClaimLinkEn()));
		saveHybrisProductDto.setClaimLinkZh(generateDefaultStringValue(productMasterMqDto.getClaimLinkCh()));
		saveHybrisProductDto.setClaimLinkZhCN(generateDefaultStringValue(productMasterMqDto.getClaimLinkSc()));

		saveHybrisProductDto.setCommissionRate(generateDefaultNonStringValue(productStoreStatusDo.getCommissionRate()));
		StringBuilder mainImages = new StringBuilder();
		StringBuilder productImages = new StringBuilder();
		StringBuilder otherImages = new StringBuilder();
		StringBuilder advertisingImages = new StringBuilder();
		StringBuilder mainVideo = new StringBuilder();
		StringBuilder thumbnailVideo = new StringBuilder();

		if(productDo.getId() == null) {
			generateBundleProductImageAndVideoHybrisData(productMasterMqDto, mainImages, productImages,
				otherImages, advertisingImages, mainVideo, thumbnailVideo);
		} else {
			generateNormalProductImageAndVideoHybrisData(productDo.getId(), productMasterMqDto, mainImages, productImages,
				otherImages, advertisingImages, mainVideo, thumbnailVideo);
		}

		String mainImagesData = (generateDefaultStringValue(mainImages.toString()).length() > 1) ? (mainImages.substring(1)) : "";
		saveHybrisProductDto.setMainImages(mainImagesData);

		String productImagesData = (generateDefaultStringValue(productImages.toString()).length() > 1) ? (productImages.substring(1)) : "";
		saveHybrisProductDto.setProductImages(productImagesData);

		String otherImagesData = (generateDefaultStringValue(otherImages.toString()).length() > 1) ? (otherImages.substring(1)) : "";
		saveHybrisProductDto.setOtherImages(otherImagesData);

		String advertisingImagesData = (generateDefaultStringValue(advertisingImages.toString()).length() > 1) ? (advertisingImages.substring(1)) : "";
		saveHybrisProductDto.setAdvertisingImages(advertisingImagesData);

		String mainVideoData = (generateDefaultStringValue(mainVideo.toString()).length() > 1) ? (mainVideo.toString()) : "";
		saveHybrisProductDto.setMainVideo(mainVideoData);

		String thumbnailVideoData = (generateDefaultStringValue(thumbnailVideo.toString()).length() > 1) ? thumbnailVideo.toString() : "";
		saveHybrisProductDto.setThumbnailVideo(thumbnailVideoData);

		if (contractDo.getRmId() != null) {
			RmTeamDo rmTeamDo = rmTeamRepository.findByUserId(contractDo.getRmId());
			if (rmTeamDo != null) {
				saveHybrisProductDto.setRmCode(rmTeamDo.getRmCode());
			} else {
				saveHybrisProductDto.setRmCode("");
			}
		}

		//	Add isECoupon = Y , When user creating a sku with product ready method = “E-Voucher” & contract type=Service Deal
		String isECoupon = "N";
		ContractTypeDo contractTypeDo = contractTypeRepository.findByContractId(contractDo.getId());
		if (contractTypeDo != null && "BC".equals(contractTypeDo.getCode()) && "E".equals(productMasterMqDto.getProductReadyMethod())) {
			isECoupon = "Y";
		}
		saveHybrisProductDto.setIsEcoupon(isECoupon);

		if (StringUtil.isNotNullOrBlank(productMasterMqDto.getFixedRedemptionDate())) {
			saveHybrisProductDto.setRedeemEndDate(simpleDateFormat.format(productMasterSimpleDateFormat.parse(productMasterMqDto.getFixedRedemptionDate())));
		} else {
			saveHybrisProductDto.setRedeemEndDate("");
		}
		saveHybrisProductDto.setPriority(productDo.getPriority() != null ? String.valueOf(productDo.getPriority()) : "1");

		Integer userMax = productMasterMqDto.getUserMax() != null ? productMasterMqDto.getUserMax().intValue() : 0;
		saveHybrisProductDto.setUserMax(String.valueOf(userMax));
		saveHybrisProductDto.setVoucherDisplayType(generateDefaultStringValue(productMasterMqDto.getVoucherDisplayType()));
		saveHybrisProductDto.setVoucherTemplateType(generateDefaultStringValue(productMasterMqDto.getVoucherTemplateType()));

		String paymentTermsCode = contractDo.getPaymentTermsCode();
		String paymentTerms = "";
		if ("A".equals(paymentTermsCode)) {
			paymentTerms = "payByRedemption";
		} else if ("B".equals(paymentTermsCode)) {
			paymentTerms = "payBySales";
		} else if ("C".equals(paymentTermsCode)) {
			paymentTerms = "payByRedeemOrExpiry";
		}
		saveHybrisProductDto.setPaymentTerms(paymentTerms);

		if (productMasterMqDto.getRedeemStartDate() != null) {
			saveHybrisProductDto.setRedeemStartDate(simpleDateFormat.format(productMasterSimpleDateFormat.parse(productMasterMqDto.getRedeemStartDate())));
		} else {
			saveHybrisProductDto.setRedeemStartDate("");
		}

		List<BuProductCategoryDo> primaryProductCategoryList =
				buProductCategoryRepository.findByBusUnitIdAndProductIdAndPrimaryInd(businessPlatformDo.getBusinessId(), productDo.getId(), "Y");
		for (BuProductCategoryDo productCategory : primaryProductCategoryList) {
			saveHybrisProductDto.setPrimaryHktvCatId(productCategory.getProductCatCode());
		}

		StringBuilder productHktvCatListStringBuilder = new StringBuilder();
		List<BuProductCategoryDo> productCategoryList =
				buProductCategoryRepository.findByBusUnitIdAndProductIdAndPrimaryInd(businessPlatformDo.getBusinessId(), productDo.getId(), "N");
		for (BuProductCategoryDo productCategory : productCategoryList) {
			productHktvCatListStringBuilder.append("|").append(productCategory.getProductCatCode());
		}
		if (productHktvCatListStringBuilder.length() > 0) {
			saveHybrisProductDto.setProductHktvCatList(productHktvCatListStringBuilder.substring(1));
		}

		BrandDo brand = brandRepository.findById(productMasterMqDto.getBrandId()).orElse(null);
		if (brand != null) {
			saveHybrisProductDto.setBrandId(brand.getBrandCode());
			saveHybrisProductDto.setBrandEn(brand.getBrandNameEn());
			saveHybrisProductDto.setBrandZh(brand.getBrandNameTc());
			saveHybrisProductDto.setBrandZhCN(brand.getBrandNameSc());
		} else {
			saveHybrisProductDto.setBrandId("");
			saveHybrisProductDto.setBrandEn("");
			saveHybrisProductDto.setBrandZh("");
			saveHybrisProductDto.setBrandZhCN("");
		}

		if (StringUtil.isNotEmpty(productMasterMqDto.getManufacturedCountry())) {
			List<SysParmDo> modelList =
					sysParmRepository.findBySegmentAndCodeAndPlatformId("COUNTRY_OF_ORIGIN", productMasterMqDto.getManufacturedCountry(), platformId);
			if (modelList != null && !modelList.isEmpty()) {
				saveHybrisProductDto.setManuCountryEn(generateDefaultStringValue(modelList.get(0).getShortDesc()));
				saveHybrisProductDto.setManuCountryCh(generateDefaultStringValue(modelList.get(0).getShortDescTc()));
				saveHybrisProductDto.setManuCountryZhCN(generateDefaultStringValue(modelList.get(0).getShortDescSc()));
			}
		} else {
			saveHybrisProductDto.setManuCountryEn("");
			saveHybrisProductDto.setManuCountryCh("");
		}
		try {
			saveHybrisProductDto.setFeatureEndTime(
					generateDefaultStringValue(hybrisDateFormat.format(productMasterSimpleDateFormat.parse(productMasterMqDto.getFeatureEndTime()))));
		} catch (Exception e) {
			saveHybrisProductDto.setFeatureEndTime("");
		}

		try {
			saveHybrisProductDto.setFeatureStartTime(
					generateDefaultStringValue(hybrisDateFormat.format(productMasterSimpleDateFormat.parse(productMasterMqDto.getFeatureStartTime()))));
		} catch (Exception e) {
			saveHybrisProductDto.setFeatureStartTime("");
		}
		saveHybrisProductDto.setMerchantId(generateDefaultStringValue(storefrontStoreCode)); //這個跟hktv_mms確認過一致
		if (!StringUtil.isEmpty(productMasterMqDto.getPickupDays())) {
			String pickUpDays = productMasterMqDto.getPickupDays();
			switch (productMasterMqDto.getPickupDays()) {
				case "MF":
					pickUpDays = "1/2/3/4/5";
					break;
				case "MS":
					pickUpDays = "1/2/3/4/5/6";
					break;
				case "MSU":
					pickUpDays = "1/2/3/4/5/6/7";
					break;
				default:
			}
			saveHybrisProductDto.setPickupDays(pickUpDays);
		} else {
			saveHybrisProductDto.setPickupDays("");
		}

		saveHybrisProductDto.setOriginalPrice(generateDefaultNonStringValue(hybrisSyncData.getExchangeRatePriceDto().getOriginalPriceHkd()));
		saveHybrisProductDto.setOriginalMainlandPrice(generateDefaultNonStringValue(hybrisSyncData.getExchangeRatePriceDto().getOriginalMainlandPrice()));

		List<SaveHybrisVideoInfoDto> videoLinkList = new ArrayList<>();
		setVideoList(productMasterMqDto.getVideoLink(), productMasterMqDto.getVideoLinkTextCh(), productMasterMqDto.getVideoLinkTextEn(), productMasterMqDto.getVideoLinkTextSc(), videoLinkList);
		setVideoList(productMasterMqDto.getVideoLink2(), productMasterMqDto.getVideoLinkTextCh2(), productMasterMqDto.getVideoLinkTextEn2(), productMasterMqDto.getVideoLinkTextSc2(), videoLinkList);
		setVideoList(productMasterMqDto.getVideoLink3(), productMasterMqDto.getVideoLinkTextCh3(), productMasterMqDto.getVideoLinkTextEn3(), productMasterMqDto.getVideoLinkTextSc3(), videoLinkList);
		setVideoList(productMasterMqDto.getVideoLink4(), productMasterMqDto.getVideoLinkTextCh4(), productMasterMqDto.getVideoLinkTextEn4(), productMasterMqDto.getVideoLinkTextSc4(), videoLinkList);
		setVideoList(productMasterMqDto.getVideoLink5(), productMasterMqDto.getVideoLinkTextCh5(), productMasterMqDto.getVideoLinkTextEn5(), productMasterMqDto.getVideoLinkTextSc5(), videoLinkList);

		if (!videoLinkList.isEmpty()) {
			saveHybrisProductDto.setVideoLinkList(gson.toJson(videoLinkList));
		} else {
			saveHybrisProductDto.setVideoLinkList("");
		}

		if (StringUtil.isNotEmpty(productMasterMqDto.getVideoLink())) {
			saveHybrisProductDto.setVideoEntry("0|" + productMasterMqDto.getVideoLink() + "|" + (StringUtil.isNotEmpty(productMasterMqDto.getVideoLinkTextEn()) ? productMasterMqDto.getVideoLinkTextEn() : "") +
					"|" + (StringUtil.isNotEmpty(productMasterMqDto.getVideoLinkTextCh()) ? productMasterMqDto.getVideoLinkTextCh() : "") +
				"|" + (StringUtil.isNotEmpty(productMasterMqDto.getVideoLinkTextSc()) ? productMasterMqDto.getVideoLinkTextSc() : ""));
		} else {
			saveHybrisProductDto.setVideoEntry("");
		}

		Integer storeWarehouseId = productStoreStatusDo.getStoreWarehouseId();
		String warehouseId = "";
		if (storeWarehouseId != null) {
			StoreWarehouseDo storeWarehouseDo = storeWarehouseRepository.findById(storeWarehouseId).orElse(null);
			if (storeWarehouseDo != null) {
				warehouseId = String.format("%s%02d", storeDo.getStorefrontStoreCode(), storeWarehouseDo.getSeqNo());
			}
		}
		saveHybrisProductDto.setWarehouseId(warehouseId);

		saveHybrisProductDto.setColorEnCode(getColorEnOrCh(productMasterMqDto.getColor()));
		saveHybrisProductDto.setColorChCode(getColorEnOrCh(productMasterMqDto.getColor(), 1));
		saveHybrisProductDto.setSizeSystemCode(generateDefaultStringValue(productMasterMqDto.getSizeSystem()));
		saveHybrisProductDto.setSizeCode(generateDefaultStringValue(productMasterMqDto.getSize()));
		saveHybrisProductDto.setColorFamiliarCode(generateDefaultStringValue(productMasterMqDto.getColourFamilies()));
		saveHybrisProductDto.setValue1Code(generateDefaultStringValue(productMasterMqDto.getOption1Value()));
		saveHybrisProductDto.setValue2Code(generateDefaultStringValue(productMasterMqDto.getOption2Value()));
		saveHybrisProductDto.setValue3Code(generateDefaultStringValue(productMasterMqDto.getOption3Value()));

		List<SysParmDo> modelList = sysParmRepository.findBySegmentAndCodeAndPlatformId(SysParmSegment.COLOR, getColorEnOrCh(productMasterMqDto.getColor()), platformId);
		if (modelList != null && !modelList.isEmpty()) {
			saveHybrisProductDto.setColorEn(modelList.get(0).getParmValue());
		} else {
			saveHybrisProductDto.setColorEn("");
		}

		modelList = sysParmRepository.findBySegmentAndCodeAndPlatformId(SysParmSegment.COLOR_FAMILIES, productMasterMqDto.getColourFamilies(), platformId);
		if (modelList != null && !modelList.isEmpty()) {
			saveHybrisProductDto.setColorFamiliar(modelList.get(0).getParmValue());
		} else {
			saveHybrisProductDto.setColorFamiliar("");
		}

		modelList = sysParmRepository.findBySegmentAndCodeAndPlatformId(SysParmSegment.SIZE, productMasterMqDto.getSize(), platformId);
		if (modelList != null && !modelList.isEmpty()) {
			saveHybrisProductDto.setSize(modelList.get(0).getParmValue());
		} else {
			saveHybrisProductDto.setSize("");
		}

		modelList = sysParmRepository.findBySegmentAndCodeAndPlatformId(SysParmSegment.SIZE_SYSTEM, productMasterMqDto.getSizeSystem(), platformId);
		if (modelList != null && !modelList.isEmpty()) {
			saveHybrisProductDto.setSizeSystem(modelList.get(0).getParmValue());
		} else {
			saveHybrisProductDto.setSizeSystem("");
		}

		modelList = sysParmRepository.findBySegmentAndCodeAndPlatformId(SysParmSegment.PRODUCT_FIELD_VALUE, productMasterMqDto.getOption1Value(), platformId);
		if (modelList != null && !modelList.isEmpty()) {
			saveHybrisProductDto.setValue1(modelList.get(0).getCode());
		} else {
			saveHybrisProductDto.setValue1("");
		}

		modelList = sysParmRepository.findBySegmentAndCodeAndPlatformId(SysParmSegment.PRODUCT_FIELD_VALUE, productMasterMqDto.getOption2Value(), platformId);
		if (modelList != null && !modelList.isEmpty()) {
			saveHybrisProductDto.setValue2(generateDefaultStringValue(modelList.get(0).getCode()));
		} else {
			saveHybrisProductDto.setValue2("");
		}

		modelList = sysParmRepository.findBySegmentAndCodeAndPlatformId(SysParmSegment.PRODUCT_FIELD_VALUE, productMasterMqDto.getOption3Value(), platformId);
		if (modelList != null && !modelList.isEmpty()) {
			saveHybrisProductDto.setValue3(generateDefaultStringValue(modelList.get(0).getCode()));
		} else {
			saveHybrisProductDto.setValue3("");
		}

		saveHybrisProductDto.setAction(generateDefaultStringValue(hybrisSyncData.getAction()));

		String exchangeRate = "";
		if (storefrontStoreCode.startsWith("J")) {
			exchangeRate = sysParmRepository.findBySegmentAndCodeAndPlatformId(SysParmSegment.EXCHANGE_RATE, "JPY", platformId).get(0).getParmValue();
		} else if (storefrontStoreCode.startsWith("K")) {
			exchangeRate = sysParmRepository.findBySegmentAndCodeAndPlatformId(SysParmSegment.EXCHANGE_RATE, "KRW", platformId).get(0).getParmValue();
		} else if (storefrontStoreCode.startsWith("T")) {
			exchangeRate = sysParmRepository.findBySegmentAndCodeAndPlatformId(SysParmSegment.EXCHANGE_RATE, "TWD", platformId).get(0).getParmValue();
		}
		saveHybrisProductDto.setExchangeRate(exchangeRate);

		if (isBuySellMerchant(productMasterMqDto.getMerchantId())) {
			saveHybrisProductDto.setShelfLife(generateDefaultNonStringValue(productMasterMqDto.getMinimumShelfLife()));
			saveHybrisProductDto.setStorageType(generateDefaultStringValue(productMasterMqDto.getStorageType()));
			saveHybrisProductDto.setPresellFruit(generateDefaultStringValue(productMasterMqDto.getPreSellFruit()));
			saveHybrisProductDto.setPhysicalStore(generateDefaultStringValue(productMasterMqDto.getPhysicalStore()));
			saveHybrisProductDto.setRmCode(generateDefaultStringValue(productMasterMqDto.getRmCode()));
		} else {
			saveHybrisProductDto.setShelfLife("");
			saveHybrisProductDto.setStorageType("");
			saveHybrisProductDto.setPresellFruit("");
			saveHybrisProductDto.setPhysicalStore("");
		}

		if (ProductReadyMethodType.CONSIGNMENT.equalsIgnoreCase(productMasterMqDto.getProductReadyMethod())) {
			saveHybrisProductDto.setShelfLife(generateDefaultNonStringValue(productMasterMqDto.getMinimumShelfLife()));
			saveHybrisProductDto.setStorageType(generateDefaultStringValue(productMasterMqDto.getStorageType()));
		}

		if (StringUtil.isNotEmpty(productMasterMqDto.getVirtualStore())
				&& merchantHelper.isVirtualStoreMerchant(productMasterMqDto.getMerchantId(), productMasterMqDto.getVirtualStore())) {
			saveHybrisProductDto.setVirtualStore(productMasterMqDto.getVirtualStore());
		} else {
			saveHybrisProductDto.setVirtualStore("");
		}

		if (productStoreStatusDo.getLastSynchronizeDate() != null) {
			if (!StringUtil.isEmpty(productMasterMqDto.getPickupDays())) {
				String pickUpDays = productMasterMqDto.getPickupDays();
				switch (productMasterMqDto.getPickupDays()) {
					case "MF":
						pickUpDays = "1/2/3/4/5";
						break;
					case "MS":
						pickUpDays = "1/2/3/4/5/6";
						break;
					case "MSU":
						pickUpDays = "1/2/3/4/5/6/7";
						break;
					default:
				}
				saveHybrisProductDto.setPickupDays(pickUpDays);
			} else {
				saveHybrisProductDto.setPickupDays("");
			}
			if (OnlineStatusEnum.OFFLINE.name().equals(productStoreStatusDo.getOnlineStatus())) {
				saveHybrisProductDto.setOfflineDate(hybrisDateFormat.format(LocalDateTime.now()));
			} else {
				saveHybrisProductDto.setOfflineDate("01/01/2099 00:00:00");
			}
		} else {
			if (OnlineStatusEnum.OFFLINE.name().equals(productStoreStatusDo.getOnlineStatus())) {
				saveHybrisProductDto.setOfflineDate(hybrisDateFormat.format(LocalDateTime.now()));
			} else {
				saveHybrisProductDto.setOfflineDate("");
			}
		}

		List<String> deliveryDistrictList = CollectionUtil.isNotEmpty(productMasterMqDto.getDeliveryDistrict()) ? productMasterMqDto.getDeliveryDistrict() : new ArrayList<>();
		saveHybrisProductDto.setDeliverableRegionCodes(deliveryDistrictList);

		Optional.of(productMasterMqDto)
			.map(ProductMasterMqDto::getPartnerInfo)
			.map(ProductPartnerInfoDto::getEveruts)
			.ifPresent(everutsInfoDto -> {
				saveHybrisProductDto.setBuyerId(everutsInfoDto.getBuyerId());
			});
		saveHybrisProductDto.setSalesChannel(getSalesChannelByRules(saveHybrisProductDto));
		return saveHybrisProductDto;
	}

	private void generateNormalProductImageAndVideoHybrisData(Integer productId, ProductMasterMqDto productMasterMqDto, StringBuilder mainImages, StringBuilder productImages,
															  StringBuilder otherImages, StringBuilder advertisingImages, StringBuilder mainVideo, StringBuilder thumbnailVideo) {
		List<ProductImagesDo> productImageEntityList = productImagesRepository.findByProductId(productId);
		List<ProductVideoDo> productVideoEntityList = productVideoRepository.findByProductId(productId);
		for (ProductImagesDo imagesDo : productImageEntityList) {
			if (imagesDo.getFilePath() != null && (ResourceUtil.existsImageDomain(imagesDo.getFilePath()))) {
				if (ProductFileConfig.IMAGE_TYPE_MAIN.equals(imagesDo.getImageType())) {
					mainImages.append(",").append(imagesDo.getFilePath());
				} else if (ProductFileConfig.IMAGE_TYPE_OTHER_A.equals(imagesDo.getImageType())) {
					productImages.append(",").append(imagesDo.getFilePath());
				} else if (ProductFileConfig.IMAGE_TYPE_OTHER_B.equals(imagesDo.getImageType())) {
					otherImages.append(",").append(imagesDo.getFilePath());
				} else if (ProductFileConfig.IMAGE_TYPE_ADVERTISING.equals(imagesDo.getImageType())) {
					advertisingImages.append(",").append(imagesDo.getFilePath());
				}
			} else {
				String[] imageFileNameArray = imagesDo.getFileName().split("\\.");
				String imageType = imageFileNameArray[imageFileNameArray.length - 1];
				String imageFlag = imagesDo.getImageType();
				String newName = productMasterMqDto.getSkuId() + "_" + imageFlag + "_" + imagesDo.getId() + "_" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + "_" + String.format("%02d", 1);
				String imageFileName = hktvImageUrl + "/HKTV/" + productMasterMqDto.getMerchantId() + "/" + newName + "."
					+ imageType;
				if (ProductFileConfig.IMAGE_TYPE_MAIN.equals(imagesDo.getImageType())) {
					mainImages.append(",").append(imageFileName);
				} else if (ProductFileConfig.IMAGE_TYPE_OTHER_A.equals(imagesDo.getImageType())) {
					productImages.append(",").append(imageFileName);
				} else if (ProductFileConfig.IMAGE_TYPE_OTHER_B.equals(imagesDo.getImageType())) {
					otherImages.append(",").append(imageFileName);
				} else if (ProductFileConfig.IMAGE_TYPE_ADVERTISING.equals(imagesDo.getImageType())) {
					advertisingImages.append(",").append(imageFileName);
				}
			}
		}

		if (!productVideoEntityList.isEmpty()) {
			for (ProductVideoDo video : productVideoEntityList) {
				if (video.getFilePath() != null) {
					if (video.getFileType().equals(ConstantType.GALLERY_VIDEO)) {
						mainVideo.append(generateDefaultStringValue(video.getFilePath()));
					} else if (video.getFileType().equals(ConstantType.THUMBNAIL_VIDEO)) {
						thumbnailVideo.append(generateDefaultStringValue(video.getFilePath()));
					}
				}
			}
		}
	}

	private void generateBundleProductImageAndVideoHybrisData(ProductMasterMqDto productMasterMqDto, StringBuilder mainImages, StringBuilder productImages,
															  StringBuilder otherImages, StringBuilder advertisingImages, StringBuilder mainVideo, StringBuilder thumbnailVideo) {

		if (productMasterMqDto.getMainPhoto() != null) {
			mainImages.append(",").append(productMasterMqDto.getMainPhoto());
		}
		if (productMasterMqDto.getVariantProductPhoto() != null) {
			productMasterMqDto.getVariantProductPhoto().forEach(photo -> productImages.append(",").append(photo));
		}
		if (productMasterMqDto.getOtherPhoto() != null) {
			productMasterMqDto.getOtherPhoto().forEach(photo -> otherImages.append(",").append(photo));
		}
		if (productMasterMqDto.getAdvertisingPhoto() != null) {
			advertisingImages.append(",").append(productMasterMqDto.getAdvertisingPhoto());
		}

		if (productMasterMqDto.getMainVideo() != null) {
			mainVideo.append(",").append(productMasterMqDto.getMainVideo());
		}
		if (productMasterMqDto.getThumbnailVideo() != null) {
			thumbnailVideo.append(",").append(productMasterMqDto.getThumbnailVideo());
		}

	}

	private UpdateHybrisProdctForOnOffLineDto generateProductOfflineToHybrisDto(
			String action, StoreDo storeDo, ProductDo productDo,
			ProductMasterMqDto productMasterMqDto) {

		UpdateHybrisProdctForOnOffLineDto updateHybrisProdctForOnOffLineDto = new UpdateHybrisProdctForOnOffLineDto();

		updateHybrisProdctForOnOffLineDto.setAction(action);
		updateHybrisProdctForOnOffLineDto.setMerchantId(storeDo.getStorefrontStoreCode());
		updateHybrisProdctForOnOffLineDto.setSkuCode(productDo.getSkuCode());
		updateHybrisProdctForOnOffLineDto.setPrimaryHktvCatId(productMasterMqDto.getProductTypeCode().get(0));
		updateHybrisProdctForOnOffLineDto.setOfflineDate(hybrisDateFormat.format(LocalDateTime.now()));

		return updateHybrisProdctForOnOffLineDto;
	}

	private boolean isBuySellMerchant(Integer merchantId) {
		List<SysParmDo> sysParmDoList = sysParmRepository.findBySegmentAndCode("BUYSELL_MERCHANT", "BUYSELL_MERCHANT");
		HashSet<String> buySellMerchantSet = new HashSet<>();
		for (SysParmDo sysParmDo : sysParmDoList) {
			String[] merchantIdArray = sysParmDo.getParmValue().split(",");
			buySellMerchantSet.addAll(Arrays.asList(merchantIdArray));
		}

		return buySellMerchantSet.contains(String.valueOf(merchantId));
	}
	private ResponseDto<MmsgwApiResultDto> prepareRequestMmsGateway(
		UserDto userDto, BusinessPlatformDo businessPlatformDo, String serviceName,
		String requestMethod, String requestJsonString, String skuCode) {
		return prepareRequestMmsGateway(userDto, businessPlatformDo, serviceName, requestMethod, requestJsonString, skuCode, false);
	}

	private SyncResponseDto<MmsgwApiResultDto> prepareRequestMmsGateway(
			UserDto userDto, BusinessPlatformDo businessPlatformDo, String serviceName,
			String requestMethod, String requestJsonString, String skuCode, boolean isOpenApi) throws SystemException {

		MmsgwStorefrontRequestDto storefrontRequestDto = new MmsgwStorefrontRequestDto();
		storefrontRequestDto.setRequestPlatform(businessPlatformDo.getPlatformCode());
		storefrontRequestDto.setRequestSystem(HybrisHelper.SERVICE_NAME);
		storefrontRequestDto.setRequestName(serviceName);
		storefrontRequestDto.setRequestMethod(requestMethod);
		storefrontRequestDto.setRequestData(requestJsonString);

		//handle single request and response
		MmsgwResponseDto mmsgwResponseDto =
			gatewayHelper.requestMmsGateway(userDto, Collections.singletonList(storefrontRequestDto), isOpenApi);

		if (mmsgwResponseDto == null) {
			log.error("Request mms_gateway fail. skuCode: {}", skuCode);
			SyncResponseDto<MmsgwApiResultDto> syncResponseDto = SyncResponseDto.<MmsgwApiResultDto>builder().errorMessageList(List.of("Request mms_gateway fail.")).build();
			syncResponseDto.addSyncStatus(SyncService.HYBRIS, Boolean.FALSE);
			return syncResponseDto;
		}

		MmsgwStorefrontResponseDto mmsgwStorefrontResponseDto =
				CollectionUtil.isNotEmpty(mmsgwResponseDto.getResponse()) ? mmsgwResponseDto.getResponse().get(0) : null;

		if (mmsgwStorefrontResponseDto == null) {
			log.error("Call mms_gateway success,but response null. skuCode: {}", skuCode);
			SyncResponseDto<MmsgwApiResultDto> syncResponseDto = SyncResponseDto.<MmsgwApiResultDto>builder().errorMessageList(List.of("Call mms_gateway success,but response null.")).build();
			syncResponseDto.addSyncStatus(SyncService.HYBRIS, Boolean.FALSE);
			return syncResponseDto;
		}

		if (mmsgwStorefrontResponseDto.getStatus() != null) {
			final MmsgwResponseStatusDto mmsgwResponseStatus = mmsgwStorefrontResponseDto.getStatus();
			if (ConstantType.ERROR_RESPONSE_FAIL.equalsIgnoreCase(mmsgwResponseStatus.getCode())) {
				throw new SystemException(mmsgwResponseStatus.getMessage());
			}
		}

		MmsgwApiResultDto mmsgwApiResultDto = new MmsgwApiResultDto();
		if (mmsgwStorefrontResponseDto.getResponseData() != null) {
			mmsgwApiResultDto = gson.fromJson(mmsgwStorefrontResponseDto.getResponseData(), MmsgwApiResultDto.class);
		}
		SyncResponseDto<MmsgwApiResultDto> syncResponseDto = SyncResponseDto.<MmsgwApiResultDto>builder().status(1).data(mmsgwApiResultDto).build();
		syncResponseDto.addSyncStatus(SyncService.HYBRIS, Boolean.TRUE);
		return syncResponseDto;
	}

	public void saveProductHistory(Integer productId, String userIp, ProductStoreStatusHistoryDo productStoreStatusHistoryDo) {
		int productVersion = productHistoryRepository.countByProductId(productId) + 1;
		productVideoHistoryMapper.add(productId, productVersion);
		productImagesHistoryMapper.add(productId, productVersion);
		productCategoryHistoryMapMapper.add(productId, productVersion, userIp);
		productHistoryMapper.add(productId, productVersion);
		productAttributesHistoryMapper.add(productId, productVersion);
		if (productStoreStatusHistoryDo != null) {
			productStoreStatusHistoryRepository.save(productStoreStatusHistoryDo);
		}
	}

	private String generateUserIp(Long recordRowId) {
		Optional<SaveProductRecordDo> recordDo = saveProductRecordRepository.findByRecordRowId(recordRowId);
		if (recordDo.isEmpty()) {
			log.error("Can not find record by record row id :{}", recordRowId);
			return null;
		} else {
			return recordDo.get().getUserIp();
		}
	}

	private SyncResponseDto<Void> syncProductDiscountPriceRule(HybrisSyncData hybrisSyncData, StoreDo storeEntity, BigDecimal commissionRate, boolean isOpenApi) throws SystemException {
		List<String> errorMessageList = new ArrayList<>();
		SyncResponseDto<MmsgwApiResultDto> gatewayResponse = SyncResponseDto.<MmsgwApiResultDto>builder().build();
		try {
			ProductMasterMqDto product = hybrisSyncData.getProductMasterMqDto();
			BigDecimal normalDiscount = product.getSellingPrice();
			Date startDate = new Date();
			Date endDate = product.getSellingPrice() != null ? new SimpleDateFormat(DateUtil.DATE_FORMAT_SIMPLE).parse(DateUtil.FOREVER_DATE) : new Date();

			//not allow to sync null, will equal to expire the discount
			if (normalDiscount == null) {
				log.info("Discount Price not found, skip to sync the product discount rule.");
				return SyncResponseDto.<Void>builder().status(StatusCodeEnum.SUCCESS.getCode()).errorMessageList(errorMessageList).build();
			}
			//0 will equal to expire the rule
			if (normalDiscount.doubleValue() == 0d) {
				log.info("Discount Price equal to 0, set to original price and request to expire the discount rule.");
				normalDiscount = product.getOriginalPrice();
				endDate = new Date();
			}
			if (normalDiscount.doubleValue() <= product.getOriginalPrice().doubleValue()) {
				SaveHybrisCreatePromotionalDiscountRuleDto saveHybrisCreatePromotionalDiscountRuleDto = new SaveHybrisCreatePromotionalDiscountRuleDto();
				saveHybrisCreatePromotionalDiscountRuleDto.setMerchantId(storeEntity.getStorefrontStoreCode());
				saveHybrisCreatePromotionalDiscountRuleDto.setSkuCode(product.getSkuId());
				saveHybrisCreatePromotionalDiscountRuleDto.setOriginalPrice(hybrisSyncData.getExchangeRatePriceDto().getOriginalPriceHkd());
				// set finished check currency sellingPrice
				normalDiscount = hybrisSyncData.getExchangeRatePriceDto().getSellingPriceHkd();
				saveHybrisCreatePromotionalDiscountRuleDto.setNormalDiscount(normalDiscount.doubleValue());
				SimpleDateFormat promotionDateFormat = new SimpleDateFormat("MM/dd/yyyy HH:mm:ss");
				saveHybrisCreatePromotionalDiscountRuleDto.setStartDate(promotionDateFormat.format(startDate));
				saveHybrisCreatePromotionalDiscountRuleDto.setEndDate(promotionDateFormat.format(endDate));

				// commission rate cut float point when it's an integer
				if (commissionRate != null
						&& Math.max(0, commissionRate.stripTrailingZeros().scale()) == 0) {
					saveHybrisCreatePromotionalDiscountRuleDto.setCommissionRate(removeAmtLastZero(commissionRate));
				} else {
					saveHybrisCreatePromotionalDiscountRuleDto.setCommissionRate(commissionRate);
				}

				saveHybrisCreatePromotionalDiscountRuleDto.setWeightLevel("");
				saveHybrisCreatePromotionalDiscountRuleDto.setDiscountTextEn(product.getDiscountTextEn());
				saveHybrisCreatePromotionalDiscountRuleDto.setDiscountTextZh(product.getDiscountTextCh());
				saveHybrisCreatePromotionalDiscountRuleDto.setDiscountTextZhCN(product.getDiscountTextSc());
				saveHybrisCreatePromotionalDiscountRuleDto.setStyle(product.getStyle());

				String dataJson = gson.toJson(saveHybrisCreatePromotionalDiscountRuleDto);

				gatewayResponse = prepareRequestMmsGateway(hybrisSyncData.getUserDto(), hybrisSyncData.getBusUnitModelDo(),
					createPromotionServiceName, createPromotionServiceMethod, dataJson, product.getSkuId(), isOpenApi);
				if (gatewayResponse.getStatus() == 1) {
					MmsgwApiResultDto apiResultDto = gatewayResponse.getData();
					if (apiResultDto.getStatus() != null && "failed".equalsIgnoreCase(apiResultDto.getStatus())) {
						String resultMessage = apiResultDto.getResult();
						if (resultMessage.contains("rule due to discount is null") ||
								resultMessage.contains("or less than/equal zero")) {
							errorMessageList.add(messageSource.getMessage("message18", null, null));
						}
					}
				} else {
					errorMessageList.add(messageSource.getMessage("message18", null, null));
				}

			} else {
				log.info("Discount Price not found, skip to sync the product discount rule.");
			}
		} catch (Exception e) {
			log.error("Fail to call syncProductPriceRule.", e);
			errorMessageList.add(e.getMessage());
			SyncResponseDto<Void> syncResponseDto = SyncResponseDto.<Void>builder().status(StatusCodeEnum.FAIL.getCode())
				.errorMessageList(errorMessageList).build();
			syncResponseDto.addAllSyncStatus(gatewayResponse.getSyncedStatusMap());
			return syncResponseDto;
		}
		int status = errorMessageList.isEmpty() ? StatusCodeEnum.SUCCESS.getCode() : StatusCodeEnum.FAIL.getCode();
		SyncResponseDto<Void> syncResponseDto = SyncResponseDto.<Void>builder().status(status).errorMessageList(errorMessageList).build();
		syncResponseDto.addAllSyncStatus(gatewayResponse.getSyncedStatusMap());
		return syncResponseDto;
	}

	public ProductMasterReturnDto generateResult(ProductMasterMqDto productMasterMqDto, List<String> errorMessageList) {
		String status = errorMessageList.isEmpty() ? "SUCCESS" : "FAIL";
		if (status.equals("SUCCESS")) {
			return ProductMasterReturnDto.builder().uuid(productMasterMqDto.getUuid()).result(status).build();
		} else {
			return ProductMasterReturnDto.builder().uuid(productMasterMqDto.getUuid()).result(status).failedReason(errorMessageList).build();
		}
	}

	public String generateDefaultStringValue(String value) {
		if (StringUtil.isEmpty(value)) {
			return "";
		}
		return value;
	}

	public <T> String generateDefaultNonStringValue(T value) {
		if (Optional.ofNullable(value).isPresent()) {
			return String.valueOf(value);
		}
		return "";
	}

	@Transactional
	public Map<SaveProductRecordDo, SaveProductRecordRowDo> createMatrixProduct(UserDto userDto, SingleEditProductDto product, String clientIp) {
		List<VariantMatrixProductDto> variantSkuProductList = product.getVariantSkuProductList();
		Integer merchantId = product.getProduct().getMerchantId();

		ProductMasterDto primaryProduct = gson.fromJson(gson.toJson(product.getProduct()), ProductMasterDto.class);

		Map<SaveProductRecordDo, SaveProductRecordRowDo> matrixMap = new HashMap<>();
		BigDecimal rmbRate = exchangeRateHelper.getExchangeRateByCurrency(CurrencyEnum.RMB);

		for (VariantMatrixProductDto variantMatrix : variantSkuProductList) {
			SingleEditProductDto variantMatrixProduct = new SingleEditProductDto();
			primaryProduct.setSkuId(variantMatrix.getSkuId());
			if (primaryProduct.getAdditional().getHktv() != null) {
				primaryProduct.getAdditional().getHktv().setIsPrimarySku("N");
			} else if ((primaryProduct.getAdditional().getLittleMall() != null)) {
				primaryProduct.getAdditional().getLittleMall().setIsPrimarySku(false);
			}
			primaryProduct.setUuid(null);

			primaryProduct.setColor(Optional.ofNullable(variantMatrix.getColor()).orElse(primaryProduct.getColor()));
			primaryProduct.setColourFamilies(Optional.ofNullable(variantMatrix.getColourFamilies()).orElse(primaryProduct.getColourFamilies()));
			primaryProduct.setSizeSystem(Optional.ofNullable(variantMatrix.getSizeSystem()).orElse(primaryProduct.getSizeSystem()));
			primaryProduct.setSize(Optional.ofNullable(variantMatrix.getSize()).orElse(primaryProduct.getSize()));
			primaryProduct.setOption1(Optional.ofNullable(variantMatrix.getOption1()).orElse(primaryProduct.getOption1()));
			primaryProduct.setOption1Value(Optional.ofNullable(variantMatrix.getOption1Value()).orElse(primaryProduct.getOption1Value()));
			primaryProduct.setOption2(Optional.ofNullable(variantMatrix.getOption2()).orElse(primaryProduct.getOption2()));
			primaryProduct.setOption2Value(Optional.ofNullable(variantMatrix.getOption2Value()).orElse(primaryProduct.getOption2Value()));
			primaryProduct.setOption3(Optional.ofNullable(variantMatrix.getOption3()).orElse(primaryProduct.getOption3()));
			primaryProduct.setOption3Value(Optional.ofNullable(variantMatrix.getOption3Value()).orElse(primaryProduct.getOption3Value()));

			variantMatrixProduct.setProduct(primaryProduct);

			// save record and row
			SaveProductRecordDo saveProductRecordDo =
					saveProductRecordHelper.createSaveProductRecord(userDto, merchantId, SaveProductType.SINGLE_CREATE_PRODUCT, String.format(SaveProductRecordHelper.CREATE_PRODUCT_FILE_NAME, primaryProduct.getSkuId(), System.currentTimeMillis()), SaveProductStatus.WAIT_START, clientIp);
			SaveProductRecordRowDo row = saveProductRecordRowHelper.createProductRecordRowDo(saveProductRecordDo.getId(), variantMatrixProduct, SaveProductStatus.WAIT_START, null);
			primaryProduct.setRecordRowId(row.getId());

			//generate relate data in row content
			CheckProductResultDto check3plResult = productPreProcessingHelper.preProcessingHktvProduct(userDto, saveProductRecordDo, row, null);
			generateIIDSDataHelper.generateIIDSData(row);
			ResponseDto<Void> checkResult = checkProductHelper.checkCreateProductHandler(userDto, saveProductRecordDo, row, check3plResult, rmbRate);

			if (checkResult.getStatus() == StatusCodeEnum.SUCCESS.getCode()) {
				productPriceMonitorProductHelper.priceMonitorCreateProcess(row, userDto);
				checkBuHelper.checkUpdateBuList(saveProductRecordDo, row);
				matrixMap.put(saveProductRecordDo, row);
			} else {
				row.setErrorMessage(StringUtil.generateErrorMessage(checkResult.getErrorMessageList()));
				row.setStatus(SaveProductStatus.FAIL);
				saveProductRecordDo.setStatus(SaveProductStatus.FAIL);
			}
			log.info("create record id: {}, save product type: {}, row size: {}, upload user id: {}, status: {}", saveProductRecordDo.getId(), SaveProductTypeEnum.getProductTypeName(saveProductRecordDo.getUploadType()), 1, saveProductRecordDo.getUploadUserId(), SaveProductStatusEnum.getProductStatusName(saveProductRecordDo.getStatus()));
		}

		return matrixMap;
	}

	@Transactional
	public void sendMatrixProductToProductMaster(UserDto userDto, Map<SaveProductRecordDo, SaveProductRecordRowDo> matrixMap) {
		//send to pm
		matrixMap.forEach((saveProductRecordDo, row) -> {
			SingleEditProductDto singleEditProductDto = gson.fromJson(row.getContent(), SingleEditProductDto.class);
			ProductMasterDto productMasterDto = gson.fromJson(gson.toJson(singleEditProductDto.getProduct()), ProductMasterDto.class);
			SaveProductResultDto productMasterCreateProductResult = productMasterHelper.requestSaveProduct(userDto, List.of(productMasterDto), row.getSku(), saveProductRecordDo);
			saveProductRecordHelper.updateRecordByProductMasterResult(productMasterCreateProductResult, saveProductRecordDo, List.of(row));
		});
		saveProductRecordRowRepository.saveAll(matrixMap.values());
		saveProductRecordRepository.saveAll(matrixMap.keySet());
	}

	private Date parseStringIntoDate(String featureStartTime, DateTimeFormatter productSimpleDateFormat) {
		LocalDateTime localDateTime = LocalDateTime.parse(featureStartTime, productSimpleDateFormat);
		return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
	}

	private void setVideoList(String link, String textZh, String textEn, String textZhCN, List<SaveHybrisVideoInfoDto> videoLinkVoList) {
		if (StringUtil.isNotEmpty(link)) {
			SaveHybrisVideoInfoDto saveHybrisVideoInfoDto = new SaveHybrisVideoInfoDto();
			saveHybrisVideoInfoDto.setLink(link);
			saveHybrisVideoInfoDto.setTextZh(StringUtil.isNotEmpty(textZh) ? textZh : "");
			saveHybrisVideoInfoDto.setTextEn(StringUtil.isNotEmpty(textEn) ? textEn : "");
			saveHybrisVideoInfoDto.setTextZhCN(StringUtil.isNotEmpty(textZhCN) ? textZhCN : "");
			videoLinkVoList.add(saveHybrisVideoInfoDto);
		}
	}

	private void saveCartonSizes(UserDto userDto, ProductMasterMqDto productMasterMqDto, ProductDo productDo) {
		List<CartonSizeDto> newCartonSizeList = productMasterMqDto.getCartonSizeList();
		List<ProductCartonSizeDo> saveCartonSizeList = new ArrayList<>();
		Integer productId = productDo.getId();
		productCartonSizeRepository.deleteByProductId(productId);

		if (CollectionUtils.isNotEmpty(newCartonSizeList)) {
			for (CartonSizeDto newCartonSize : newCartonSizeList) {
				if (Objects.nonNull(newCartonSize.getLength()) && Objects.nonNull(newCartonSize.getWidth()) && Objects.nonNull(newCartonSize.getHeight())) {
					Date nowDate = new Date();
					String userCode = userDto.getUserCode();
					saveCartonSizeList.add(ProductCartonSizeDo.builder()
							.productId(productId)
							.cartonLength(newCartonSize.getLength())
							.cartonDepth(newCartonSize.getWidth())
							.cartonHeight(newCartonSize.getHeight())
							.createdBy(userCode)
							.createdDate(nowDate)
							.lastUpdatedBy(userCode)
							.lastUpdatedDate(nowDate)
							.build());
				}
			}
			productCartonSizeRepository.saveAll(saveCartonSizeList);
		}
	}

	public void setFieldValueNullByRuleAndType(SaveProductRecordDo productRecord, SaveProductRecordRowDo row) {
		switch (productRecord.getUploadType()) {
			case SaveProductType.BATCH_CREATE_PRODUCT:
			case SaveProductType.BATCH_EDIT_PRODUCT:
			case SaveProductType.BATCH_CREATE_PRODUCT_FROM_TMALL:
			case SaveProductType.BATCH_CREATE_PRODUCT_FROM_AUTO_TMALL:
			case SaveProductType.SYNC_SAME_PRODUCT_CODE:
			case SaveProductType.BATCH_EDIT_PRODUCT_FROM_EXCEL:
				// content是SingleEditProductDto物件且有hktv product才要執行
				setFieldValueNullByRule(row);
				break;
			default:
				break;
		}
	}

	public void setFieldValueNullByRule(SaveProductRecordRowDo row){
		SingleEditProductDto singleSaveProductDto = gson.fromJson(row.getContent(), SingleEditProductDto.class);

		ProductMasterDto product = singleSaveProductDto.getProduct();
		if (isNotNullAndBlank(product.getSizeSystem())) {
			product.setSizeSystem(null);
		}

		if (isNotNullAndBlank(product.getSize())) {
			product.setSize(null);
		}

		if (isNotNullAndBlank(product.getColourFamilies())) {
			product.setColourFamilies(null);
		}

		if (isNotNullAndBlank(product.getColor())) {
			product.setColor(null);
		}

		if (isNotNullAndBlank(product.getOption1())) {
			product.setOption1(null);
		}

		if (isNotNullAndBlank(product.getOption1Value())) {
			product.setOption1Value(null);
		}

		if (isNotNullAndBlank(product.getOption2())) {
			product.setOption2(null);
		}

		if (isNotNullAndBlank(product.getOption2Value())) {
			product.setOption2Value(null);
		}

		if (isNotNullAndBlank(product.getOption3())) {
			product.setOption3(null);
		}

		if (isNotNullAndBlank(product.getOption3Value())) {
			product.setOption3Value(null);
		}

		HktvProductDto hktvProduct = singleSaveProductDto.getProduct().getAdditional().getHktv();
		if (hktvProduct == null) {
			row.setContent(gson.toJson(singleSaveProductDto));
			return;
		}

		String contractType = hktvProduct.getContractNo() != null ? contractRepository.findMainContractTypeInContract(hktvProduct.getContractNo()) : null;
		if (!ContractType.EVERUTS.equals(contractType) && hktvProduct.getPartnerInfo() != null) {
			hktvProduct.getPartnerInfo().setEveruts(null);
		}

		// When Product Ready Method != DS(Display) || contract != DS (Display Store Contract) => set affiliateUrl = null
		if (StringUtil.isNotEquals(ProductReadyMethodType.DISPLAY_STORE, hktvProduct.getProductReadyMethod()) || StringUtil.isNotEquals(ContractType.DISPLAY_STORE_CONTRACT, contractType)) {
			hktvProduct.setAffiliateUrl(null);
		}
		// When Category != EW => set ClaimLink = null
		if (StringUtil.isNotEquals(CategoryConfig.EW_SKU_CATEGORY,hktvProduct.getPrimaryCategoryCode())) {
			hktvProduct.setClaimLinkEn(null);
			hktvProduct.setClaimLinkCh(null);
		}

		row.setContent(gson.toJson(singleSaveProductDto));
	}

	private boolean isNotNullAndBlank(String value) {
		return value != null && value.isBlank();
	}


	public ProductStoreStatusHistoryDo generateProductStoreStatusHistory(ProductDo productDo, ProductMasterMqDto productMasterMqDto) {
		ProductStoreStatusDo productStoreStatus = productStoreStatusRepository.findByProductId(productDo.getId()).orElseThrow();
		Date productStoreStatusHistoryStartDate;
		ProductStoreStatusHistoryDo productStoreStatusHistory =
			productStoreStatusHistoryRepository.findByProductIdAndStoreIdAndStoreSkuCode(
				productStoreStatus.getProductId(), productStoreStatus.getStoreId(), productStoreStatus.getStoreSkuId());

		if (!Objects.equals(productMasterMqDto.getContractNo(), productDo.getContractId())) {
			log.info("sku uuid {} change contract id from {} to {}", productDo.getUuid(), productDo.getContractId(), productMasterMqDto.getContractNo());
		}

		//if product is first time edit that productStoreStatusHistory be null, end date use contract start date
		if (Objects.nonNull(productStoreStatusHistory)) {
			productStoreStatusHistoryStartDate = productStoreStatusHistory.getEndDate();
		} else {
			ContractDo contract = contractRepository.findByProductUuid(productStoreStatus.getShareStockUuid());
			productStoreStatusHistoryStartDate = contract.getStartDate();
		}

		return gson.fromJson(gson.toJson(ProductStoreStatusHistoryDo.generateProductStoreStatusHistoryDo(productStoreStatus, productStoreStatusHistoryStartDate)), ProductStoreStatusHistoryDo.class);
	}

	//see rules in src/main/resources/rules/product_sales_channel_rules.drl
	public List<String> getSalesChannelByRules(SaveHybrisProductDto saveHybrisProductDto) {
		//get rule data
		List<SysParmDo> systemParams = cacheHelper.getSalesChannelSystemParamsBySegment(SysParmSegmentEnum.SALES_CHANNEL_SEGMENTS);
		Map<String, Set<String>> systemParamsMap = systemParams.stream()
			.collect(Collectors.groupingBy(SysParmDo::getSegment,
				Collectors.flatMapping(
					data -> Arrays.stream(data.getParmValue().split(",")),
					Collectors.toSet()
				)));
		HybrisSalesChannelRuleDto salesChannelRule = new HybrisSalesChannelRuleDto(systemParamsMap);

		//check exclusive rule
		boolean wechatExclusive = false;
		boolean ctmExclusive = false;
		if (saveHybrisProductDto.getProductHktvCatList() != null) {
			for(String catCode : saveHybrisProductDto.getProductHktvCatList().split("\\|")) {
				if (!ctmExclusive && catCode.length() >= 7 && salesChannelRule.getCtmExcludedCategories().contains(catCode.substring(0, 7))) {
					ctmExclusive = true;
				}
				if (!wechatExclusive && catCode.length() >= 4 && salesChannelRule.getWechatExcludedCategories().contains(catCode.substring(0, 4))) {
					wechatExclusive = true;
				}
			}
		}

		//execute rule
		KieSession kieSession = kieContainer.newKieSession();
		List<String> results = new ArrayList<>();
		try {
			kieSession.setGlobal("salesChannelRule", salesChannelRule); // Ensure global is set properly
			kieSession.setGlobal("results", results);
			kieSession.setGlobal("wechatExclusive", wechatExclusive);
			kieSession.setGlobal("ctmExclusive", ctmExclusive);
			kieSession.insert(saveHybrisProductDto);
			kieSession.fireAllRules();
		} finally {
			kieSession.dispose();
		}

		return results;
	}


	/**
	 * Updates the invisible flag of a product identified by its UUID.
	 *
	 * @param uuid the unique identifier of the product
	 * @param invisibleFlag the flag indicating whether the product should be invisible (true for invisible, false for visible)
	 * @return a message indicating the success or error of the operation; returns null if the update is successful
	 */
	@Transactional
	public String updateInvisible(String uuid, boolean invisibleFlag) {

		Optional<ProductDo> productOpt = productRepository.findByUuid(uuid);
		String invisibleFlagStr = invisibleFlag ? INVISIBLE_FLAG_Y : INVISIBLE_FLAG_N;

		if (productOpt.isEmpty()) {
			log.error("Product with UUID {} not found in MMS db.", uuid);
			return messageSource.getMessage("message369", null, Locale.getDefault());
		}

		try {

			ProductDo product = productOpt.get();
			product.setInvisibleFlag(invisibleFlagStr);

			productRepository.save(product);
			saveProductHistory(product.getId(), ClientIpHolder.getClientIp(), null);

			return null;
		} catch (Exception e) {

			log.error(
				"update invisible error, uuid: {}, stack trace: {}",
				uuid,
				ExceptionUtils.getStackTrace(e),
				e);

			return messageSource.getMessage("message370", new String[]{e.getMessage()},
				Locale.getDefault());
		}
	}
}
