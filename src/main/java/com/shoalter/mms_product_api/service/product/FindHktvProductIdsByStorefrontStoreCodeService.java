package com.shoalter.mms_product_api.service.product;

import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.product.helper.ProductMasterHelper;
import com.shoalter.mms_product_api.service.product.pojo.littlemall.SearchProductIdsRequestData;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
@RequiredArgsConstructor
public class FindHktvProductIdsByStorefrontStoreCodeService {

	private final ProductMasterHelper productMasterHelper;

	public ResponseDto<List<String>> start(UserDto userDto, String storefrontStoreCode) {
		SearchProductIdsRequestData searchProductIdsRequestData = SearchProductIdsRequestData.generateHktvStore(storefrontStoreCode);
		List<String> productIdsResponseData = productMasterHelper.requestProductIds(userDto, searchProductIdsRequestData);
		if (productIdsResponseData == null || productIdsResponseData.isEmpty()) {
			return ResponseDto.success(List.of());
		}
		return ResponseDto.<List<String>>builder().status(1).data(productIdsResponseData).build();
	}
}
