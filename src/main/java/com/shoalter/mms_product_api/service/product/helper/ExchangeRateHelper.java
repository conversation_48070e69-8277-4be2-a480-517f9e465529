package com.shoalter.mms_product_api.service.product.helper;

import com.google.gson.Gson;
import com.shoalter.mms_product_api.config.product.BuCodeEnum;
import com.shoalter.mms_product_api.config.product.HybrisMqHandlingEnum;
import com.shoalter.mms_product_api.config.product.QueryTypeEnum;
import com.shoalter.mms_product_api.config.product.SaveProductStatus;
import com.shoalter.mms_product_api.config.product.SaveProductType;
import com.shoalter.mms_product_api.config.product.SaveProductTypeEnum;
import com.shoalter.mms_product_api.config.product.StatusCodeEnum;
import com.shoalter.mms_product_api.config.product.SysParmSegmentEnum;
import com.shoalter.mms_product_api.config.type.ConstantType;
import com.shoalter.mms_product_api.config.type.ContractType;
import com.shoalter.mms_product_api.dao.repository.api.SaveProductRecordRowRepository;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordDo;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordRowDo;
import com.shoalter.mms_product_api.dao.repository.product.ProductStoreStatusRepository;
import com.shoalter.mms_product_api.dao.repository.product.pojo.StorefrontStoreCodeMerchantViewDo;
import com.shoalter.mms_product_api.dao.repository.store.StoreRepository;
import com.shoalter.mms_product_api.dao.repository.system.SysParmRepository;
import com.shoalter.mms_product_api.dao.repository.system.pojo.SysParmDo;
import com.shoalter.mms_product_api.mapper.ProductMasterDtoMapper;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.external_system.mms_setting.enums.MmsSettingFunctionEnum;
import com.shoalter.mms_product_api.service.external_system.mms_setting.helper.MmsSettingApiHelper;
import com.shoalter.mms_product_api.service.external_system.mms_setting.pojo.MmsSettingExchangeRateRequest;
import com.shoalter.mms_product_api.service.product.BatchEditPriceExchangeRateService;
import com.shoalter.mms_product_api.service.product.pojo.ExchangeRatePriceDto;
import com.shoalter.mms_product_api.service.product.pojo.FindStoreSkuIdProductRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterResultDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterStoreSkuIdResponseDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductOverviewResultDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductSearchRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.SaveProductData;
import com.shoalter.mms_product_api.service.product.pojo.SingleEditProductDto;
import com.shoalter.mms_product_api.util.CollectionUtil;
import com.shoalter.mms_product_api.util.SpringBeanProvider;
import com.shoalter.mms_product_api.util.StringUtil;
import com.shoalter.mms_product_api.util.enums.CurrencyEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.shoalter.mms_product_api.service.product.helper.ProductMasterHelper.SEARCH_MAX_SIZE;

@Slf4j
@RequiredArgsConstructor
@Service
public class ExchangeRateHelper {

	private final SaveProductRecordRowHelper saveProductRecordRowHelper;
	private final SaveProductRecordHelper saveProductRecordHelper;
	private final ProductStoreStatusRepository productStoreStatusRepository;
	private final StoreRepository storeRepository;
	private final SaveProductRecordRowRepository saveProductRecordRowRepository;
	private final SysParmRepository sysParmRepository;
	private final ProductMasterHelper productMasterHelper;
	private final MmsSettingApiHelper mmsSettingApiHelper;
	private final ProductMasterDtoMapper productMasterDtoMapper;
	private final Gson gson;

	/**
	 * 1. find rmb whitelist skuIds by storefrontStoreCode for edit checking, if present, sync rmb price to hybris originalMainlandPrice field
	 * 2. get rmb exchange rate for currency = RMB sku or RMB_SKU_WHITELIST sku, calculate price sync to hybris
	 */
	public SaveProductData generateSaveProductData(Integer saveProductType, Set<String> storefrontStoreCodeSet) {
		Set<String> hkdToRmbSkuIds = new HashSet<>();

		if (SaveProductType.HKTV_RMB_SKU_WHITELIST_CHECK_TYPE_SET.contains(saveProductType)) {
			List<SysParmDo> rmbStoreWhiteList = sysParmRepository.findBySegmentAndCodeIn(SysParmSegmentEnum.RMB_SKU_WHITELIST.name(), storefrontStoreCodeSet);
			if (CollectionUtil.isNotEmpty(rmbStoreWhiteList)) {
				hkdToRmbSkuIds = rmbStoreWhiteList
					.stream()
					.map(SysParmDo::getParmValue)
					.filter(Objects::nonNull)
					.map(value -> value.split(StringUtil.COMMA))
					.flatMap(Arrays::stream)
					.collect(Collectors.toSet());
			}
		}
		return SaveProductData.builder()
			.rmbToHkdExchangeRate(getExchangeRateByCurrency(CurrencyEnum.RMB))
			.hkdToRmbWhiteListSkuIds(hkdToRmbSkuIds)
			.build();
	}

	public ExchangeRatePriceDto checkCurrencyAndGenerateHybrisPrice(String skuId, SaveProductData saveProductData, String currency, BigDecimal originalPrice, BigDecimal sellingPrice) {
		BigDecimal originalPriceHkd = originalPrice;
		BigDecimal originalMainlandPrice = null;
		BigDecimal sellingPriceHkd = sellingPrice;

		if (sellingPriceHkd == null || sellingPriceHkd.compareTo(BigDecimal.ZERO) <= 0) {
			log.info("Selling Price equal to 0, set to original price and request to expire the discount rule.");
			sellingPriceHkd = originalPrice;
		}

		BigDecimal rmbToHkdRate = saveProductData.getRmbToHkdExchangeRate();

		if (rmbToHkdRate == null) {
			log.error("checkCurrencyAndGenerateHybrisPrice Error, RMB exchangeRate is null, originalPriceHkd: {}, sellingPriceHkd:{}", originalPriceHkd, sellingPriceHkd);
			return ExchangeRatePriceDto.builder()
				.rmbToHkdExchangeRate(null)
				.originalPriceHkd(null)
				.originalMainlandPrice(originalMainlandPrice)
				.sellingPriceHkd(null).build();
		}

		if (CurrencyEnum.RMB.name().equals(currency)) {
			originalMainlandPrice = originalPrice;
			originalPriceHkd = convertMultiplyHktvRmbPrice(originalPrice, rmbToHkdRate);
			sellingPriceHkd = convertMultiplyHktvRmbPrice(sellingPriceHkd, rmbToHkdRate);
		} else if (CollectionUtil.isNotEmpty(saveProductData.getHkdToRmbWhiteListSkuIds())
			&& CurrencyEnum.HKD.name().equals(currency)
			&& saveProductData.getHkdToRmbWhiteListSkuIds().contains(skuId)) {
			originalMainlandPrice = convertDivideHktvRmbPrice(originalPrice, rmbToHkdRate);
		}

		return ExchangeRatePriceDto.builder()
			.rmbToHkdExchangeRate(rmbToHkdRate)
			.originalPriceHkd(originalPriceHkd)
			.originalMainlandPrice(originalMainlandPrice)
			.sellingPriceHkd(sellingPriceHkd).build();
	}

	public BigDecimal checkCurrencyGenerateSellingPrice(String currency, BigDecimal rmbToHkdRate, BigDecimal sellingPrice) {
		BigDecimal sellingPriceHkd = sellingPrice;

		if (CurrencyEnum.RMB.name().equals(currency)) {
			if (rmbToHkdRate != null) {
				sellingPriceHkd = sellingPriceHkd == null ? null : convertMultiplyHktvRmbPrice(sellingPriceHkd, rmbToHkdRate);
			} else {
				log.error("checkCurrencyGenerateSellingPrice Error, RMB exchangeRate is null, sellingPriceHkd:{}", sellingPriceHkd);
				sellingPriceHkd = null;
			}
		}
		return sellingPriceHkd;
	}

	/**
	 * import from HKTV to LITTLE_MALL, check if HKTV store is mainland contract, need to calculate rmb price to hkd price
	 *
	 * @param rmbRate       : if null, HKTV is not mainland contract, no need to calculate price
	 * @param originalPrice
	 * @param sellingPrice
	 */
	public ExchangeRatePriceDto checkCurrencyAndGeneraLittleMallPrice(BigDecimal rmbRate, BigDecimal originalPrice, BigDecimal sellingPrice, boolean isHktvMainlandContract) {

		if (sellingPrice == null || sellingPrice.compareTo(BigDecimal.ZERO) == 0) {
			sellingPrice = null;
		}

		if (rmbRate == null) {
			if (isHktvMainlandContract) {
				log.error("checkCurrencyAndGeneraLittleMallPrice error, rmbRate is null, originalPrice: {}, sellingPrice: {}", originalPrice, sellingPrice);
				return ExchangeRatePriceDto.builder()
					.originalPriceHkd(null)
					.sellingPriceHkd(null)
					.build();
			}

			return ExchangeRatePriceDto.builder()
				.originalPriceHkd(originalPrice)
				.sellingPriceHkd(sellingPrice)
				.build();
		} else {
			return ExchangeRatePriceDto.builder()
				.originalPriceHkd(originalPrice.multiply(rmbRate).setScale(2, RoundingMode.HALF_UP))
				.sellingPriceHkd(sellingPrice == null ? null : sellingPrice.multiply(rmbRate).setScale(2, RoundingMode.HALF_UP))
				.build();
		}
	}

	public ExchangeRatePriceDto convertHybrisMqExchangeRatePrice(HybrisMqHandlingEnum hybrisMqHandlingEnum, String skuId, SaveProductData saveProductData, String currency, BigDecimal originalPrice) {
		if (HybrisMqHandlingEnum.BATCH_EDIT_PRICE_EXCHANGE_RATE_RMB != hybrisMqHandlingEnum || saveProductData == null) {
			return null;
		}
		ExchangeRatePriceDto exchangeRatePriceDto = new ExchangeRatePriceDto();
		BigDecimal rmbToHkdRate = saveProductData.getRmbToHkdExchangeRate();

		BigDecimal originalPriceHkd = null;
		BigDecimal originalMainlandPrice = null;

		if (rmbToHkdRate == null) {
			log.error("convertHybrisMqExchangeRatePrice RMB exchangeRate is null, originalPrice: {}", originalPrice);
		} else if (CurrencyEnum.RMB.name().equals(currency)) {
			originalPriceHkd = convertMultiplyHktvRmbPrice(originalPrice, rmbToHkdRate);
			originalMainlandPrice = originalPrice;
		} else if (CollectionUtil.isNotEmpty(saveProductData.getHkdToRmbWhiteListSkuIds())
			&& CurrencyEnum.HKD.name().equals(currency)
			&& saveProductData.getHkdToRmbWhiteListSkuIds().contains(skuId)) {

			originalPriceHkd = originalPrice;
			originalMainlandPrice = convertDivideHktvRmbPrice(originalPrice, rmbToHkdRate);
		}

		exchangeRatePriceDto.setRmbToHkdExchangeRate(rmbToHkdRate);
		exchangeRatePriceDto.setOriginalPriceHkd(originalPriceHkd);
		exchangeRatePriceDto.setOriginalMainlandPrice(originalMainlandPrice);
		return exchangeRatePriceDto;
	}

	public BigDecimal getExchangeRateByCurrency(CurrencyEnum baseCurrency) {
		if (baseCurrency == null) {
			log.warn("getExchangeRateByCurrency currency is null");
			return null;
		}

		MmsSettingExchangeRateRequest request = MmsSettingExchangeRateRequest.builder()
			.functionCode(MmsSettingFunctionEnum.PRODUCT)
			.targetCurrency(CurrencyEnum.HKD)
			.baseCurrency(baseCurrency)
			.build();

		BigDecimal exchangeRate = mmsSettingApiHelper.requestExchangeRate(request);
		if (exchangeRate == null || exchangeRate.compareTo(BigDecimal.ZERO) <= 0) {
			log.warn("exchangeRate is null or invalid, currency:{}, exchangeRate:{}", baseCurrency.name(), exchangeRate);
			return null;
		}

		return exchangeRate;
	}

	private BigDecimal convertMultiplyHktvRmbPrice(BigDecimal price, BigDecimal rmbToHkdRate) {
		if (price == null || rmbToHkdRate == null) {
			return price;
		}
		return price.multiply(rmbToHkdRate).setScale(4, RoundingMode.HALF_UP);
	}

	private BigDecimal convertDivideHktvRmbPrice(BigDecimal price, BigDecimal rmbToHkdRate) {
		if (price == null || rmbToHkdRate == null) {
			return price;
		}
		return price.divide(rmbToHkdRate, 4, RoundingMode.HALF_UP);
	}

	public Map<Integer, List<SingleEditProductDto>> findCurrencyRmbMerchantProductMap(UserDto userDto) {
		Map<Integer, List<SingleEditProductDto>> merchantProductMap = new HashMap<>();
		List<ProductMasterResultDto> productMasterResults = new ArrayList<>();

		try {
			// find mainland sku from mms db
			List<String> storeSkuIdList = productStoreStatusRepository.findStoreSkuIdsByContractTypes(ContractType.MAINLAND_MERCHANT_CONTRACT_SET);
			log.info("findCurrencyRmbMerchantProductMap, total mainland contract storeSkuId from mms db count: {}", storeSkuIdList.size());

			// find whitelist sku from sysparm RMB_SKU_WHITELIST
			List<String> hkdToRmbWhiteListStoreSkuIds = sysParmRepository.findBySegment(SysParmSegmentEnum.RMB_SKU_WHITELIST.name())
				.stream()
				.filter(sysParm -> sysParm.getCode() != null && sysParm.getParmValue() != null)
				.flatMap(sysParm -> Arrays.stream(sysParm.getParmValue().split(StringUtil.COMMA))
					.map(String::trim)
					.filter(StringUtil::isNotEmpty)
					.map(skuId -> String.format("%s_S_%s", sysParm.getCode(), skuId))
				)
				.collect(Collectors.toList());
			log.info("findCurrencyRmbMerchantProductMap, total RMB_SKU_WHITELIST storeSkuId count: {}", hkdToRmbWhiteListStoreSkuIds.size());

			// combine storeSkuIds for deduplication
			List<String> totalStoreSkuIds = Stream.concat(
					storeSkuIdList.stream(),
					hkdToRmbWhiteListStoreSkuIds.stream())
				.distinct()
				.collect(Collectors.toList());

			// find mainland bundle sku from product master
			List<ProductMasterResultDto> bundleSkuResults = findMainlandContractBundleSku().stream()
				.filter(productMasterResultDto -> productMasterResultDto.getAdditional() != null && productMasterResultDto.getAdditional().getHktv() != null)
				// filter mainland bundle sku, filter storeSkuId not in totalStoreSkuIds for deduplication
				.filter(productMasterResultDto -> !totalStoreSkuIds.contains(productMasterResultDto.getAdditional().getHktv().getStoreSkuId()))
				.collect(Collectors.toList());
			log.info("findCurrencyRmbMerchantProductMap, find mainland bundle from product master, total sku count: {}", bundleSkuResults.size());
			productMasterResults.addAll(bundleSkuResults);

			if (CollectionUtil.isEmpty(totalStoreSkuIds) && CollectionUtil.isEmpty(bundleSkuResults)) {
				return merchantProductMap;
			}

			for (List<String> storeSkuIds : ListUtils.partition(totalStoreSkuIds, BatchCheckHelper.MAXIMUM_2000)) {
				ProductMasterStoreSkuIdResponseDto storeSkuIdProductResponse = productMasterHelper.requestProductByStoreSkuId(userDto,
					FindStoreSkuIdProductRequestDto.builder()
						.buCode(BuCodeEnum.HKTV.name())
						.storeSkuIds(storeSkuIds)
						.build());
				if (storeSkuIdProductResponse == null) {
					log.warn("ProductMaster requestProductByStoreSkuId response null");
					continue;
				}
				if (storeSkuIdProductResponse.getStatus().equals(StatusCodeEnum.FAIL.name())) {
					log.warn("ProductMaster requestProductByStoreSkuId response FAIL, error message: {}", storeSkuIdProductResponse.getMessage());
					continue;
				}
				productMasterResults.addAll(storeSkuIdProductResponse.getData());
			}

			log.info("total sku from product master count:{} ", productMasterResults.size());
			if (CollectionUtil.isEmpty(productMasterResults)) {
				return merchantProductMap;
			}

			for (ProductMasterResultDto productMasterResult : productMasterResults) {
				SingleEditProductDto singleEditProductDto = new SingleEditProductDto();
				ProductMasterDto productMasterDto = productMasterDtoMapper.toProductMasterDto(productMasterResult);
				singleEditProductDto.setProduct(productMasterDto);
				merchantProductMap.computeIfAbsent(productMasterResult.getMerchantId(), k -> new ArrayList<>()).add(singleEditProductDto);
			}

			List<StorefrontStoreCodeMerchantViewDo> storeMerchants = storeRepository.findStorefrontStoreCodeByBuAndMerchants(BuCodeEnum.HKTV.name(), merchantProductMap.keySet());
			Map<Integer, Set<String>> merchantStorefrontStoreCodeMap = storeMerchants.stream()
				.collect(Collectors.groupingBy(StorefrontStoreCodeMerchantViewDo::getMerchantId, Collectors.mapping(StorefrontStoreCodeMerchantViewDo::getStorefrontStoreCode, Collectors.toSet())));

			log.info("merchant count: {}, merchant with storefrontStoreCode :{}", storeMerchants.size(), merchantStorefrontStoreCodeMap);

		} catch (Exception e) {
			log.error("findMainlandContractMerchantProductMap error", e);
		}
		return merchantProductMap;
	}

	private List<ProductMasterResultDto> findMainlandContractBundleSku() {
		List<String> storefrontStoreCodes = storeRepository.findActiveStorefrontStoreCodeByContractTypeCodeIn(ContractType.MAINLAND_MERCHANT_CONTRACT_SET);
		if (CollectionUtil.isEmpty(storefrontStoreCodes)) {
			return new ArrayList<>();
		}
		UserDto userDto = UserDto.generateSystemUserDto();
		ProductSearchRequestDto productSearchRequestDto = ProductSearchRequestDto.builder()
			.page(1)
			.size(SEARCH_MAX_SIZE)
			.buCode(List.of(BuCodeEnum.HKTV.name()))
			.isBundle(Boolean.TRUE)
			.storefrontStoreCodes(storefrontStoreCodes)
			.build();
		ProductOverviewResultDto productOverviewResult = productMasterHelper.requestProductsByParams(userDto, productSearchRequestDto, QueryTypeEnum.LEFT.name());
		if (productOverviewResult == null) {
			log.error("Cannot find mainland bundle from product master");
			return new ArrayList<>();
		}
		if (productOverviewResult.getTotalElements() == 0) {
			log.info("Mainland bundle from product master is empty");
			return new ArrayList<>();
		}
		List<ProductMasterResultDto> bundleResults = new ArrayList<>(productOverviewResult.getContent());

		int totalPage = productOverviewResult.getTotalPages();
		for (int i = productSearchRequestDto.getPage() + 1; i <= totalPage; i++) {
			productSearchRequestDto.setPage(i);
			ProductOverviewResultDto iteratorResultDto = productMasterHelper.requestProductsByParams(userDto, productSearchRequestDto, QueryTypeEnum.LEFT.name());
			if (iteratorResultDto == null) {
				log.error("Cannot find mainland bundle from product master, query page:{}", i);
				continue;
			}
			if (CollectionUtil.isNotEmpty(iteratorResultDto.getContent())) {
				bundleResults.addAll(iteratorResultDto.getContent());
			}
		}
		return bundleResults;
	}

	/**
	 * split merchant 10,000 sku for save one record
	 * record : status REQUESTING_PM, protocol QUEUE. recordRow : status REQUESTING_PM
	 * no need checking, send product master and hybris MQ
	 */
	public Map<Integer, Set<Long>> createBatchEditRateExchangePriceRecord(UserDto userDto, Map<Integer, List<SingleEditProductDto>> merchantProductMap) {
		if (CollectionUtil.isEmpty(merchantProductMap)) {
			return Collections.emptyMap();
		}

		Map<Integer, Set<Long>> merchantRecordMap = new HashMap<>();
		try {
			merchantProductMap.forEach((merchantId, products) -> {
				AtomicInteger batchIndex = new AtomicInteger(0);
				log.info("createBatchEditRateExchangePriceRecord merchantId:{}, total sku count: {}", merchantId, products.size());

				for (List<SingleEditProductDto> productDtos : ListUtils.partition(products, BatchCheckHelper.MAXIMUM_10000)) {
					SaveProductRecordDo saveProductRecordDo = SpringBeanProvider.getBean(ExchangeRateHelper.class).saveBatchEditRateExchangePriceRecordAndRow(userDto, merchantId, batchIndex, productDtos);
					merchantRecordMap.computeIfAbsent(merchantId, k -> new HashSet<>()).add(saveProductRecordDo.getId());
					log.info("create record id: {}, save product type: {}, row size: {}, merchant id: {}", saveProductRecordDo.getId(), SaveProductTypeEnum.getProductTypeName(saveProductRecordDo.getUploadType()), productDtos.size(), merchantId);
				}
			});
		} catch (Exception e) {
			log.error("createBatchEditRateExchangePriceRecord error", e);
		}
		return merchantRecordMap;
	}

	@Transactional
	public SaveProductRecordDo saveBatchEditRateExchangePriceRecordAndRow(UserDto userDto, Integer merchantId, AtomicInteger batchIndex, List<SingleEditProductDto> productDtos) {
		SaveProductRecordDo saveProductRecordDo = saveProductRecordHelper.createQueueSaveProductRecord(
			userDto,
			merchantId,
			SaveProductType.BATCH_EDIT_PRICE_EXCHANGE_RATE_RMB,
			convertFileName(batchIndex.incrementAndGet(), merchantId),
			SaveProductStatus.REQUESTING_PM
		);
		List<SaveProductRecordRowDo> rows = productDtos.stream()
			.map(product -> saveProductRecordRowHelper.generateProductRecordRowDo(saveProductRecordDo.getId(), product, SaveProductStatus.REQUESTING_PM, null))
			.collect(Collectors.toList());
		saveProductRecordRowHelper.batchSaveSaveProductRecordRowDo(rows);

		List<SaveProductRecordRowDo> saveProductRecordRows = saveProductRecordRowRepository.findByRecordId(saveProductRecordDo.getId());
		saveProductRecordRows.forEach(row -> {
			SingleEditProductDto singleEditProductDto = gson.fromJson(row.getContent(), SingleEditProductDto.class);
			singleEditProductDto.getProduct().setRecordRowId(row.getId());
			singleEditProductDto.getProduct().setMmsModifiedUser(userDto.getUserCode());
			singleEditProductDto.getProduct().setMmsModifiedTime(LocalDateTime.ofInstant(saveProductRecordDo.getUploadTime().toInstant(), ZoneId.systemDefault()));
			saveProductRecordRowRepository.updateRowContentByRecordRowId(row.getId(), gson.toJson(singleEditProductDto));
		});
		return saveProductRecordDo;
	}

	public String convertFileName(int batchIndex, Integer merchantId) {
		String dateString = ConstantType.YYYY_MM_DD_HH_MM_SS.format(LocalDateTime.now());
		if (batchIndex == 1) {
			return String.format(BatchEditPriceExchangeRateService.BATCH_EDIT_PRICE_EXCHANGE_RATE_FILE_NAME, merchantId, dateString);
		} else {
			return String.format(BatchEditPriceExchangeRateService.BATCH_EDIT_PRICE_EXCHANGE_RATE_FILE_NAME_INDEX, merchantId, dateString, batchIndex);
		}
	}
}
