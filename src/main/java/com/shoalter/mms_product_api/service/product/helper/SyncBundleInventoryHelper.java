package com.shoalter.mms_product_api.service.product.helper;

import com.google.gson.Gson;
import com.shoalter.mms_product_api.config.product.SaveProductStatus;
import com.shoalter.mms_product_api.config.product.SaveProductTypeEnum;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordDo;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordRowDo;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.product.pojo.SingleEditProductDto;
import com.shoalter.mms_product_api.service.product.pojo.productinventoryapi.CreateBundleInventoryRequestDo;
import com.shoalter.mms_product_api.service.product.pojo.productinventoryapi.ProductInventoryBundleResponseDto;
import com.shoalter.mms_product_api.service.product.pojo.productinventoryapi.ProductInventoryResponseDto;
import com.shoalter.mms_product_api.util.CollectionUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@RequiredArgsConstructor
@Service
public class SyncBundleInventoryHelper {

	private final Gson gson;
	private final UserHelper userHelper;
	private final InventoryHelper inventoryHelper;

	private final TaskExceptionHelper taskExceptionHelper;
	private final MessageSource messageSource;

	@Transactional
	public void sendBundleInventoryAndUpdateRecordRow(SaveProductRecordDo record, List<SaveProductRecordRowDo> saveProductRecordRowDos) {
		List<SaveProductRecordRowDo> waitSyncInventoryList = saveProductRecordRowDos.stream().filter(row -> SaveProductStatus.SUCCESS == row.getStatus()).collect(Collectors.toList());
		if(CollectionUtil.isEmpty(waitSyncInventoryList)){
			return;
		}

		try {
			log.info("Task info run record id: {}, save product type: {}, row size: {}, upload user id: {}, status: {}", record.getId(), SaveProductTypeEnum.getProductTypeName(record.getUploadType()), waitSyncInventoryList.size(), record.getUploadUserId(), "REQUESTING_BUNDLE_INVENTORY");
			UserDto userDto = userHelper.generateUserDtoByRecord(record);

			ProductInventoryResponseDto<ProductInventoryBundleResponseDto> responseDto = requestUpdateBundleQtyToMmsInventory(userDto, waitSyncInventoryList);
			// bundle only one sku
			SaveProductRecordRowDo row = waitSyncInventoryList.get(0);
			if (Objects.nonNull(responseDto.getResponse()) && "SUCCESS".equalsIgnoreCase(responseDto.getCode())) {
				ProductInventoryBundleResponseDto productInventoryBundleResponseDto = responseDto.getResponse();
				if (CollectionUtil.isNotEmpty(productInventoryBundleResponseDto.getFailList()) &&
					productInventoryBundleResponseDto.getFailList().get(0).getUuid().equals(row.getUuid())) {
					row.setStatus(SaveProductStatus.FAIL);
					row.setErrorMessage(productInventoryBundleResponseDto.getFailList().get(0).getErrorMsg());
				} else {
					row.setStatus(SaveProductStatus.SUCCESS);
				}
			} else {
				row.setErrorMessage(generateErrorMessage(List.of(messageSource.getMessage("message91", null, null))));
				row.setStatus(SaveProductStatus.FAIL);
			}
		} catch (Exception e) {
			taskExceptionHelper.start(record, waitSyncInventoryList, e);
		}
	}

	private ProductInventoryResponseDto<ProductInventoryBundleResponseDto> requestUpdateBundleQtyToMmsInventory(UserDto userDto, List<SaveProductRecordRowDo> saveProductRecordRowDos) {
		boolean useDataResourcePM = true;
		List<CreateBundleInventoryRequestDo> createBundleInventoryRequestDos = new ArrayList<>();
		for (SaveProductRecordRowDo saveProductRecordRowDo : saveProductRecordRowDos) {
			SingleEditProductDto singleEditProductDto = gson.fromJson(saveProductRecordRowDo.getContent(), SingleEditProductDto.class);
			createBundleInventoryRequestDos.add(CreateBundleInventoryRequestDo.generateBundleInventoryRequestDo(singleEditProductDto, saveProductRecordRowDo.getUuid()));
		}

		return inventoryHelper.putBundleInventory(userDto, createBundleInventoryRequestDos, null, useDataResourcePM);
	}

	private String generateErrorMessage(List<String> errorMessageList) {
		return String.join(", \n", errorMessageList);
	}
}
