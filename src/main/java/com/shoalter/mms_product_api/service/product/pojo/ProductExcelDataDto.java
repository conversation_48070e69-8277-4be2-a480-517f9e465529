package com.shoalter.mms_product_api.service.product.pojo;


import lombok.Data;

import java.math.BigDecimal;

@Data
public class ProductExcelDataDto {
	private String productId;
	private String skuId;
	private String productTypeCode;
	private String primaryCategoryCode;
	private String brandId;
	private String productReadyMethod;
	private Integer Id;
	private String warehouse;
	private String termName;
	private String isPrimarySku;
	private String skuNameEn;
	private String skuNameCh;
	private String skuShortDescriptionEn;
	private String skuShortDescriptionCh;
	private String skuLongDescriptionEn;
	private String skuLongDescriptionCh;
	private String mainPhoto;
	private String mainVideo;
	private String variantProductPhoto;
	private String otherPhoto;
	private String advertisingPhoto;
	private String videoLink;
	private String videoLinkTextEn;
	private String videoLinkTextCh;
	private String videoLink2;
	private String videoLinkEn2;
	private String videoLinkCh2;
	private String videoLink3;
	private String videoLinkEn3;
	private String videoLinkCh3;
	private String videoLink4;
	private String videoLinkEn4;
	private String videoLinkCh4;
	private String videoLink5;
	private String videoLinkEn5;
	private String videoLinkCh5;
	private String manufacturedCountry;
	private String colourFamilies;
	private String colorEn;
	private String sizeSystem;
	private String size;
	private String currency;
	private BigDecimal cost;
	private BigDecimal originalPrice;
	private BigDecimal sellingPrice;
	private BigDecimal mallDollar;
	private BigDecimal vipMallDollar;
	private Long userMax;
	private String style;
	private String discountTextEn;
	private String discountTextCh;
	private String packingSpecEn;
	private String packingSpecCh;
	private BigDecimal packingHeight;
	private BigDecimal packingLength;
	private BigDecimal packingDepth;
	private String packingDimensionUnit;
	private BigDecimal weight;
	private String weightUnit;
	private String packingBoxType;
	private Integer cartonHeight;
	private Integer cartonDepth;
	private Integer cartonLength;
	private String invisibleFlag;
	private String barcodes;
	private String featureStartTime;
	private String featureEndTime;
	private String voucherType;
	private String voucherDisplayType;
	private String voucherTemplateType;
	private String expiryType;
	private String option1;
	private String option1Value;
	private String option2;
	private String option2Value;
	private String option3;
	private String option3Value;
	private String redeemStartDate;
	private String fixedRedemptionDate;
	private Integer uponPurchaseDate;
	private String finePrintEn;
	private String finePrintCh;
	private String needRemovalServices;
	private String goodsType;
	private String warrantyPeriodUnit;
	private Integer warrantyPeriod;
	private String warrantySupplierEn;
	private String warrantySupplierCh;
	private String serviceCentreAddressEn;
	private String serviceCentreAddressCh;
	private String serviceCentreEmail;
	private String serviceCentreContact;
	private String warrantyRemarkEn;
	private String warrantyRemarkCh;
	private String invoiceRemarksEn;
	private String invoiceRemarksCh;
	private String returnDays;
	private String productReadyDays;
	private String pickupDays;
	private String pickupTimeslot;
	private String overseaDelivery;
	private String urgent;
	private Integer minimumShelfLife;
	private String virtualStore;
	private String rmCode;
	private String storageType;
	private String preSellFruit;
	private String physicalStore;
	private String affiliateUrl;
}
