package com.shoalter.mms_product_api.service.product.pojo.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
public class StoreApiSearchProductFieldOptionsRequestDto implements Serializable {
	private  String buCode;
	private  String storefrontStoreCode;
	private List<Integer> pids;
	private List<Integer> sids;
	private List<Integer> vids;

	public static StoreApiSearchProductFieldOptionsRequestDto generate(String buCode, String storefrontStoreCode, List<Integer> pids, List<Integer> sids, List<Integer> vids) {
		return StoreApiSearchProductFieldOptionsRequestDto.builder()
			.buCode(buCode)
			.storefrontStoreCode(storefrontStoreCode)
			.pids(pids)
			.sids(sids)
			.vids(vids)
			.build();
	}
}
