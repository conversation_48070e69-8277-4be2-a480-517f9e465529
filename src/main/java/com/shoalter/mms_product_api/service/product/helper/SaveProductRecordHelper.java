package com.shoalter.mms_product_api.service.product.helper;

import com.shoalter.mms_product_api.config.product.ProductMasterErrorTypeEnum;
import com.shoalter.mms_product_api.config.product.SaveProductProtocol;
import com.shoalter.mms_product_api.config.product.SaveProductProtocolEnum;
import com.shoalter.mms_product_api.config.product.SaveProductSource;
import com.shoalter.mms_product_api.config.product.SaveProductStatus;
import com.shoalter.mms_product_api.dao.repository.api.SaveProductRecordRepository;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordDo;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordRowDo;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.openApi.helper.OapiHelper;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterResponseDto;
import com.shoalter.mms_product_api.service.product.pojo.SaveProductResultDto;
import com.shoalter.mms_product_api.util.CollectionUtil;
import com.shoalter.mms_product_api.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Slf4j
@RequiredArgsConstructor
@Service
public class SaveProductRecordHelper {
	public static final String CREATE_PRODUCT_FILE_NAME = "Create_%s_%d.xlsx";
	public static final String EDIT_PRODUCT_FILE_NAME = "Edit_%s_%d.xlsx";
	public static final String FORCE_OFFLINE_SINGLE_FILE_NAME = "Single_ForceOffline_%d_%s.xlsx";
	public static final String FORCE_OFFLINE_BATCH_FILE_NAME = "Batch_ForceOffline_%d_%s.xlsx";
	public static final String FORCE_OFFLINE_BATCH_FAIL_FILE_NAME = "Batch_ForceOffline_%s.xlsx";
	public static final String VARIANT_EDIT_PRODUCT_FILE_NAME = "VariantEdit_%s_%d.xlsx";
	public static final String BATCH_EDIT_PRODUCT_FILE_NAME = "BatchEdit_%d.xlsx";
	private final SaveProductRecordRepository saveProductRecordRepository;
	private final ProductMasterHelper productMasterHelper;

	public void updateRecordStatusToChecking(SaveProductRecordDo record) {
		record.setStatus(SaveProductStatus.CHECKING_PRODUCT);
		saveProductRecordRepository.save(record);
	}

	/**
	 * @deprecated This method will be removed in future versions (MS-5279). Use createSaveProductRecord() instead.
	 */
	@Deprecated
	public SaveProductRecordDo createSaveProductRecord(UserDto userDto, Integer merchantId, Integer uploadType, String fileName, Integer recordStatus) {
		SaveProductRecordDo record = new SaveProductRecordDo();
		record.setMerchantId(merchantId);
		record.setUploadType(uploadType);
		record.setFileName(fileName);
		record.setStatus(recordStatus);
		record.setUploadUserId(userDto.getUserId());
		record.setUploadTime(new Date());
		return saveProductRecordRepository.save(record);
	}

	/**
	 * MS-5279 save client ip
	 */
	public SaveProductRecordDo createSaveProductRecord(UserDto userDto, Integer merchantId,
		Integer uploadType, String fileName, Integer recordStatus, String clientIp) {
		return
			createSaveProductRecord(
				userDto,
				merchantId,
				uploadType,
				fileName,
				recordStatus,
				clientIp,
				SaveProductProtocolEnum.fromSaveProductTypeInt(uploadType).toString());
	}

	public SaveProductRecordDo createSaveProductRecord(int userId, Integer merchantId, Integer uploadType, String skuId) {
		SaveProductRecordDo saveProductRecordDo = new SaveProductRecordDo();
		saveProductRecordDo.setUploadTime(new Date());
		saveProductRecordDo.setMerchantId(merchantId);
		saveProductRecordDo.setStatus(SaveProductStatus.PROCESSING);
		saveProductRecordDo.setUploadType(uploadType);
		saveProductRecordDo.setUploadUserId(userId);
		saveProductRecordDo.setFileName(String.format(CREATE_PRODUCT_FILE_NAME, skuId, System.currentTimeMillis()));
		return saveProductRecordRepository.save(saveProductRecordDo);
	}

	public SaveProductRecordDo createSaveProductRecord(int userId, Integer merchantId, Integer uploadType, String fileName, int recordStatus) {
		SaveProductRecordDo saveProductRecordDo = new SaveProductRecordDo();
		saveProductRecordDo.setUploadTime(new Date());
		saveProductRecordDo.setMerchantId(merchantId);
		saveProductRecordDo.setStatus(recordStatus);
		saveProductRecordDo.setUploadType(uploadType);
		saveProductRecordDo.setUploadUserId(userId);
		saveProductRecordDo.setFileName(fileName);
		return saveProductRecordRepository.save(saveProductRecordDo);
	}

	public SaveProductRecordDo createQueueSaveProductRecord(UserDto userDto, Integer merchantId, Integer uploadType, String fileName, int recordStatus) {
		SaveProductRecordDo saveProductRecordDo = new SaveProductRecordDo();
		saveProductRecordDo.setMerchantId(merchantId);
		saveProductRecordDo.setUploadType(uploadType);
		saveProductRecordDo.setFileName(fileName);
		saveProductRecordDo.setStatus(recordStatus);
		saveProductRecordDo.setUploadUserId(userDto.getUserId());
		saveProductRecordDo.setUploadTime(new Date());
		saveProductRecordDo.setSource(SaveProductSource.MMS);
		saveProductRecordDo.setSourceIdentifier(userDto.getUserCode());
		saveProductRecordDo.setProtocol(SaveProductProtocol.QUEUE);
		return saveProductRecordRepository.save(saveProductRecordDo);
	}

	public SaveProductRecordDo createOapiSaveProductRecord(UserDto userDto, Integer merchantId, Integer uploadType, String fileName, Integer recordStatus) {
		SaveProductRecordDo record = generateOapiSaveProductRecord(userDto, merchantId, uploadType, fileName, recordStatus);
		return saveProductRecordRepository.save(record);
	}

	public SaveProductRecordDo createOapiSaveProductRecordWithQueueProtocol(UserDto userDto, Integer merchantId, Integer uploadType, String fileName, Integer recordStatus) {
		SaveProductRecordDo record = generateOapiSaveProductRecord(userDto, merchantId, uploadType, fileName, recordStatus);
		record.setProtocol(SaveProductProtocol.QUEUE);
		return saveProductRecordRepository.save(record);
	}

	private SaveProductRecordDo generateOapiSaveProductRecord(UserDto userDto, Integer merchantId, Integer uploadType, String fileName, Integer recordStatus) {
		SaveProductRecordDo record = new SaveProductRecordDo();
		record.setMerchantId(merchantId);
		record.setUploadType(uploadType);
		record.setFileName(fileName);
		record.setStatus(recordStatus);
		record.setUploadUserId(userDto.getUserId());
		record.setUploadTime(new Date());
		record.setSource(SaveProductSource.OPEN_API);
		record.setSourceIdentifier(userDto.getUserCode());
		return record;
	}

	@Transactional
	public List<String> updateRecordByProductMasterResult(SaveProductResultDto productMasterCreateProductResult, SaveProductRecordDo saveProductRecordDo, List<SaveProductRecordRowDo> saveProductRecordDoRows) {
		List<String> errorMessage = new ArrayList<>();
		ProductMasterErrorTypeEnum productMasterErrorTypeEnum = ProductMasterErrorTypeEnum.getEnum(productMasterCreateProductResult.getCode());
		if (productMasterErrorTypeEnum != null && ProductMasterErrorTypeEnum.RETRY_ERRORS.contains(productMasterErrorTypeEnum)) {
			log.error("record id: {} receive retry error, PM error code: {}", saveProductRecordDo.getId(), productMasterErrorTypeEnum.getErrorCode());
			saveProductRecordDo.setStatus(SaveProductStatus.REQUESTING_PM);
			saveProductRecordDoRows.forEach(row -> row.setStatus(SaveProductStatus.REQUESTING_PM));
			return errorMessage;
		}

		errorMessage = productMasterHelper.handleProductCreateOrEditMasterResponse(productMasterCreateProductResult, saveProductRecordDo.getUploadType());
		// check send pm result to judge status
		if (errorMessage.isEmpty()) {
			saveProductRecordDoRows.forEach(row -> row.setStatus(SaveProductStatus.CHECKING_PM));
			saveProductRecordDo.setPmTractId(productMasterCreateProductResult.getData());
			saveProductRecordDo.setStatus(SaveProductStatus.CHECKING_PM);
		} else {
			for (SaveProductRecordRowDo row : saveProductRecordDoRows) {
				row.setErrorMessage(StringUtil.generateErrorMessage(errorMessage));
				row.setStatus(SaveProductStatus.FAIL);
				log.error("record id: {} receive error, PM error code: {}, PM error message: {}", saveProductRecordDo.getId(), productMasterCreateProductResult.getCode(), productMasterCreateProductResult.getErrorDetails());
			}
			saveProductRecordDo.setStatus(SaveProductStatus.FAIL);
		}

		return errorMessage;
	}

	@Transactional
	public void updateRecordByProductMasterResult(ProductMasterResponseDto productMasterResponseDto, SaveProductRecordDo saveProductRecordDo, List<SaveProductRecordRowDo> saveProductRecordDoRows) {
		ProductMasterErrorTypeEnum productMasterErrorTypeEnum = productMasterResponseDto.getErrorCode();
		if (productMasterErrorTypeEnum != null && ProductMasterErrorTypeEnum.RETRY_ERRORS.contains(productMasterErrorTypeEnum)) {
			log.error("record id: {} receive retry error, PM error code: {}", saveProductRecordDo.getId(), productMasterErrorTypeEnum.getErrorCode());
			saveProductRecordDo.setStatus(SaveProductStatus.REQUESTING_PM);
			saveProductRecordDoRows.forEach(row -> row.setStatus(SaveProductStatus.REQUESTING_PM));
			return;
		}

		// check send pm result to judge status
		if (CollectionUtil.isEmpty(productMasterResponseDto.getErrorMessageList())) {
			saveProductRecordDoRows.forEach(row -> row.setStatus(SaveProductStatus.CHECKING_PM));
			saveProductRecordDo.setPmTractId(productMasterResponseDto.getTraceId());
			saveProductRecordDo.setStatus(SaveProductStatus.CHECKING_PM);
		} else {
			for (SaveProductRecordRowDo row : saveProductRecordDoRows) {
				row.setErrorMessage(StringUtil.generateErrorMessage(productMasterResponseDto.getErrorMessageList()));
				row.setStatus(SaveProductStatus.FAIL);
				log.error("record id: {} receive error, PM error code: {}, PM error message: {}", saveProductRecordDo.getId(), productMasterResponseDto.getErrorCode(), productMasterResponseDto.getErrorMessageList());
			}
			saveProductRecordDo.setStatus(SaveProductStatus.FAIL);
		}
	}

	/**
	 * Creates and saves a product record based on the provided parameters.
	 *
	 * @param userDto The user data transfer object containing user information
	 * @param merchantId The ID of the merchant
	 * @param uploadType The type of upload (e.g., file, manual)
	 * @param fileName Name of the uploaded file
	 * @param recordStatus Status of the product record
	 * @param clientIp IP address of the client
	 * @param protocol Protocol used for the upload, HTTP as default
	 * @return SaveProductRecordDo The saved product record entity
	 */
	public SaveProductRecordDo createSaveProductRecord(UserDto userDto, Integer merchantId,
		Integer uploadType, String fileName, Integer recordStatus, String clientIp, String protocol) {
		boolean isOpenAPI = userDto.getUserCode()
			.startsWith(OapiHelper.OPEN_API_SOURCE_IDENTIFIER_PREFIX);
		SaveProductRecordDo record = new SaveProductRecordDo();
		record.setMerchantId(merchantId);
		record.setUploadType(uploadType);
		record.setFileName(fileName);
		record.setStatus(recordStatus);
		record.setUploadUserId(userDto.getUserId());
		record.setUploadTime(new Date());
		record.setUserIp(clientIp);
		record.setSource(isOpenAPI ? SaveProductSource.OPEN_API : SaveProductSource.MMS);
		record.setSourceIdentifier(userDto.getUserCode());
		record.setProtocol(protocol);
		return saveProductRecordRepository.save(record);
	}
}
