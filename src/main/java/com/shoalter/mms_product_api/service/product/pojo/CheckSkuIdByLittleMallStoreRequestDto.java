package com.shoalter.mms_product_api.service.product.pojo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class CheckSkuIdByLittleMallStoreRequestDto {
	@JsonProperty("little_mall_store_code")
	@SerializedName("little_mall_store_code")
	private String littleMallStoreCode;
	@JsonProperty(value ="sku_id",required = true)
	@SerializedName("sku_id")
	private List<String> skuId;
}
