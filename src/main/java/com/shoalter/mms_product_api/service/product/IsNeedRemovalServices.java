package com.shoalter.mms_product_api.service.product;

import com.shoalter.mms_product_api.config.product.SysParmSegmentEnum;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.service.product.helper.SysParamHelper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@RequiredArgsConstructor
@Service
public class IsNeedRemovalServices {

	private final SysParamHelper sysParamHelper;

	public ResponseDto<Boolean> start(String buCode, List<String> productTypeCodeList) {
		List<String> categoryForRemovalServiceList = sysParamHelper.getSplitSystemParamsBySegmentAndBuCode(SysParmSegmentEnum.CATEGORY_FOR_REMOVAL_SERVICE, buCode);
		for (String productTypeCode : productTypeCodeList) {
			for (String categoryForRemovalService : categoryForRemovalServiceList) {
				if (productTypeCode.startsWith(categoryForRemovalService)) {
					return ResponseDto.success(true);
				}
			}
		}

		return ResponseDto.success(false);
	}

}
