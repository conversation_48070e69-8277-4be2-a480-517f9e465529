package com.shoalter.mms_product_api.service.product;

import com.shoalter.mms_product_api.dao.repository.system.SysParmRepository;
import com.shoalter.mms_product_api.dao.repository.system.pojo.SysParmDo;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import static com.shoalter.mms_product_api.config.type.ProductReadyMethodType.*;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@RequiredArgsConstructor
@Service
public class FindReadyDaysService {

    private final SysParmRepository sysParmRepository;

    public ResponseDto<List<SysParmDo>> start(String buCode, String productReadyMethodCode) {
        List<SysParmDo> sysParmDoList = findProductReadyDays(buCode, productReadyMethodCode);
        return ResponseDto.<List<SysParmDo>>builder().data(sysParmDoList).status(1).build();
    }

    private List<SysParmDo> findProductReadyDays(String buCode, String productReadyMethodCode) {
        switch (productReadyMethodCode) {
            case MERCHANT_DELIVERY:
			case MAINLAND_DELIVERY:
			case DISPLAY_STORE:
				return sysParmRepository.findBySegmentAndBuCode("PRODUCT_READY_DAYS", buCode);
            case STANDARD_DELIVERY_MERCHANT_DELIVER_TO_WAREHOUSE:
            case STANDARD_DELIVERY_PICKUP_BY_THIRD_PARTY:
            case E_VOUCHER:
            case NON_STANDARD_DELIVERY:
			case THIRD_PARTY:
			case STANDARD_DELIVERY_SAME_DAY_IN_HUB:
                return sysParmRepository.findBySegmentAndBuCodeAndCode("PRODUCT_READY_DAYS_DEFAULT", buCode, productReadyMethodCode);
            case OVERSEA_DELIVERY:
                return sysParmRepository.findBySegmentAndBuCodeAndCodeList("PRODUCT_READY_DAYS", buCode, List.of("7", "11"));
			case CONSIGNMENT:
				return sysParmRepository.findBySegmentAndBuCodeAndCodeList("PRODUCT_READY_DAYS", buCode, List.of("0", "1"));
			case HYBRID_DELIVERY_CONSOLIDATED:
				return sysParmRepository.findBySegmentAndBuCodeAndCodeList("PRODUCT_READY_DAYS", buCode, IntStream.rangeClosed(3, 5).mapToObj(String::valueOf).collect(Collectors.toList()));
        }
        return null;
    }
}
