package com.shoalter.mms_product_api.service.product;

import com.google.gson.Gson;
import com.shoalter.mms_product_api.config.product.ProductMasterBusinessUnitType;
import com.shoalter.mms_product_api.config.product.SaveProductStatus;
import com.shoalter.mms_product_api.config.product.SaveProductStatusEnum;
import com.shoalter.mms_product_api.config.product.SaveProductType;
import com.shoalter.mms_product_api.config.product.SaveProductTypeEnum;
import com.shoalter.mms_product_api.config.type.ConstantType;
import com.shoalter.mms_product_api.dao.repository.api.SaveProductRecordRepository;
import com.shoalter.mms_product_api.dao.repository.api.SaveProductRecordRowRepository;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordDo;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordRowDo;
import com.shoalter.mms_product_api.exception.SystemException;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.product.helper.CheckProductHelper;
import com.shoalter.mms_product_api.service.product.helper.ProductMasterHelper;
import com.shoalter.mms_product_api.service.product.pojo.*;
import com.shoalter.mms_product_api.util.CollectionUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@RequiredArgsConstructor
@Service
@Slf4j
public class EditPackagingInfoService {

	private final ProductMasterHelper productMasterHelper;
	private final CheckProductHelper checkProductHelper;
	private final SaveProductRecordRepository saveProductRecordRepository;
	private final SaveProductRecordRowRepository saveProductRecordRowRepository;
	private final MessageSource messageSource;
	private final Gson gson;

	@Transactional
	public ResponseDto<ProductRecordResponseDto> start(UserDto userDto, EditProductPackagingInfoDto editProductPackagingInfoDto) {
		ProductMasterDto productMasterDto = findProduct(userDto, editProductPackagingInfoDto);
		List<String> errorMessageList = checkProduct(productMasterDto);
		if (CollectionUtil.isNotEmpty(errorMessageList)) {
			return ResponseDto.fail(errorMessageList);
		}
		Long recordId = saveRecord(userDto, productMasterDto);
		ProductRecordResponseDto responseDto = ProductRecordResponseDto.builder()
				.recordId(recordId)
				.build();
		return ResponseDto.<ProductRecordResponseDto>builder().status(1).data(responseDto).build();
	}

	private ProductMasterDto findProduct(UserDto userDto, EditProductPackagingInfoDto editProductPackagingInfoDto) {
		List<String> uuidList = List.of(editProductPackagingInfoDto.getUuid());
		ProductMasterSearchRequestDto productMasterSearchRequestDto = ProductMasterSearchRequestDto.builder().uuids(uuidList).build();
		List<ProductMasterResultDto> productList = productMasterHelper.requestProductsByUuid(userDto, productMasterSearchRequestDto);
		if (productList.isEmpty()) {
			throw new SystemException(messageSource.getMessage("message51", null, null));
		}
		ProductMasterResultDto product = productList.get(0);
		ProductMasterDto productMasterDto = gson.fromJson(gson.toJson(product), ProductMasterDto.class);
		productMasterDto.setBuToSend(List.of(ProductMasterBusinessUnitType.HKTV, ProductMasterBusinessUnitType.THIRD_PL));
		productMasterDto.setWeight(editProductPackagingInfoDto.getWeight());
		productMasterDto.setWeightUnit(editProductPackagingInfoDto.getWeightUnit());
		productMasterDto.setPackingHeight(editProductPackagingInfoDto.getPackingHeight());
		productMasterDto.setPackingLength(editProductPackagingInfoDto.getPackingLength());
		productMasterDto.setPackingDepth(editProductPackagingInfoDto.getPackingDepth());
		productMasterDto.setPackingDimensionUnit(editProductPackagingInfoDto.getPackingDimensionUnit());
		return productMasterDto;
	}

	private List<String> checkProduct(ProductMasterDto productMasterDto) {
		List<String> errorMessageList = new ArrayList<>();
		if (productMasterDto.getAdditional().getHktv() != null) {
			String buCode = ConstantType.PLATFORM_CODE_HKTV;
			String deliveryMethodCode = productMasterDto.getAdditional().getHktv().getDeliveryMethod();
			BigDecimal weight = productMasterDto.getWeight();
			String weightUnit = productMasterDto.getWeightUnit();
			CheckProductResultDto checkWeightLimitByDeliveryResult =
					checkProductHelper.checkWeightLimitByDeliveryMethod(buCode, deliveryMethodCode, weight, weightUnit);
			errorMessageList.addAll(checkWeightLimitByDeliveryResult.getErrorMessageList());
		}
		return errorMessageList;
	}

	private Long saveRecord(UserDto userDto, ProductMasterDto productMasterDto) {
		SaveProductRecordDo saveProductRecordDo = new SaveProductRecordDo();
		saveProductRecordDo.setUploadType(SaveProductType.SINGLE_EDIT_PRODUCT_PACKAGING_INFO);
		saveProductRecordDo.setStatus(SaveProductStatus.PROCESSING);
		saveProductRecordDo.setUploadUserId(userDto.getUserId());
		saveProductRecordDo.setUploadTime(new Date());
		saveProductRecordDo.setMerchantId(productMasterDto.getMerchantId());
		saveProductRecordDo = saveProductRecordRepository.save(saveProductRecordDo);

		SaveProductRecordRowDo saveProductRecordRowDo = new SaveProductRecordRowDo();
		saveProductRecordRowDo.setRecordId(saveProductRecordDo.getId());
		saveProductRecordRowDo.setUuid(productMasterDto.getUuid());
		saveProductRecordRowDo.setSku(productMasterDto.getSkuId());
		SingleEditProductDto singleEditProductDto = new SingleEditProductDto();
		singleEditProductDto.setProduct(productMasterDto);
		saveProductRecordRowDo.setStatus(SaveProductStatus.PROCESSING);
		saveProductRecordRowDo = saveProductRecordRowRepository.save(saveProductRecordRowDo);
		singleEditProductDto.getProduct().setRecordRowId(saveProductRecordRowDo.getId());
		saveProductRecordRowDo.setContent(gson.toJson(singleEditProductDto));
		saveProductRecordRowRepository.save(saveProductRecordRowDo);

		log.info("create record id: {}, save product type: {}, row size: {}, upload user id: {}, status: {}", saveProductRecordDo.getId(), SaveProductTypeEnum.getProductTypeName(saveProductRecordDo.getUploadType()), 1, saveProductRecordDo.getUploadUserId(), SaveProductStatusEnum.getProductStatusName(saveProductRecordDo.getStatus()));

		return saveProductRecordDo.getId();
	}
}
