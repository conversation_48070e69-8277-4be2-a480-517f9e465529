package com.shoalter.mms_product_api.service.product;

import com.shoalter.mms_product_api.config.product.edit_column.TemplateTypeEnum;
import com.shoalter.mms_product_api.config.type.ConstantType;
import com.shoalter.mms_product_api.dao.mapper.businessUnit.pojo.BusinessPlatformDo;
import com.shoalter.mms_product_api.dao.repository.system.pojo.SysParmDo;
import com.shoalter.mms_product_api.exception.NoDataException;
import com.shoalter.mms_product_api.service.product.helper.EditProductTemplateHelper;
import com.shoalter.mms_product_api.service.product.pojo.DownloadEditProductTemplateRequestDto;
import com.shoalter.mms_product_api.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Service
@Slf4j
@RequiredArgsConstructor
public class EditProductTemplateService {

	private final EditProductTemplateHelper editProductTemplateHelper;

	public HttpEntity<ByteArrayResource> start(DownloadEditProductTemplateRequestDto requestDto) {
		if (requestDto.getTemplateType() == null || TemplateTypeEnum.ALL_COLUMN == requestDto.getTemplateType()) {
			throw new NoDataException();
		}

		String dateString = DateTimeFormatter.ofPattern(ConstantType.DATE_TIME_FORMAT_ONLY_NUMBER).format(LocalDateTime.now());
		ByteArrayOutputStream os = new ByteArrayOutputStream();
		Pair<BusinessPlatformDo, List<SysParmDo>> templateNecessaryData = editProductTemplateHelper.getTemplateNecessaryData();
		Workbook workbook = editProductTemplateHelper.generateEditTemplate(templateNecessaryData, requestDto.getTemplateType(), requestDto.getStoreId(),
			requestDto.getContractId(), false).getLeft();
		try {
			workbook.write(os);
			workbook.close();
		} catch (IOException e) {
			log.error(e.getMessage(), e);
		}
		return new ResponseEntity<>(new ByteArrayResource(os.toByteArray()), getResponseHeader(requestDto.getTemplateType(), dateString, StringUtil.FILE_EXTENSION_EXCEL), HttpStatus.OK);
	}

	private String generateFileName(TemplateTypeEnum templateType, String postfix, String fileExtension) {
		return String.format("Edit_Product_%s_Templates_%s.%s", templateType.name(), postfix, fileExtension);
	}

	public HttpHeaders getResponseHeader(TemplateTypeEnum templateType, String dateString, String fileExtension) {
		String fileName = generateFileName(templateType, dateString, fileExtension);
		HttpHeaders header = new HttpHeaders();
		header.setContentType(MediaType.APPLICATION_OCTET_STREAM);
		header.set(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + fileName);
		return header;
	}


}
