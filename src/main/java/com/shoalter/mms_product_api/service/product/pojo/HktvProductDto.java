package com.shoalter.mms_product_api.service.product.pojo;

import com.fasterxml.jackson.annotation.JsonGetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.google.gson.annotations.SerializedName;
import com.shoalter.mms_product_api.config.product.OnlineStatusEnum;
import com.shoalter.mms_product_api.config.security.xss.XssStringDeserializer;
import com.shoalter.mms_product_api.service.bundle.pojo.BundleSettingRequestDto;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class HktvProductDto implements Serializable {

	// hktv 特有的產品資料
	@JsonProperty("contract_no")
	@SerializedName("contract_no")
	private Integer contractNo;
	private String stores;
	@JsonProperty("product_ready_method")
	@SerializedName("product_ready_method")
	private String productReadyMethod;
	@JsonProperty("delivery_method")
	@SerializedName("delivery_method")
	private String deliveryMethod;
	@Schema(hidden = true)
	@JsonProperty("store_sku_id")
	@SerializedName("store_sku_id")
	private String storeSkuId;
	@JsonProperty("product_type_code")
	@SerializedName("product_type_code")
	private List<String> productTypeCode;
	@JsonProperty("primary_category_code")
	@SerializedName("primary_category_code")
	private String primaryCategoryCode;
	@JsonProperty("is_primary_sku")
	@SerializedName("is_primary_sku")
	private String isPrimarySku;
	private String visibility;
	@JsonProperty("sku_short_description_en")
	@SerializedName("sku_short_description_en")
	@JsonDeserialize(using = XssStringDeserializer.class)
	private String skuShortDescriptionEn;
	@JsonProperty("sku_short_description_ch")
	@SerializedName("sku_short_description_ch")
	@JsonDeserialize(using = XssStringDeserializer.class)
	private String skuShortDescriptionCh;
	@JsonProperty("sku_short_description_sc")
	@SerializedName("sku_short_description_sc")
	@JsonDeserialize(using = XssStringDeserializer.class)
	private String skuShortDescriptionSc;
	@JsonProperty("sku_long_description_en")
	@SerializedName("sku_long_description_en")
	@JsonDeserialize(using = XssStringDeserializer.class)
	private String skuLongDescriptionEn;
	@JsonProperty("sku_long_description_ch")
	@SerializedName("sku_long_description_ch")
	@JsonDeserialize(using = XssStringDeserializer.class)
	private String skuLongDescriptionCh;
	@JsonProperty("sku_long_description_sc")
	@SerializedName("sku_long_description_sc")
	@JsonDeserialize(using = XssStringDeserializer.class)
	private String skuLongDescriptionSc;
	@JsonProperty("feature_start_time")
	@SerializedName("feature_start_time")
	private String featureStartTime;
	@JsonProperty("feature_end_time")
	@SerializedName("feature_end_time")
	private String featureEndTime;
	@JsonProperty("voucher_type")
	@SerializedName("voucher_type")
	private String voucherType;
	@JsonProperty("voucher_display_type")
	@SerializedName("voucher_display_type")
	private String voucherDisplayType;
	@JsonProperty("expiry_type")
	@SerializedName("expiry_type")
	private String expiryType;
	@JsonProperty("redeem_start_date")
	@SerializedName("redeem_start_date")
	private String redeemStartDate;
	@JsonProperty("voucher_template_type")
	@SerializedName("voucher_template_type")
	private String voucherTemplateType;
	@JsonProperty("fixed_redemption_date")
	@SerializedName("fixed_redemption_date")
	private String fixedRedemptionDate;
	@JsonProperty("upon_purchase_date")
	@SerializedName("upon_purchase_date")
	private Integer uponPurchaseDate;
	@JsonProperty("fine_print_en")
	@SerializedName("fine_print_en")
	@JsonDeserialize(using = XssStringDeserializer.class)
	private String finePrintEn;
	@JsonProperty("fine_print_ch")
	@SerializedName("fine_print_ch")
	@JsonDeserialize(using = XssStringDeserializer.class)
	private String finePrintCh;
	@JsonProperty("fine_print_sc")
	@SerializedName("fine_print_sc")
	@JsonDeserialize(using = XssStringDeserializer.class)
	private String finePrintSc;
	@JsonProperty("term_name")
	@SerializedName("term_name")
	private String termName;
	private String currency;
	private BigDecimal cost;
	@JsonProperty("selling_price")
	@SerializedName("selling_price")
	private BigDecimal sellingPrice;
	private String style;
	@JsonProperty("discount_text_en")
	@SerializedName("discount_text_en")
	@JsonDeserialize(using = XssStringDeserializer.class)
	private String discountTextEn;
	@JsonProperty("discount_text_ch")
	@SerializedName("discount_text_ch")
	@JsonDeserialize(using = XssStringDeserializer.class)
	private String discountTextCh;
	@JsonProperty("discount_text_sc")
	@SerializedName("discount_text_sc")
	@JsonDeserialize(using = XssStringDeserializer.class)
	private String discountTextSc;
	@JsonProperty("mall_dollar")
	@SerializedName("mall_dollar")
	private BigDecimal mallDollar;
	@JsonProperty("vip_mall_dollar")
	@SerializedName("vip_mall_dollar")
	private BigDecimal vipMallDollar;
	@JsonProperty("user_max")
	@SerializedName("user_max")
	private Long userMax;
	@JsonProperty("main_photo")
	@SerializedName("main_photo")
	private String mainPhoto;
	@JsonProperty("main_video")
	@SerializedName("main_video")
	private String mainVideo;
	@JsonProperty("variant_product_photo")
	@SerializedName("variant_product_photo")
	private List<String> variantProductPhoto;
	@JsonProperty("other_photo")
	@SerializedName("other_photo")
	private List<String> otherPhoto;
	@JsonProperty("advertising_photo")
	@SerializedName("advertising_photo")
	private String advertisingPhoto;
	@JsonProperty("video_link")
	@SerializedName("video_link")
	private String videoLink;
	@JsonProperty("video_link_text_en")
	@SerializedName("video_link_text_en")
	private String videoLinkTextEn;
	@JsonProperty("video_link_text_ch")
	@SerializedName("video_link_text_ch")
	private String videoLinkTextCh;
	@JsonProperty("video_link_text_sc")
	@SerializedName("video_link_text_sc")
	private String videoLinkTextSc;
	@JsonProperty("video_link2")
	@SerializedName("video_link2")
	private String videoLink2;
	@JsonProperty("video_link_text_en2")
	@SerializedName("video_link_text_en2")
	private String videoLinkTextEn2;
	@JsonProperty("video_link_text_ch2")
	@SerializedName("video_link_text_ch2")
	private String videoLinkTextCh2;
	@JsonProperty("video_link_text_sc2")
	@SerializedName("video_link_text_sc2")
	private String videoLinkTextSc2;
	@JsonProperty("video_link3")
	@SerializedName("video_link3")
	private String videoLink3;
	@JsonProperty("video_link_text_en3")
	@SerializedName("video_link_text_en3")
	private String videoLinkTextEn3;
	@JsonProperty("video_link_text_ch3")
	@SerializedName("video_link_text_ch3")
	private String videoLinkTextCh3;
	@JsonProperty("video_link_text_sc3")
	@SerializedName("video_link_text_sc3")
	private String videoLinkTextSc3;
	@JsonProperty("video_link4")
	@SerializedName("video_link4")
	private String videoLink4;
	@JsonProperty("video_link_text_en4")
	@SerializedName("video_link_text_en4")
	private String videoLinkTextEn4;
	@JsonProperty("video_link_text_ch4")
	@SerializedName("video_link_text_ch4")
	private String videoLinkTextCh4;
	@JsonProperty("video_link_text_sc4")
	@SerializedName("video_link_text_sc4")
	private String videoLinkTextSc4;
	@JsonProperty("video_link5")
	@SerializedName("video_link5")
	private String videoLink5;
	@JsonProperty("video_link_text_en5")
	@SerializedName("video_link_text_en5")
	private String videoLinkTextEn5;
	@JsonProperty("video_link_text_ch5")
	@SerializedName("video_link_text_ch5")
	private String videoLinkTextCh5;
	@JsonProperty("video_link_text_sc5")
	@SerializedName("video_link_text_sc5")
	private String videoLinkTextSc5;
	@JsonProperty("warehouse_id")
	@SerializedName("warehouse_id")
	private Integer warehouseId;
	@JsonProperty("warehouse_code")
	@SerializedName("warehouse_code")
	//web request data = storefront store code + sequence number
	//example:"H123-98"
	private String warehouseCode;
	@JsonProperty("packing_spec_en")
	@SerializedName("packing_spec_en")
	@JsonDeserialize(using = XssStringDeserializer.class)
	private String packingSpecEn;
	@JsonProperty("packing_spec_ch")
	@SerializedName("packing_spec_ch")
	@JsonDeserialize(using = XssStringDeserializer.class)
	private String packingSpecCh;
	@JsonProperty("packing_spec_sc")
	@SerializedName("packing_spec_sc")
	@JsonDeserialize(using = XssStringDeserializer.class)
	private String packingSpecSc;
	@JsonProperty("invoice_remarks_en")
	@SerializedName("invoice_remarks_en")
	@JsonDeserialize(using = XssStringDeserializer.class)
	private String invoiceRemarksEn;
	@JsonProperty("invoice_remarks_ch")
	@SerializedName("invoice_remarks_ch")
	@JsonDeserialize(using = XssStringDeserializer.class)
	private String invoiceRemarksCh;
	@JsonProperty("invoice_remarks_sc")
	@SerializedName("invoice_remarks_sc")
	@JsonDeserialize(using = XssStringDeserializer.class)
	private String invoiceRemarksSc;
	@JsonProperty("return_days")
	@SerializedName("return_days")
	private Integer returnDays;
	@JsonProperty("product_ready_days")
	@SerializedName("product_ready_days")
	private String productReadyDays;
	@JsonProperty("pickup_days")
	@SerializedName("pickup_days")
	private String pickupDays;
	@JsonProperty("pickup_timeslot")
	@SerializedName("pickup_timeslot")
	private String pickupTimeslot;
	private String urgent;
	private String warranty;
	@JsonProperty("need_removal_services")
	@SerializedName("need_removal_services")
	private String needRemovalServices;
	@JsonProperty("goods_type")
	@SerializedName("goods_type")
	private String goodsType;
	@JsonProperty("warranty_period_unit")
	@SerializedName("warranty_period_unit")
	private String warrantyPeriodUnit;
	@JsonProperty("warranty_period")
	@SerializedName("warranty_period")
	private Integer warrantyPeriod;
	@JsonProperty("warranty_supplier_en")
	@SerializedName("warranty_supplier_en")
	@JsonDeserialize(using = XssStringDeserializer.class)
	private String warrantySupplierEn;
	@JsonProperty("warranty_supplier_ch")
	@SerializedName("warranty_supplier_ch")
	@JsonDeserialize(using = XssStringDeserializer.class)
	private String warrantySupplierCh;
	@JsonProperty("warranty_supplier_sc")
	@SerializedName("warranty_supplier_sc")
	@JsonDeserialize(using = XssStringDeserializer.class)
	private String warrantySupplierSc;
	@JsonProperty("service_centre_address_en")
	@SerializedName("service_centre_address_en")
	@JsonDeserialize(using = XssStringDeserializer.class)
	private String serviceCentreAddressEn;
	@JsonProperty("service_centre_address_ch")
	@SerializedName("service_centre_address_ch")
	@JsonDeserialize(using = XssStringDeserializer.class)
	private String serviceCentreAddressCh;
	@JsonProperty("service_centre_address_sc")
	@SerializedName("service_centre_address_sc")
	@JsonDeserialize(using = XssStringDeserializer.class)
	private String serviceCentreAddressSc;
	@JsonProperty("service_centre_email")
	@SerializedName("service_centre_email")
	private String serviceCentreEmail;
	@JsonProperty("service_centre_contact")
	@SerializedName("service_centre_contact")
	private String serviceCentreContact;
	@JsonProperty("warranty_remark_en")
	@SerializedName("warranty_remark_en")
	@JsonDeserialize(using = XssStringDeserializer.class)
	private String warrantyRemarkEn;
	@JsonProperty("warranty_remark_ch")
	@SerializedName("warranty_remark_ch")
	@JsonDeserialize(using = XssStringDeserializer.class)
	private String warrantyRemarkCh;
	@JsonProperty("warranty_remark_sc")
	@SerializedName("warranty_remark_sc")
	@JsonDeserialize(using = XssStringDeserializer.class)
	private String warrantyRemarkSc;
	@JsonProperty("online_status")
	@SerializedName("online_status")
	private OnlineStatusEnum onlineStatus;
	@JsonProperty("rm_code")
	@SerializedName("rm_code")
	private String rmCode;
	@JsonProperty("virtual_store")
	@SerializedName("virtual_store")
	private String virtualStore;
	/**
	 * set to hybris replicateImage field, if true : hybris will sync primary/variant
	 * variant_product_photo with same images product master only save, unused
	 */
	@JsonProperty("replicate_to_other_variants_skus")
	@SerializedName("replicate_to_other_variants_skus")
	@JsonInclude(JsonInclude.Include.NON_NULL)
	private Boolean replicateToOtherVariantsSkus;
	@JsonProperty("presell_fruit")
	@SerializedName("presell_fruit")
	private String preSellFruit;
	@JsonProperty("physical_store")
	@SerializedName("physical_store")
	private String physicalStore;
	@JsonProperty("thumbnail_video")
	@SerializedName("thumbnail_video")
	private String thumbnailVideo;
	@JsonProperty("storage_type")
	@SerializedName("storage_type")
	private String storageType;
	@Schema(hidden = true)
	@JsonProperty("commission_rate")
	@SerializedName("commission_rate")
	private BigDecimal commissionRate;
	@JsonProperty("store_id")
	@SerializedName("store_id")
	private Integer storeId;
	@JsonProperty("video_filename")
	@SerializedName("video_filename")
	private String videoFilename;
	@JsonProperty("delivery_district")
	@SerializedName("delivery_district")
	private List<String> deliveryDistrict;
	@JsonProperty("offline_due_to_rollback")
	private Boolean offlineDueToRollback;
	@Schema(hidden = true)
	@JsonProperty("status")
	@SerializedName("status")
	private String status;
	@JsonProperty("affiliate_url")
	@SerializedName("affiliate_url")
	private String affiliateUrl;
	@JsonProperty("extended_warranty_percentage_setting")
	@SerializedName("extended_warranty_percentage_setting")
	private BigDecimal ewPercentageSetting;
	@JsonProperty("claim_link_en")
	@SerializedName("claim_link_en")
	private String claimLinkEn;
	@JsonProperty("claim_link_ch")
	@SerializedName("claim_link_ch")
	private String claimLinkCh;
	@JsonProperty("claim_link_sc")
	@SerializedName("claim_link_sc")
	private String claimLinkSc;
	@JsonProperty("bind_extended_warranty_store_sku_ids")
	@SerializedName("bind_extended_warranty_store_sku_ids")
	private List<String> bindExtendedWarrantyStoreSkuIds;

	@Schema(hidden = true)
	@JsonProperty("hasEditing")
	@SerializedName("hasEditing")
	private Boolean hasEditing;

	@JsonProperty("bundle_setting")
	@SerializedName("bundle_setting")
	private BundleSettingRequestDto bundleSetting;

	@JsonProperty("storefront_store_code")
	@SerializedName("storefront_store_code")
	private String storefrontStoreCode;

	@JsonProperty("partner_info")
	@SerializedName("partner_info")
	private ProductPartnerInfoDto partnerInfo;

	// for child sku change warehouse or online status change to offline that need to check bundles be offline
	@JsonIgnore
	private boolean isCheckOffLineBundle;

	@JsonGetter("skuCode")
	public boolean getPrintableIsCheckOffLineBundle() {
		return isCheckOffLineBundle;
	}

	@JsonProperty("external_platform")
	@SerializedName("external_platform")
	private ExternalPlatform externalPlatform;

	@JsonProperty("force_offline")
	@SerializedName("force_offline")
	private Boolean forceOffline;

	@JsonProperty("case_number")
	@SerializedName("case_number")
	private String caseNumber;
}
