package com.shoalter.mms_product_api.service.product;

import com.shoalter.mms_product_api.config.interceptor.ClientIpHolder;
import com.shoalter.mms_product_api.config.product.SaveProductStatus;
import com.shoalter.mms_product_api.config.product.SaveProductStatusEnum;
import com.shoalter.mms_product_api.config.product.SaveProductTypeEnum;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordDo;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordRowDo;
import com.shoalter.mms_product_api.exception.NoDataException;
import com.shoalter.mms_product_api.mapper.LittleMallRelationDataMapper;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.product.helper.BatchCheckHelper;
import com.shoalter.mms_product_api.service.product.helper.CheckLittleMallProductHelper;
import com.shoalter.mms_product_api.service.product.helper.PermissionHelper;
import com.shoalter.mms_product_api.service.product.helper.ProductMasterHelper;
import com.shoalter.mms_product_api.service.product.helper.SaveProductRecordHelper;
import com.shoalter.mms_product_api.service.product.helper.SaveProductRecordRowHelper;
import com.shoalter.mms_product_api.service.product.pojo.SingleEditProductDto;
import com.shoalter.mms_product_api.service.product.pojo.littlemall.BatchLittleMallProductRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.littlemall.LittleMallBatchDto;
import com.shoalter.mms_product_api.service.product.pojo.littlemall.LittleMallRelationDto;
import com.shoalter.mms_product_api.service.product.pojo.productmaster.request.ProductMasterRelationSettingRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.productmaster.response.ProductMasterRelationSettingResponseDto;
import com.shoalter.mms_product_api.util.CollectionUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class LittleMallBatchEditService {
	private final PermissionHelper permissionHelper;
	private final SaveProductRecordHelper saveProductRecordHelper;
	private final SaveProductRecordRowHelper saveProductRecordRowHelper;
	private final CheckLittleMallProductHelper checkLittleMallProductHelper;
	private final ProductMasterHelper productMasterHelper;
	private final LittleMallRelationDataMapper littleMallRelationDataMapper;
	private final MessageSource messageSource;

	@Transactional
	public ResponseDto<Void> start(UserDto userDto, BatchLittleMallProductRequestDto batchLittleMallProductRequestDto, int uploadType) {

		// basic check
		permissionHelper.checkPermission(userDto, batchLittleMallProductRequestDto.getMerchantId());
		if (CollectionUtil.isEmpty(batchLittleMallProductRequestDto.getLittleMallBatchDtoList())) {
			throw new NoDataException();
		}
		if (batchLittleMallProductRequestDto.getLittleMallBatchDtoList().size() > BatchCheckHelper.MAXIMUM_10000) {
			return ResponseDto.fail(List.of(messageSource.getMessage("message16", new String[]{String.format("%,d", BatchCheckHelper.MAXIMUM_10000)}, null)));
		}

		List<String> errorMessages = checkDuplicated(batchLittleMallProductRequestDto);
		if (CollectionUtil.isNotEmpty(errorMessages)) {
			return ResponseDto.fail(errorMessages);
		}

		// TODO IF phase 2 relation can edit, need to remove set from query result
		batchLittleMallProductRequestDto.setRelations(generateLittleMallRelationDto(userDto, batchLittleMallProductRequestDto));

		Map<String, LittleMallRelationDto> relationProductIdMap = new HashMap<>();
		if (CollectionUtil.isNotEmpty(batchLittleMallProductRequestDto.getRelations())){
			for (LittleMallRelationDto relation : batchLittleMallProductRequestDto.getRelations()) {
				relationProductIdMap.putIfAbsent(relation.getProductId(), relation);
			}
		}

		// save product record
		SaveProductRecordDo saveProductRecordDo =
			saveProductRecordHelper.createSaveProductRecord(userDto, batchLittleMallProductRequestDto.getMerchantId(), uploadType,
				batchLittleMallProductRequestDto.getFileName(), SaveProductStatus.PROCESSING, ClientIpHolder.getClientIp());

		// save product record row
		List<SaveProductRecordRowDo> saveProductRecordRowDoList = batchLittleMallProductRequestDto.getLittleMallBatchDtoList().stream()
			.map(littleMallBatchDto -> {
				SingleEditProductDto singleEditProductDto = LittleMallBatchDto.convertToSingleEditProductDtoWithRelation(littleMallBatchDto, relationProductIdMap);
				return saveProductRecordRowHelper.generateProductRecordRowDo(saveProductRecordDo.getId(), singleEditProductDto, SaveProductStatus.PROCESSING, null);
			})
			.collect(Collectors.toList());
		saveProductRecordRowHelper.batchSaveSaveProductRecordRowDo(saveProductRecordRowDoList);
		log.info("create record id: {}, save product type: {}, row size: {}, upload user id: {}, status: {}", saveProductRecordDo.getId(), SaveProductTypeEnum.getProductTypeName(saveProductRecordDo.getUploadType()), saveProductRecordRowDoList.size(), saveProductRecordDo.getUploadUserId(), SaveProductStatusEnum.getProductStatusName(saveProductRecordDo.getStatus()));
		return ResponseDto.success(null);
	}

	private List<LittleMallRelationDto> generateLittleMallRelationDto(UserDto userDto, BatchLittleMallProductRequestDto batchLittleMallProductRequestDto) {
		if (CollectionUtil.isEmpty(batchLittleMallProductRequestDto.getLittleMallBatchDtoList())) {
			return new ArrayList<>();
		}
		Set<String> productIds = batchLittleMallProductRequestDto.getLittleMallBatchDtoList().stream().map(LittleMallBatchDto::getProductId).collect(Collectors.toSet());
		String storefrontStoreCode = batchLittleMallProductRequestDto.getLittleMallBatchDtoList().get(0).getStores();
		List<ProductMasterRelationSettingRequestDto> requests = productIds.stream().map(productId ->
				ProductMasterRelationSettingRequestDto.builder()
					.storefrontStoreCode(storefrontStoreCode)
					.productId(productId).build())
			.collect(Collectors.toList());

		List<ProductMasterRelationSettingResponseDto> productMasterResponseDtoList = productMasterHelper.requestLittleMallRelationSettingByParams(userDto, requests);
		if (productMasterResponseDtoList == null) {
			log.info("no product master result, storefrontStoreCode : {}, productIds : {}", storefrontStoreCode, productIds);
			throw new NoDataException();
		}
		return littleMallRelationDataMapper.toLittleMallRelationDto(productMasterResponseDtoList);
	}

	private List<String> checkDuplicated(BatchLittleMallProductRequestDto batchLittleMallProductRequestDto) {
		List<String> errorMessages = new ArrayList<>();
		List<LittleMallBatchDto> products = batchLittleMallProductRequestDto.getLittleMallBatchDtoList();
		errorMessages.add(checkLittleMallProductHelper.checkPrimarySkuDuplicated(products));
		errorMessages.add(checkLittleMallProductHelper.checkSkuIdDuplicated(products));
		return errorMessages.stream().filter(Objects::nonNull).collect(Collectors.toList());
	}
}
