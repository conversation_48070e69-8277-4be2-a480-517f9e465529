package com.shoalter.mms_product_api.service.product.pojo.littlemall;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class LittleMallSearchProductIdsRequestData {
	@JsonProperty("little_mall_storefront_store_code")
	@SerializedName("little_mall_storefront_store_code")
	private String littleMallStorefrontStoreCode;

	public static LittleMallSearchProductIdsRequestData generate(String littleMallStorefrontStoreCode) {
		return LittleMallSearchProductIdsRequestData.builder()
			.littleMallStorefrontStoreCode(littleMallStorefrontStoreCode)
			.build();
	}
}
