package com.shoalter.mms_product_api.service.product.template;


import com.google.gson.Gson;
import com.shoalter.mms_product_api.config.product.ExcelValidationName;
import com.shoalter.mms_product_api.config.product.edit_column.OnlineStatusTemplateColumnEnum;
import com.shoalter.mms_product_api.dao.mapper.businessUnit.pojo.BusinessPlatformDo;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordDo;
import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordRowDo;
import com.shoalter.mms_product_api.dao.repository.system.pojo.SysParmDo;
import com.shoalter.mms_product_api.service.product.AbstractReport;
import com.shoalter.mms_product_api.service.product.pojo.BatchEditOnlineStatusDto;
import com.shoalter.mms_product_api.service.product.pojo.HktvProductDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterDto;
import com.shoalter.mms_product_api.service.product.pojo.SetCellValueDto;
import com.shoalter.mms_product_api.service.product.pojo.SingleEditProductDto;
import com.shoalter.mms_product_api.util.ExcelUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

import static com.shoalter.mms_product_api.util.ExcelUtil.PRODUCT_STATUS_LIST;

@RequiredArgsConstructor
@Service
@Slf4j
public class ProductTemplateOnlineStatusHelper extends AbstractReport implements IProductTemplateHelper<OnlineStatusTemplateColumnEnum> {

	private final Gson gson;

	public Workbook setTemplateBodyColumn(Workbook workbook, List<SingleEditProductDto> productList, List<SysParmDo> sysParmList) {
		Workbook tempWorkbook = new SXSSFWorkbook((XSSFWorkbook) workbook, 100);
		Sheet dataSheet = tempWorkbook.getSheet(PRODUCT_SHEET_NAME);
		CellStyle bodyStyle = ExcelUtil.createBodyStyle(tempWorkbook, HorizontalAlignment.LEFT, false, false, false, false, false);
		AtomicInteger rowIndex = new AtomicInteger(1);

		productList.forEach(singleEditProductDto -> {
			ProductMasterDto product = singleEditProductDto.getProduct();
			HktvProductDto hktvProductDto = product.getAdditional().getHktv();
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), OnlineStatusTemplateColumnEnum.STORE_ID.getColumnNumber(), hktvProductDto.getStores());
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), OnlineStatusTemplateColumnEnum.SKU_ID.getColumnNumber(), product.getSkuId());
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), OnlineStatusTemplateColumnEnum.STORE_STATUS.getColumnNumber(), hktvProductDto.getOnlineStatus().name());
			rowIndex.addAndGet(1);
		});
		return tempWorkbook;
	}

	@Override
	public Workbook setErrorReportBodyColumn(Workbook workbook, List<SaveProductRecordRowDo> rows, List<SysParmDo> sysParmList, SaveProductRecordDo record) {
		Workbook tempWorkbook = new SXSSFWorkbook((XSSFWorkbook) workbook, 100);
		Sheet dataSheet = tempWorkbook.getSheet(PRODUCT_SHEET_NAME);
		CellStyle bodyStyle = ExcelUtil.createBodyStyle(tempWorkbook, HorizontalAlignment.LEFT, false, false, false, false, false);
		CellStyle lockBodyStyle = ExcelUtil.createBodyStyle(tempWorkbook, HorizontalAlignment.LEFT, false, false, false, false, false);
		lockBodyStyle.setLocked(true);
		AtomicInteger rowIndex = new AtomicInteger(1);
		SetCellValueDto setCellValueDto = SetCellValueDto.builder()
			.sheet(dataSheet)
			.lockStyle(lockBodyStyle)
			.notLockStyle(bodyStyle)
			.build();

		rows.forEach(row -> {
			setCellValueDto.setRowNumber(rowIndex.get());
			SingleEditProductDto singleEditProductDto = gson.fromJson(row.getContent(), SingleEditProductDto.class);
			BatchEditOnlineStatusDto batchEditOnlineStatusDto = singleEditProductDto.getBatchEditElement().getOnlineStatus();
			setCellValue(setCellValueDto, OnlineStatusTemplateColumnEnum.STORE_ID.getColumnNumber(), isLockColumn(OnlineStatusTemplateColumnEnum.STORE_ID), batchEditOnlineStatusDto.getStorefrontStoreCode());
			setCellValue(setCellValueDto, OnlineStatusTemplateColumnEnum.SKU_ID.getColumnNumber(), isLockColumn(OnlineStatusTemplateColumnEnum.SKU_ID), batchEditOnlineStatusDto.getSkuCode());
			setCellValue(setCellValueDto, OnlineStatusTemplateColumnEnum.STORE_STATUS.getColumnNumber(), isLockColumn(OnlineStatusTemplateColumnEnum.STORE_STATUS), batchEditOnlineStatusDto.getOnlineStatus().name());
			setCellValue(dataSheet, bodyStyle, rowIndex.get(), OnlineStatusTemplateColumnEnum.values().length, row.getErrorMessage());
			rowIndex.addAndGet(1);
		});
		return tempWorkbook;
	}

	@Override
	public int getColumnWidth(TemplateInterface<OnlineStatusTemplateColumnEnum> columnEnum) {
		if (columnEnum == OnlineStatusTemplateColumnEnum.SKU_ID) {
			return 7000;
		}
		return columnEnum.getColumnName().length() * 400;
	}

	@Override
	public boolean isLockColumn(TemplateInterface<OnlineStatusTemplateColumnEnum> columnEnum) {
		return columnEnum == OnlineStatusTemplateColumnEnum.STORE_ID ||
			columnEnum == OnlineStatusTemplateColumnEnum.SKU_ID;
	}

	@Override
	public void setLoveSheetValues(Sheet lovSheet, Map<String, Map<String, Object>> map, Num theColNum) {
		setLovSheetValue(lovSheet, ExcelValidationName.VALIDATION_PRODUCT_STATUS, PRODUCT_STATUS_LIST, theColNum, map);
	}
}
