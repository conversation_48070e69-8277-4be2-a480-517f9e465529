package com.shoalter.mms_product_api.service.product.pojo;

import com.shoalter.mms_product_api.service.extended_warranty.pojo.BatchUpdateEwProductBindingDto;
import lombok.Data;

@Data
public class BatchUpdateProductInfoDto {
	private BatchEditPackingDimensionDto packingDimension;
	private BatchEditPriceDto skuPrice;
	private BatchEditOnlineStatusDto onlineStatus;
	private BatchEditVisibilityDto visibility;
	private BatchUpdateEwProductBindingDto batchUpdateEwProductBindingDto;
	private BatchEditOverseaReserveRegionDto batchEditOverseaReserveRegionDto;
	private BatchEditTranslateDto batchEditTranslateDto;
	private BatchEditProductReadyDaysDto batchEditProductReadyDaysDto;
	private ProductForceOfflineDto productForceOfflineDto;
}
