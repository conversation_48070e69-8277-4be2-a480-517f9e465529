package com.shoalter.mms_product_api.service.product.pojo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import com.shoalter.mms_product_api.service.product.pojo.littlemall.LittleMallBatchDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class ProductMasterDto extends ProductMasterProductDto implements Serializable {
    @JsonProperty("bu_to_send")
	@SerializedName("bu_to_send")
    @Schema(hidden = true)
    private List<String> buToSend;
	@Schema(hidden = true)
    @JsonProperty("record_row_id")
	@SerializedName("record_row_id")
	private Long recordRowId;

	@Schema(hidden = true)
	private boolean isDisableTo3PL;

    public static ProductMasterDto convertFromProductMasterResultDto(ProductMasterResultDto productMasterResultDto) {
        ProductMasterDto productMasterDto = new ProductMasterDto();
        productMasterDto.setProductId(productMasterResultDto.getProductId());
        productMasterDto.setUuid(productMasterResultDto.getUuid());
        productMasterDto.setSkuId(productMasterResultDto.getSkuId());
        productMasterDto.setBrandId(productMasterResultDto.getBrandId());
        productMasterDto.setManufacturedCountry(productMasterResultDto.getManufacturedCountry());
        productMasterDto.setColourFamilies(productMasterResultDto.getColourFamilies());
        productMasterDto.setColor(productMasterResultDto.getColor());
        productMasterDto.setSizeSystem(productMasterResultDto.getSizeSystem());
        productMasterDto.setSize(productMasterResultDto.getSize());
        productMasterDto.setOption1(productMasterResultDto.getOption1());
        productMasterDto.setOption1Value(productMasterResultDto.getOption1Value());
        productMasterDto.setOption2(productMasterResultDto.getOption2());
        productMasterDto.setOption2Value(productMasterResultDto.getOption2Value());
        productMasterDto.setOption3(productMasterResultDto.getOption3());
        productMasterDto.setOption3Value(productMasterResultDto.getOption3Value());
        productMasterDto.setBarcodes(productMasterResultDto.getBarcodes());
        productMasterDto.setPackingHeight(productMasterResultDto.getPackingHeight());
        productMasterDto.setPackingLength(productMasterResultDto.getPackingLength());
        productMasterDto.setPackingDepth(productMasterResultDto.getPackingDepth());
        productMasterDto.setPackingDimensionUnit(productMasterResultDto.getPackingDimensionUnit());
        productMasterDto.setWeight(productMasterResultDto.getWeight());
        productMasterDto.setWeightUnit(productMasterResultDto.getWeightUnit());
        productMasterDto.setAdditional(productMasterResultDto.getAdditional());
        productMasterDto.setMerchantId(productMasterResultDto.getMerchantId());
        productMasterDto.setOriginalPrice(productMasterResultDto.getOriginalPrice());
        productMasterDto.setPackingBoxType(productMasterResultDto.getPackingBoxType());
        productMasterDto.setStorageTemperature(productMasterResultDto.getStorageTemperature());
        productMasterDto.setMinimumShelfLife(productMasterResultDto.getMinimumShelfLife());
        productMasterDto.setMerchantName(productMasterResultDto.getMerchantName());
        productMasterDto.setSkuNameEn(productMasterResultDto.getSkuNameEn());
        productMasterDto.setSkuNameCh(productMasterResultDto.getSkuNameCh());
        productMasterDto.setSkuNameSc(productMasterResultDto.getSkuNameSc());
		productMasterDto.setBundleSetting(productMasterResultDto.getBundleSetting());
		productMasterDto.setMmsCreateUser(productMasterResultDto.getMmsCreateUser());
		productMasterDto.setMmsCreateTime(productMasterResultDto.getMmsCreateTime());
		productMasterDto.setMmsModifiedUser(productMasterResultDto.getMmsModifiedUser());
		productMasterDto.setMmsModifiedTime(productMasterResultDto.getMmsModifiedTime());
		productMasterDto.setCartonSizeList(productMasterResultDto.getCartonSizeList());
        return productMasterDto;
    }

    public static ProductMasterDto convertFromLittleMallBatchDto(LittleMallBatchDto littleMallBatchDto) {
        ProductMasterDto product = new ProductMasterDto();
        product.setSkuId(littleMallBatchDto.getSkuId());
        product.setProductId(littleMallBatchDto.getProductId());
        product.setSkuNameCh(littleMallBatchDto.getSkuName());
        product.setOriginalPrice(littleMallBatchDto.getOriginalPrice());
        product.setMerchantId(littleMallBatchDto.getMerchantId());
        product.setMerchantName(littleMallBatchDto.getMerchantName());

        LittleMallProductDto littleMall = new LittleMallProductDto();
        littleMall.setIsPrimarySku(littleMallBatchDto.getIsPrimarySku());
        littleMall.setSellingPrice(littleMallBatchDto.getSellingPrice());
        littleMall.setVisibility(littleMallBatchDto.getVisibility());
        littleMall.setOnlineStatus(littleMallBatchDto.getOnlineStatus());
        littleMall.setDisplayInHktvmallCategory(littleMallBatchDto.getDisplayInHktvmallCategory());
        littleMall.setSkuLongDescriptionCh(littleMallBatchDto.getSkuLongDescriptionCh());
        littleMall.setMainPhoto(littleMallBatchDto.getMainPhoto());
        littleMall.setOtherPhoto(littleMallBatchDto.getOtherPhoto());
        littleMall.setStoreCode(littleMallBatchDto.getStores());
        littleMall.setStoreSkuId(littleMallBatchDto.getStores() + "_S_" + littleMallBatchDto.getSkuId());
		littleMall.setProductReadyMethod(littleMallBatchDto.getProductReadyMethod());
		littleMall.setProductField1(littleMallBatchDto.getProductField1());
		littleMall.setProductFieldCategory1(littleMallBatchDto.getProductFieldCategory1());
		littleMall.setProductFieldOption1(littleMallBatchDto.getProductFieldOption1());
		littleMall.setProductField2(littleMallBatchDto.getProductField2());
		littleMall.setProductFieldCategory2(littleMallBatchDto.getProductFieldCategory2());
		littleMall.setProductFieldOption2(littleMallBatchDto.getProductFieldOption2());
		littleMall.setProductField3(littleMallBatchDto.getProductField3());
		littleMall.setProductFieldCategory3(littleMallBatchDto.getProductFieldCategory3());
		littleMall.setProductFieldOption3(littleMallBatchDto.getProductFieldOption3());
		littleMall.setProductField4(littleMallBatchDto.getProductField4());
		littleMall.setProductFieldCategory4(littleMallBatchDto.getProductFieldCategory4());
		littleMall.setProductFieldOption4(littleMallBatchDto.getProductFieldOption4());

        BuProductDto additional = new BuProductDto();
        additional.setLittleMall(littleMall);
        product.setAdditional(additional);
        return product;
    }
}
