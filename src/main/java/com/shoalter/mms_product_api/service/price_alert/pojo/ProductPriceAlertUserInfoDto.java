package com.shoalter.mms_product_api.service.price_alert.pojo;

import com.shoalter.mms_product_api.dao.repository.merchant.pojo.UserNameAndEmailViewDo;
import com.shoalter.mms_product_api.dao.repository.merchant.pojo.UserStoreRoleDo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ProductPriceAlertUserInfoDto implements UserNameAndEmailViewDo {
	private Integer userId;
	private String userName;
	private String email;

	public static ProductPriceAlertUserInfoDto from(UserStoreRoleDo userStoreRoleDo) {
		return ProductPriceAlertUserInfoDto.builder()
			.userId(userStoreRoleDo.getUserId())
			.userName(userStoreRoleDo.getUserName())
			.email(userStoreRoleDo.getEmail())
			.build();
	}

}
