package com.shoalter.mms_product_api.service.price_alert.pojo;

import com.shoalter.mms_product_api.dao.repository.product.pojo.ProductPriceAlertDo;
import com.shoalter.mms_product_api.dao.repository.product.pojo.ProductPriceMonitorProductCheckDo;
import com.shoalter.mms_product_api.dao.repository.product.pojo.ProductPriceMonitorProductDo;
import com.shoalter.mms_product_api.dao.repository.rm.pojo.RmTeamUserInfo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ProductPriceAlertDto {

	private Integer merchantId;
	private String storefrontStoreCode;
	private String skuCode;
	private String productCode;
	private String skuNameCh;
	private BigDecimal tmallOriginalPrice;
	private BigDecimal tmallSellingPrice;
	private BigDecimal hktvOriginalPrice;
	private BigDecimal hktvSellingPrice;
	private String tmallUrl;
	private String status;
	private String brandName;
	private String rmCode;
	private String rmName;
	private String rmlName;

	private LocalDateTime lastUpdateDateTime;

	// To fetch RM team user info
	private Integer productPriceMonitorProductId;


	public static ProductPriceAlertDto fromEntity(ProductPriceAlertDo alert) {
		return ProductPriceAlertDto.builder()
			.merchantId(alert.getMerchantId())
			.skuCode(alert.getSkuCode())
			.skuNameCh(alert.getSkuNameTchi())
			.productCode(alert.getProductCode())
			.storefrontStoreCode(alert.getStore() != null ? alert.getStore().getStorefrontStoreCode() : null)
			.hktvOriginalPrice(alert.getSourceOriginalPrice())
			.hktvSellingPrice(alert.getSourceSellingPrice())
			.tmallOriginalPrice(alert.getTargetOriginalPrice())
			.tmallSellingPrice(alert.getTargetSellingPrice())
			.tmallUrl(alert.getTargetUrl())
			.status("Scheduled for Offline")
			.lastUpdateDateTime(alert.getLastUpdatedDate())
			.build();
	}

	public static ProductPriceAlertDto fromEntity(ProductPriceMonitorProductCheckDo check) {
		ProductPriceMonitorProductDo priceMonitorProduct = check.getProductPriceMonitorProduct();
		return ProductPriceAlertDto.builder()
			.merchantId(priceMonitorProduct.getMerchantId())
			.skuCode(priceMonitorProduct.getSkuCode())
			.skuNameCh(priceMonitorProduct.getSkuName())
			.productCode(priceMonitorProduct.getTargetProductCode())
			.storefrontStoreCode(priceMonitorProduct.getStorefrontStoreCode())
			.hktvOriginalPrice(check.getSourceOriginalPrice())
			.hktvSellingPrice(check.getSourceSellingPrice())
			.tmallOriginalPrice(check.getTargetOriginalPrice())
			.tmallSellingPrice(check.getTargetSellingPrice())
			.tmallUrl(check.getTargetUrl())
			.brandName(check.getBrand() != null ? check.getBrand().getBrandNameTc() : null)
			.rmCode(check.getRmCode())
			.status("Scheduled for Offline")
			.lastUpdateDateTime(check.getLastUpdatedDate())
			.productPriceMonitorProductId(check.getProductPriceMonitorProductId())
			.build();
	}

	public ProductPriceAlertDto setRmInfos(List<RmTeamUserInfo> rmTeamUserInfos) {
		if (CollectionUtils.isEmpty(rmTeamUserInfos)) {
			return this;
		}
		// combine RM/RML names to a single string, separated by |
		this.rmName = rmTeamUserInfos.stream()
			.map(RmTeamUserInfo::getRmName)
			.reduce((first, second) -> first + " | " + second)
			.orElse("");
		this.rmlName = rmTeamUserInfos.stream()
			.map(RmTeamUserInfo::getRmlName)
			.reduce((first, second) -> first + " | " + second)
			.orElse("");
		return this;
	}
}
