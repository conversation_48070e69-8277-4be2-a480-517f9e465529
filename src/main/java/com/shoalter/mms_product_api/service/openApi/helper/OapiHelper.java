package com.shoalter.mms_product_api.service.openApi.helper;


import com.shoalter.mms_product_api.config.product.OapiStatusCodeEnum;
import com.shoalter.mms_product_api.config.product.SysParmSegment;
import com.shoalter.mms_product_api.config.type.ProductReadyMethodType;
import com.shoalter.mms_product_api.dao.repository.brand.BrandRepository;
import com.shoalter.mms_product_api.dao.repository.store.StoreRepository;
import com.shoalter.mms_product_api.dao.repository.store.StoreWarehouseRepository;
import com.shoalter.mms_product_api.dao.repository.store.pojo.StoreContractMerchantDo;
import com.shoalter.mms_product_api.dao.repository.store.pojo.StoreWarehouseDo;
import com.shoalter.mms_product_api.dao.repository.system.SysParmRepository;
import com.shoalter.mms_product_api.dao.repository.system.pojo.SysParmDo;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.service.openApi.pojo.OapiBatchEditMainResponseData;
import com.shoalter.mms_product_api.service.openApi.pojo.OapiResponseDto;
import com.shoalter.mms_product_api.service.product.FindDeliveryMethodService;
import com.shoalter.mms_product_api.service.product.IsWhiteListMerchantService;
import com.shoalter.mms_product_api.util.CollectionUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;


@RequiredArgsConstructor
@Service
@Slf4j
public class OapiHelper {
	private final SysParmRepository sysParmRepository;
	private final StoreWarehouseRepository storeWarehouseRepository;
	private final BrandRepository brandRepository;
	private final StoreRepository storeRepository;
	private final IsWhiteListMerchantService isWhiteListMerchantService;
	private final FindDeliveryMethodService findDeliveryMethodService;
	private final MessageSource messageSource;

	public static final String OPEN_API_SOURCE_IDENTIFIER_PREFIX = "OpenAPI-";

	public ResponseDto<StoreContractMerchantDo> validateStore(String storefrontStoreCode, String platformCode, String businessType) {
		// query store, contract, merchant
		List<StoreContractMerchantDo> storeContractMerchantDoList = storeRepository.findStoreContractMerchantByStorefrontStoreCode(businessType, platformCode, storefrontStoreCode);
		StoreContractMerchantDo storeContractMerchantDo;
		if (storeContractMerchantDoList.isEmpty()) {
			return ResponseDto.fail(List.of(messageSource.getMessage("message184", new String[]{storefrontStoreCode}, null)));
		} else {
			storeContractMerchantDo = storeContractMerchantDoList.get(0);
		}

		// check white list
		if (!isWhiteListMerchant(storeContractMerchantDo.getMerchantId())) {
			return ResponseDto.fail(List.of(messageSource.getMessage("message288", null, null)));
		}

		return ResponseDto.success(storeContractMerchantDo);
	}

	public boolean isWhiteListMerchant(Integer merchantId) {
		return isWhiteListMerchantService.start(merchantId).getData();
	}

	public String findDeliveryMethod(String buCode, String productReadyMethod) {
		if (StringUtils.isBlank(productReadyMethod)) {
			return null; // return null, 後面checking rule會回error message
		}
		SysParmDo deliveryMethod = findDeliveryMethodService.start(buCode, productReadyMethod).getData();
		return deliveryMethod == null ? null : deliveryMethod.getCode(); // return null, 後面checking rule會回error message
	}

	public Integer findFirstWarehouseId(Integer storeId, String productReadyMethodCode) {
		List<SysParmDo> sysParmDoList = sysParmRepository.findBySegmentAndCode(SysParmSegment.PRODUCT_READY_METHOD_WAREHOUSE, productReadyMethodCode);
		if (CollectionUtil.isEmpty(sysParmDoList)) {
			return null; // return null, 後面checking rule會回error message
		}
		SysParmDo sysParmDo = ProductReadyMethodType.CONSIGNMENT.equalsIgnoreCase(productReadyMethodCode) ? sysParmDoList.get(1) : sysParmDoList.get(0);
		String seqNoString = sysParmDo.getLongDesc();
		List<Integer> seqNoList = Arrays.stream(seqNoString.split(","))
			.filter(StringUtils::isNotEmpty)
			.map(Integer::parseInt)
			.collect(Collectors.toList());

		List<StoreWarehouseDo> storeWarehouseDoList = storeWarehouseRepository.findStoreWarehouseByStoreIdAndSeqNoInOrderBySeqNo(storeId, seqNoList);
		if (storeWarehouseDoList.isEmpty()) {
			return null; // return null, 後面checking rule會回error message
		}

		storeWarehouseDoList.sort(Comparator.comparing(StoreWarehouseDo::getSeqNo));
		return storeWarehouseDoList.get(0).getId();
	}

	public Integer findBrandId(String brandCode) {
		List<Integer> brandIds = brandRepository.findIdByBrandCode(brandCode);
		if (brandIds.isEmpty()) {
			return null; // return null, 後面checking rule會回error message
		} else {
			return brandIds.get(0);
		}
	}

	public OapiResponseDto<OapiBatchEditMainResponseData> generateBatchEditResponseData(OapiStatusCodeEnum statusEnum, Long recordId, List<String> message) {
		return OapiResponseDto.<OapiBatchEditMainResponseData>builder()
			.message(OapiStatusCodeEnum.SUCCESS == statusEnum ? "Open API batch edit successful" : "Open API batch edit failed")
			.code(statusEnum.getCode())
			.data(OapiBatchEditMainResponseData.builder()
				.recordId(recordId)
				.status(statusEnum.getCode())
				.message(message)
				.build())
			.build();
	}
}
