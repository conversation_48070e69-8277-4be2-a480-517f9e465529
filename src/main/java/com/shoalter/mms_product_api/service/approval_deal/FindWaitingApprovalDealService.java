package com.shoalter.mms_product_api.service.approval_deal;

import com.shoalter.mms_product_api.dao.repository.approval_deal.CommissionApprovalDealRepository;
import com.shoalter.mms_product_api.dao.repository.business.BuProductCategoryRepository;
import com.shoalter.mms_product_api.dao.repository.business.pojo.BuProductCategoryDo;
import com.shoalter.mms_product_api.service.approval_deal.enums.ApprovalDealStatusEnum;
import com.shoalter.mms_product_api.service.approval_deal.pojo.CommissionApprovalDealDto;
import com.shoalter.mms_product_api.service.approval_deal.pojo.WaitingApprovalDealMainRequestData;
import com.shoalter.mms_product_api.service.approval_deal.pojo.WaitingApprovalResponseData;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.util.CollectionUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

@RequiredArgsConstructor
@Service
@Slf4j
public class FindWaitingApprovalDealService {

	private final CommissionApprovalDealRepository commissionApprovalDealRepository;
	private final BuProductCategoryRepository buProductCategoryRepository;

	public ResponseDto<?> start(WaitingApprovalDealMainRequestData requestData) {

		List<CommissionApprovalDealDto> commissionApprovalDealDtoList = commissionApprovalDealRepository.findCommissionApprovalDealDto(
			requestData.getBu(),
			requestData.getStorefrontStoreCode(),
			List.of(ApprovalDealStatusEnum.MERCHANT_SUBMITTED.name(), ApprovalDealStatusEnum.RM_REVIEWED.name(), ApprovalDealStatusEnum.REJECTED.name()),
			requestData.getSkuCode());
		if (commissionApprovalDealDtoList.isEmpty()) {
			return ResponseDto.success(null);
		}

		WaitingApprovalResponseData waitingApproval = new WaitingApprovalResponseData();
		CommissionApprovalDealDto commissionApprovalDealDto = commissionApprovalDealDtoList.get(0);
		BeanUtils.copyProperties(commissionApprovalDealDto, waitingApproval);

		Optional<BuProductCategoryDo> primaryCategoryCodeInfo = buProductCategoryRepository.findByProductCatCode(requestData.getBu(), commissionApprovalDealDto.getPrimaryCategoryCode());
		waitingApproval.setPrimaryCategoryCodeInfo(primaryCategoryCodeInfo.orElse(null));

		String productTypeCode = commissionApprovalDealDto.getProductTypeCode();
		if (StringUtils.isNotBlank(productTypeCode)) {
			List<BuProductCategoryDo> productTypeCodeInfo = buProductCategoryRepository.findByProductCatCodeList(requestData.getBu(), Arrays.asList(productTypeCode.split(",")));
			if (CollectionUtil.isNotEmpty(productTypeCodeInfo)) {
				waitingApproval.setProductTypeCodeInfo(productTypeCodeInfo);
			}
		}

		return ResponseDto.success(waitingApproval);
	}
}
