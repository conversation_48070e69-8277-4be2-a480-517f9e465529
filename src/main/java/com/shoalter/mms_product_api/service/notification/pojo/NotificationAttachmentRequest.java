package com.shoalter.mms_product_api.service.notification.pojo;

import com.shoalter.mms_product_api.service.notification.pojo.dto.MessageContentDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.core.io.FileSystemResource;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class NotificationAttachmentRequest {

	private MessageContentDto messageContentDto;
	private FileSystemResource attachment;

}
