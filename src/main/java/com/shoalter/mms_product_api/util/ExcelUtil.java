package com.shoalter.mms_product_api.util;

import com.shoalter.mms_product_api.config.product.BatchEditOverseaDeliveryActionEnum;
import com.shoalter.mms_product_api.config.product.ExpiryTypeEnum;
import com.shoalter.mms_product_api.config.product.ForceOfflineStatusEnum;
import com.shoalter.mms_product_api.config.product.OnlineStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.*;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;


@Slf4j
public class ExcelUtil {

    private ExcelUtil(){}

    static {
        YES_NO_LIST = List.of("Y : Yes", "N : No");
        YN_LIST = List.of("Y", "N");
        WEIGHT_UNIT_LIST = List.of("kg", "g");
        PACK_DIMENSION_UNIT_LIST = List.of("m", "cm", "mm");
        EXPIRY_TYPE_LIST = List.of(ExpiryTypeEnum.FIXED.name(), ExpiryTypeEnum.RELATIVELY.name());
        RETURN_DAYS_LIST = List.of("0", "7", "31");
        GOODS_TYPE_LIST = List.of("parallel goods", "authorized goods");
        WARRANTY_PERIOD_UNIT_LIST = List.of("Years", "Months", "Days");
        PRODUCT_STATUS_LIST = Stream.of(OnlineStatusEnum.values()).map(Enum::name).collect(Collectors.toUnmodifiableList());
        STYLE_LIST = List.of("RED", "GREY", "BLACK", "BLUE");
        VISIBLE_LIST = List.of("Y : Yes", "N : No");
        OLD_YES_NO_LIST = List.of("Yes", "No");
        OVERSEA_DELIVERY_ACTION_LIST = Stream.of(BatchEditOverseaDeliveryActionEnum.values()).map(Enum::name).collect(Collectors.toUnmodifiableList());
		SKU_STATUS_FORCE_OFFLINE_LIST = Stream.of(ForceOfflineStatusEnum.values()).map(ForceOfflineStatusEnum::getStatus).collect(Collectors.toUnmodifiableList());
    }

    /**
     * Excel Lov Sheet Value defining here
     */
    public static final List<String> YES_NO_LIST;
    public static final List<String> YN_LIST;

    public static final List<String> WEIGHT_UNIT_LIST;
    public static final List<String> PACK_DIMENSION_UNIT_LIST;
    public static final List<String> EXPIRY_TYPE_LIST ;
    public static final List<String> RETURN_DAYS_LIST ;
    public static final List<String> GOODS_TYPE_LIST ;
    public static final List<String> WARRANTY_PERIOD_UNIT_LIST ;
    public static final List<String> PRODUCT_STATUS_LIST ;
    public static final List<String> STYLE_LIST ;
    public static final List<String> VISIBLE_LIST ;
    public static final List<String> OLD_YES_NO_LIST;
    public static final List<String> OVERSEA_DELIVERY_ACTION_LIST;
	public static final List<String> SKU_STATUS_FORCE_OFFLINE_LIST;

    public static CellStyle createTableHeaderStyle(Workbook wb, HorizontalAlignment alignment,
                                                   boolean setFillForegroundColor, boolean bold) {
        CellStyle body = wb.createCellStyle();
        Font bodyFont = wb.createFont();
        bodyFont.setFontName("SansSerif");
        bodyFont.setFontHeightInPoints((short) 10);
        body.setAlignment(alignment); // 文字對齊
        bodyFont.setBold(bold);// 粗体显示

        if (setFillForegroundColor) {
            bodyFont.setColor(HSSFColor.HSSFColorPredefined.WHITE.getIndex());
            body.setFillForegroundColor(HSSFColor.HSSFColorPredefined.RED.getIndex());
            body.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        }

        body.setFont(bodyFont);// 选择需要用到的字体格式
        body.setWrapText(true);

        return body;
    }

	public static CellStyle createTableHeaderStyle(Workbook wb, HorizontalAlignment horizontalAlignment,VerticalAlignment verticalAlignment,
												   String fontName,HSSFColor.HSSFColorPredefined fontColor,boolean isBold,boolean isLocked) {
		CellStyle cellStyle = wb.createCellStyle();
		Font font = wb.createFont();
		font.setFontName(fontName);
		font.setFontHeightInPoints((short) 10);
		font.setBold(isBold);
		font.setColor(fontColor.getIndex());

		cellStyle.setFont(font);
		cellStyle.setAlignment(horizontalAlignment);
		cellStyle.setVerticalAlignment(verticalAlignment);
		cellStyle.setWrapText(true);
		cellStyle.setLocked(isLocked);
		return cellStyle;
	}

    public static CellStyle createBodyStyle(Workbook wb, HorizontalAlignment alignment,
                                            boolean color, boolean fullBorder, boolean bold, boolean doubleLine, boolean isLocked) {
        CellStyle body = wb.createCellStyle();
        Font bodyFont = wb.createFont();
        bodyFont.setFontName("SansSerif");
        bodyFont.setFontHeightInPoints((short) 10);
        body.setWrapText(true);
        body.setFont(bodyFont);// 选择需要用到的字体格式
	    body.setAlignment(alignment);// 文字對齊
		body.setLocked(isLocked);
        bodyFont.setBold(bold);// 粗体显示


        if (color) {
            bodyFont.setColor(HSSFColor.HSSFColorPredefined.LIGHT_ORANGE.getIndex());
        }


        if (fullBorder) {
            body.setBorderBottom(BorderStyle.THIN); // 下边框
            body.setBorderLeft(BorderStyle.THIN);// 左边框
            body.setBorderTop(BorderStyle.THIN);// 上边框
            body.setBorderRight(BorderStyle.THIN);// 右边框
        }

        if(doubleLine){
            body.setBorderTop(BorderStyle.THIN);// 上边框
            body.setBorderBottom(BorderStyle.DOUBLE); // 下边框
        }

        return body;
    }

	public static CellStyle createDateFormatStyle(String formatStyle, Workbook wb, HorizontalAlignment alignment,
												  boolean color, boolean fullBorder, boolean bold, boolean doubleLine, boolean isLocked) {
		CellStyle body = wb.createCellStyle();
		Font bodyFont = wb.createFont();
		bodyFont.setFontName("SansSerif");
		bodyFont.setFontHeightInPoints((short) 10);
		body.setWrapText(true);
		body.setFont(bodyFont);// 选择需要用到的字体格式
		body.setAlignment(alignment);// 文字對齊
		body.setLocked(isLocked);
		bodyFont.setBold(bold);// 粗体显示


		if (color) {
			bodyFont.setColor(HSSFColor.HSSFColorPredefined.LIGHT_ORANGE.getIndex());
		}


		if (fullBorder) {
			body.setBorderBottom(BorderStyle.THIN); // 下边框
			body.setBorderLeft(BorderStyle.THIN);// 左边框
			body.setBorderTop(BorderStyle.THIN);// 上边框
			body.setBorderRight(BorderStyle.THIN);// 右边框
		}

		if(doubleLine){
			body.setBorderTop(BorderStyle.THIN);// 上边框
			body.setBorderBottom(BorderStyle.DOUBLE); // 下边框
		}

		DataFormat dateFormat = wb.createDataFormat();
		body.setDataFormat(dateFormat.getFormat(formatStyle));

		return body;
	}

    public static Font getRedFont(Workbook workbook){
        Font redFont = workbook.createFont();
        redFont.setColor(IndexedColors.RED.getIndex());
        redFont.setFontName("SansSerif");
        redFont.setFontHeightInPoints((short) 10);
        redFont.setBold(true);
        return redFont;
    }

}
