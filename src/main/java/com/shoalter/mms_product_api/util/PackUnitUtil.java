package com.shoalter.mms_product_api.util;

import com.shoalter.mms_product_api.config.product.PackDimensionUnitEnum;
import com.shoalter.mms_product_api.config.product.WeightUnitEnum;
import lombok.NonNull;

import java.math.BigDecimal;

public class PackUnitUtil {
    private PackUnitUtil(){}

    public static BigDecimal convertPackDimension(@NonNull PackDimensionUnitEnum beforeUnit, @NonNull PackDimensionUnitEnum afterUnit, @NonNull BigDecimal packDimension){
        return packDimension.multiply(new BigDecimal(beforeUnit.getProportion() / afterUnit.getProportion()));
    }

    public static BigDecimal convertWeight(@NonNull WeightUnitEnum beforeUnit, @NonNull WeightUnitEnum afterUnit, @NonNull BigDecimal packDimension){
        return packDimension.multiply(new BigDecimal(beforeUnit.getProportion() / afterUnit.getProportion()));
    }
}
