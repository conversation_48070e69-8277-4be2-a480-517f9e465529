package com.shoalter.mms_product_api.dao.repository.product.pojo;

import lombok.Data;

import javax.persistence.*;
import java.util.Date;
@Entity
@Table(name = "PRODUCT_VIDEO")
@Data
public class ProductVideoDo {
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Id
    @Column(name = "ID", columnDefinition="INT")
    private Integer id;
    @Column(name = "PRODUCT_ID")
    private Integer productId;
    @Column(name = "PRODUCT_CODE")
    private String productCode;
    @Column(name = "FILE_NAME")
    private String fileName;
    @Column(name = "STATUS")
    private String status;
    @Column(name = "FILE_PATH")
    private String filePath;
    @Column(name = "CREATED_BY")
    private String createdBy;
    @Column(name = "CREATED_DATE")
    private Date createdDate;
    @Column(name = "LAST_UPDATED_BY")
    private String lastUpdatedBy;
    @Column(name = "LAST_UPDATED_DATE")
    private Date lastUpdatedDate;
    @Column(name = "FILE_TYPE")
    private String fileType;
    @Column(name = "HREF")
    private String href;
}
