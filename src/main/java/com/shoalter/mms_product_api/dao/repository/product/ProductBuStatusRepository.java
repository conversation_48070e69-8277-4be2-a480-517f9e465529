package com.shoalter.mms_product_api.dao.repository.product;

import com.shoalter.mms_product_api.dao.repository.product.pojo.ProductBuStatusDo;
import com.shoalter.mms_product_api.dao.repository.product.pojo.ProductStoreStatusDo;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

public interface ProductBuStatusRepository extends JpaRepository<ProductBuStatusDo, Integer> {

    List<ProductBuStatusDo> findByProductId(Integer productId);

    void deleteByProductId(Integer id);
}
