package com.shoalter.mms_product_api.dao.repository.product;

import com.shoalter.mms_product_api.dao.repository.product.pojo.ProductPriceAlertDo;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

public interface ProductPriceAlertRepository extends JpaRepository<ProductPriceAlertDo, Long> {
	List<ProductPriceAlertDo> findByStatus(Integer status);

	List<ProductPriceAlertDo> findByTargetPlatformAndStatus(String targetPlatform, Integer status);

	List<ProductPriceAlertDo> findByJobTraceUuidAndStatus(String jobTraceUuid, Integer status);

	@EntityGraph(attributePaths = {"store"})
	List<ProductPriceAlertDo> findByJobTraceUuidAndStatusIn(String cronJobUuid, List<Integer> statusCode);

	boolean existsByStoreSkuIdAndBusUnitIdAndSourcePlatformAndTargetPlatformAndStatus(
		String storeSkuId,
		Integer busUnitId,
		String sourcePlatform,
		String targetPlatform,
		Integer status
	);
}
