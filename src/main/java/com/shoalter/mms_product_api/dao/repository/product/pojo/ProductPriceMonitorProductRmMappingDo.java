package com.shoalter.mms_product_api.dao.repository.product.pojo;

import lombok.*;

import javax.persistence.*;
import java.time.LocalDateTime;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Table(name = "PRODUCT_PRICE_MONITOR_PRODUCT_RM_MAPPING")
public class ProductPriceMonitorProductRmMappingDo {
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "ID")
	private Long id;
	@Column(name = "PRODUCT_PRICE_MONITOR_PRODUCT_ID", nullable = false)
	private Integer productPriceMonitorProductId;
	@Column(name = "RM_CODE", nullable = false)
	private String rmCode;
	@Column(name = "CREATED_DATE", nullable = false)
	private LocalDateTime createdDate;
	@Column(name = "CREATED_BY", nullable = false)
	private String createdBy;
	@Column(name = "LAST_UPDATED_DATE", nullable = false)
	private LocalDateTime lastUpdatedDate;
	@Column(name = "LAST_UPDATED_BY", nullable = false)
	private String lastUpdatedBy;
}
