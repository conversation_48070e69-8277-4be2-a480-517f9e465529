package com.shoalter.mms_product_api.dao.repository.api.pojo;

import com.shoalter.mms_product_api.config.product.SaveProductProtocol;
import com.shoalter.mms_product_api.config.product.SaveProductSource;
import lombok.Data;

import javax.persistence.*;
import java.util.Date;

@Entity
@Table(name = "SAVE_PRODUCT_RECORD")
@Data
public class SaveProductRecordDo {
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Id
    @Column(name = "ID")
    private Long id;
    @Column(name = "MERCHANT_ID")
    private Integer merchantId;
    @Column(name = "PROTOCOL")
    private String protocol = SaveProductProtocol.HTTP;
    @Column(name = "UPLOAD_TYPE")
    private Integer uploadType;
    @Column(name = "FILE_NAME")
    private String fileName;
    @Column(name = "STATUS")
    private Integer status;
    @Column(name = "UPLOAD_USER_ID")
    private Integer uploadUserId;
    @Column(name = "UPLOAD_TIME")
    private Date uploadTime;
    @Column(name = "CHECK_ID")
    private String checkId;
    @Column(name = "CHECK_TIME")
    private Date checkTime;
    @Column(name = "PM_TRACT_ID")
    private String pmTractId;
    @Column(name = "INVENTORY_TRACT_ID")
    private String inventoryTractId;
    @Column(name = "GROUP_ID")
    private Long groupId;
	@Column(name = "USER_IP")
	private String userIp;
	@Column(name = "SOURCE")
	private String source = SaveProductSource.MMS;
	@Column(name = "SOURCE_IDENTIFIER")
	private String sourceIdentifier;
}
