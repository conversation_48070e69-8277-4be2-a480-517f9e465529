package com.shoalter.mms_product_api.dao.repository.merchant;

import com.shoalter.mms_product_api.dao.repository.merchant.pojo.UserStoreDo;
import com.shoalter.mms_product_api.dao.repository.merchant.pojo.UserStoreRoleDo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface UserStoreRepository extends JpaRepository<UserStoreDo, Integer> {
	@Query(value = "SELECT US.STORE_ID " +
			"FROM USER_STORE US " +
			"WHERE US.USER_ID = :userId", nativeQuery = true)
	List<Integer> findStoreIdByUserId(Integer userId);

	@Query(value = "SELECT S.Store_code " +
		"FROM USER_STORE US " +
		"JOIN STORE S ON S.Id = US.STORE_ID " +
		"WHERE US.USER_ID = :userId", nativeQuery = true)
	List<String> findStoreCodeByUserId(Integer userId);

	@Query(value = "SELECT " +
		"US.USER_ID AS userId, " +
		"SR.ROLE_CODE AS roleCode, " +
		"SR.ROLE_TYPE AS roleType, " +
		"US.STORE_ID AS storeId, " +
		"S.STOREFRONT_STORE_CODE AS storefrontStoreCode, " +
		"SU.USER_NAME AS userName, " +
		"SU.EMAIL AS email " +
		"FROM USER_STORE US " +
		"JOIN STORE S ON US.STORE_ID = S.ID " +
		"JOIN SYS_USER_ROLE SUR ON US.USER_ID = SUR.USER_ID " +
		"JOIN SYS_ROLE SR ON SUR.ROLE_ID = SR.ID " +
		"JOIN SYS_USER SU ON US.USER_ID = SU.ID " +
		"WHERE US.STORE_ID IN (:storeIds) " +
		"AND SR.ROLE_CODE IN (:roleCodes)", nativeQuery = true)
	List<UserStoreRoleDo> findUserStoreRolesByStoreIdsAndRoleCodes(
		@Param("storeIds") List<Integer> storeIds,
		@Param("roleCodes") List<String> roleCodes
	);
}
