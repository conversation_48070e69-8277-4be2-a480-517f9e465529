package com.shoalter.mms_product_api.dao.repository.product.pojo;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import javax.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Entity representing the PRODUCT_PRICE_MONITOR_PRODUCT_CHECK_HISTORY table
 * Used to store historical records of product price monitoring checks
 */
@Entity
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Table(name = "PRODUCT_PRICE_MONITOR_PRODUCT_CHECK_HISTORY")
public class ProductPriceMonitorProductCheckHistoryDo {

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "ID")
	private Long id;

	@Column(name = "PRODUCT_PRICE_MONITOR_PRODUCT_CHECK_ID")
	private Integer productPriceMonitorProductCheckId;

	@Column(name = "PRODUCT_PRICE_MONITOR_PRODUCT_ID")
	private Integer productPriceMonitorProductId;

	@Column(name = "ACTIVE_IND")
	private Integer activeInd;

	@Column(name = "PRICE_STATUS")
	private Integer priceStatus;

	@Column(name = "BUS_UNIT_ID")
	private Integer busUnitId;

	@Column(name = "MERCHANT_ID")
	private Integer merchantId;

	@Column(name = "STORE_ID")
	private Integer storeId;

	@Column(name = "STOREFRONT_STORE_CODE")
	private String storefrontStoreCode;

	@Column(name = "STORE_SKU_ID")
	private String storeSkuId;

	@Column(name = "SKU_CODE")
	private String skuCode;

	@Column(name = "SKU_NAME")
	private String skuName;

	@Column(name = "SOURCE_PLATFORM")
	private String sourcePlatform;

	@Column(name = "SOURCE_CURRENCY_CODE")
	private String sourceCurrencyCode;

	@Column(name = "TARGET_PLATFORM")
	private String targetPlatform;

	@Column(name = "TARGET_PRODUCT_CODE")
	private String targetProductCode;

	@Column(name = "TARGET_SKU_CODE")
	private String targetSkuCode;

	@Column(name = "TARGET_CURRENCY_CODE")
	private String targetCurrencyCode;

	@Column(name = "SAME_PRICE_FLAG")
	private Integer samePriceFlag;

	@Column(name = "STATUS")
	private Integer status;

	@Column(name = "JOB_TRACE_UUID")
	private String jobTraceUuid;

	@Column(name = "RECORD_ID")
	private Long recordId;

	@Column(name = "BRAND_ID")
	private Integer brandId;

	@Column(name = "SOURCE_ORIGINAL_PRICE")
	private BigDecimal sourceOriginalPrice;

	@Column(name = "SOURCE_SELLING_PRICE")
	private BigDecimal sourceSellingPrice;

	@Column(name = "TARGET_ORIGINAL_PRICE")
	private BigDecimal targetOriginalPrice;

	@Column(name = "TARGET_SELLING_PRICE")
	private BigDecimal targetSellingPrice;

	@Column(name = "TARGET_URL")
	private String targetUrl;

	@Column(name = "TARGET_PRICE_UPDATED_DATE")
	private LocalDateTime targetPriceUpdatedDate;

	@Column(name = "ERROR_REASON")
	private String errorReason;

	@Column(name = "ERROR_CODE")
	private String errorCode;

	@Column(name = "CREATED_DATE")
	private LocalDateTime createdDate;

	@Column(name = "CREATED_BY")
	private String createdBy;

	@Column(name = "LAST_UPDATED_DATE")
	private LocalDateTime lastUpdatedDate;

	@Column(name = "LAST_UPDATED_BY")
	private String lastUpdatedBy;

	@Column(name = "HISTORY_CREATED_DATE")
	private LocalDateTime historyCreatedDate;
}
