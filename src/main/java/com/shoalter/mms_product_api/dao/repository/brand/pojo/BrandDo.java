package com.shoalter.mms_product_api.dao.repository.brand.pojo;

import java.util.Date;
import lombok.Data;
import javax.persistence.*;

@Entity
@Table(name = "BRAND")
@Data
public class BrandDo {
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Id
    @Column(name = "ID")
    private Integer id;
    @Column(name = "BRAND_CODE")
    private String brandCode;
    @Column(name = "BRAND_NAME_EN")
    private String brandNameEn;
    @Column(name = "BRAND_NAME_TC")
    private String brandNameTc;
    @Column(name = "BRAND_NAME_SC")
    private String brandNameSc;
    @Column(name = "BRAND_DESC_EN")
    private String brandDescEn;
    @Column(name = "BRAND_DESC_TC")
    private String brandDescTc;
    @Column(name = "BRAND_DESC_SC")
    private String brandDescSc;
    @Column(name = "IMAGE_FILE_NAME")
    private String imageFileName;
    @Column(name = "CREATED_BY")
    private String createdBy;
    @Column(name = "CREATED_DATE")
    private Date createdDate;
    @Column(name = "LAST_UPDATED_BY")
    private String lastUpdatedBy;
    @Column(name = "LAST_UPDATED_DATE")
    private Date lastUpdatedDate;
    @Column(name = "REASON_CODE")
    private String reasonCode;
    @Column(name = "REASON_DESC")
    private String reasonDesc;
    @Column(name = "STATUS")
    private String status;
    @Column(name = "BUS_UNIT_ID")
    private Integer busUnitId;
}
