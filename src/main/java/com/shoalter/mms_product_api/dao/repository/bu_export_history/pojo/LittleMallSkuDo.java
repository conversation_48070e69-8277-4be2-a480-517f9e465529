package com.shoalter.mms_product_api.dao.repository.bu_export_history.pojo;

import com.shoalter.mms_product_api.service.product.pojo.SingleEditProductDto;
import com.shoalter.mms_product_api.service.product.pojo.littlemall.LittleMallExportDto;
import com.vladmihalcea.hibernate.type.json.JsonType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.TypeDef;

import javax.persistence.*;
import java.time.LocalDateTime;

@Entity
@Table(name = "LITTLE_MALL_SKU")
@Data
@TypeDef(name = "json", typeClass = JsonType.class)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LittleMallSkuDo {
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Id
	@Column(name = "ID")
	private Integer id;

	@Column(name = "LITTLE_MALL_EXPORT_INFO_ID")
	private Integer littleMallExportInfoId;

	@Type(type = "json")
	@Column(name = "SKU_DATA")
	private LittleMallExportDto skuData;

	@CreationTimestamp
	@Column(name = "CREATED_DATE")
	private LocalDateTime createdDate;
}
