package com.shoalter.mms_product_api.dao.repository.product.pojo;

import java.util.Date;
import java.math.BigDecimal;
import lombok.Data;
import javax.persistence.*;
@Entity
@Table(name = "PRODUCT_STORE_STATUS")
@Data
public class ProductStoreStatusDo {
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Id
    @Column(name = "ID")
    private Integer id;
    @Column(name = "PRODUCT_ID")
    private Integer productId;
    @Column(name = "STORE_ID")
    private Integer storeId;
    @Column(name = "STORE_SKU_ID")
    private String storeSkuId;
    @Column(name = "ONLINE_STATUS")
    private String onlineStatus;
    @Column(name = "STATUS")
    private String status;
    @Column(name = "LAST_SYNCHRONIZE_DATE")
    private Date lastSynchronizeDate;
    @Column(name = "CREATED_BY")
    private String createdBy;
    @Column(name = "CREATED_DATE")
    private Date createdDate;
    @Column(name = "LAST_UPDATED_BY")
    private String lastUpdatedBy;
    @Column(name = "LAST_UPDATED_DATE")
    private Date lastUpdatedDate;
    @Column(name = "COMMISSION_RATE")
    private BigDecimal commissionRate;
    @Column(name = "CONTRACT_PROD_TERMS_ID")
    private Integer contractProdTermsId;
    @Column(name = "STORE_WAREHOUSE_ID")
    private Integer storeWarehouseId;
    @Column(name = "SHARE_STOCK_UUID")
    private String shareStockUuid;
}