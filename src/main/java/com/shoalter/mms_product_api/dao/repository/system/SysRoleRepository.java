package com.shoalter.mms_product_api.dao.repository.system;

import com.shoalter.mms_product_api.dao.repository.system.pojo.SysRoleDo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

public interface SysRoleRepository extends JpaRepository<SysRoleDo, Integer> {
    @Query(value = "SELECT * " +
            "FROM SYS_ROLE SR " +
            "INNER JOIN SYS_USER_ROLE SUR on SR.ID = SUR.ROLE_ID " +
            "INNER JOIN SYS_USER SU on SUR.USER_ID = SU.ID " +
            "WHERE SU.ID = :userId", nativeQuery = true)
    SysRoleDo findByUserId(Integer userId);
}
