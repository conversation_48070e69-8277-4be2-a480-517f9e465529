package com.shoalter.mms_product_api.dao.repository.product;

import com.shoalter.mms_product_api.dao.repository.product.pojo.ProductPriceMonitorProductDo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.Set;

@Repository
public interface ProductPriceMonitorProductRepository extends JpaRepository<ProductPriceMonitorProductDo, Integer> {
	/**
	 * find by unique key
	 */
	Optional<ProductPriceMonitorProductDo> findByStoreSkuIdAndBusUnitIdAndTargetPlatform(String storeSkuId, Integer busUnitId, String targetPlatform);


	/**
	 * check exists by unique key
	 */
	boolean existsByStoreSkuIdAndBusUnitIdAndTargetPlatform(String storeSkuId, Integer busUnitId, String targetPlatform);

	/**
	 * find by unique key and activeInd
	 */
	Optional<ProductPriceMonitorProductDo> findByStoreSkuIdAndBusUnitIdAndTargetPlatformAndActiveInd(String storeSkuId, Integer busUnitId, String targetPlatform, Integer activeInd);

	List<ProductPriceMonitorProductDo> findByActiveIndAndTargetPlatformAndBusUnitId(Integer activeInd, String targetPlatform, Integer busUnitId);

	List<ProductPriceMonitorProductDo> findByActiveIndAndPriceStatusAndTargetPlatformAndBusUnitId(Integer activeInd, Integer priceStatus, String targetPlatform, Integer busUnitId);

	List<ProductPriceMonitorProductDo> findByActiveIndAndPriceStatusInAndTargetPlatformAndBusUnitId(Integer activeInd, Set<Integer> priceStatus, String targetPlatform, Integer busUnitId);

	@Query(value = "SELECT p.* " +
		"FROM PRODUCT_PRICE_MONITOR_PRODUCT p " +
		"WHERE p.ACTIVE_IND = :activeInd " +
		"AND p.TARGET_PLATFORM = :targetPlatform " +
		"AND p.BUS_UNIT_ID = :busUnitId " +
		"AND (p.PRICE_STATUS IN :priceStatus OR p.TARGET_PRICE_UPDATED_DATE IS NULL OR (p.TARGET_PRICE_UPDATED_DATE <= NOW() - INTERVAL :daysBefore DAY))",
		nativeQuery = true)
	List<ProductPriceMonitorProductDo> findProductsMonitorForPriceUpdate(
		@Param("activeInd") Integer activeInd,
		@Param("targetPlatform") String targetPlatform,
		@Param("busUnitId") Integer busUnitId,
		@Param("daysBefore") Integer daysBefore,
		@Param("priceStatus") Set<Integer> priceStatus
	);
}
