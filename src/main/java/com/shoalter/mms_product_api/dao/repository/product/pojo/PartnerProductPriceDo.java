package com.shoalter.mms_product_api.dao.repository.product.pojo;

import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Entity
@Table(name = "PARTNER_PRODUCT_PRICE")
@Data
public class PartnerProductPriceDo {
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "ID")
	private Long id;
	@Column(name = "BUS_UNIT_ID")
	private Integer busUnitId;
	@Column(name = "SOURCE")
	private String source;
	@Column(name = "STATUS")
	private Integer status;
	@Column(name = "EXCHANGE_RATE")
	private BigDecimal exchangeRate;
	@Column(name = "STOREFRONT_STORE_CODE")
	private String storefrontStoreCode;
	@Column(name = "PRODUCT_CODE")
	private String productCode;
	@Column(name = "SKU_CODE")
	private String skuCode;
	@Column(name = "STORE_SKU_ID")
	private String storeSkuId;
	@Column(name = "CHARGE_CURRENCY")
	private String chargeCurrency;
	@Column(name = "CHARGE_PRICE")
	private BigDecimal chargePrice;
	@Column(name = "CHARGE_CONVERTED_CURRENCY")
	private String chargeConvertedCurrency;
	@Column(name = "CHARGE_CONVERTED")
	private BigDecimal chargeConverted;
	@Column(name = "SERVICE_FEE_CURRENCY")
	private String serviceFeeCurrency;
	@Column(name = "SERVICE_FEE")
	private BigDecimal serviceFee;
	@Column(name = "SERVICE_FEE_CONVERTED_CURRENCY")
	private String serviceFeeConvertedCurrency;
	@Column(name = "SERVICE_FEE_CONVERTED")
	private BigDecimal serviceFeeConverted;
	@Column(name = "CREATE_DATE")
	@CreationTimestamp
	private LocalDateTime createDate;
	@Column(name = "LAST_UPDATE_DATE")
	private LocalDateTime lastUpdateDate;
}
