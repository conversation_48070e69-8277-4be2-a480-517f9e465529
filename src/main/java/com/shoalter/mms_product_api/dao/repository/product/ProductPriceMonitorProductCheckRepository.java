package com.shoalter.mms_product_api.dao.repository.product;

import com.shoalter.mms_product_api.dao.repository.product.pojo.ProductPriceMonitorProductCheckDo;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.Set;

/**
 * Repository for ProductPriceMonitorProductCheckDo entity.
 * Provides methods for CRUD operations and custom queries.
 */
@Repository
public interface ProductPriceMonitorProductCheckRepository extends JpaRepository<ProductPriceMonitorProductCheckDo, Long> {

	/**
	 * Find by product price monitor product ID, using the unique index
	 */
	Optional<ProductPriceMonitorProductCheckDo> findByProductPriceMonitorProductId(Integer productPriceMonitorProductId);


	@Query("SELECT c FROM ProductPriceMonitorProductCheckDo c JOIN FETCH c.productPriceMonitorProduct p WHERE c.status = :status AND p.activeInd = :activeInd")
	List<ProductPriceMonitorProductCheckDo> findByStatusAndActiveInd(@Param("status") Integer status, @Param("activeInd") Integer activeInd);

	/**
	 * Find check records by status with eagerly fetched product monitor data
	 * The relationship is already defined in the entity, so this will automatically
	 * join with the PRODUCT_PRICE_MONITOR_PRODUCT table
	 */
	@Query("SELECT c FROM ProductPriceMonitorProductCheckDo c JOIN FETCH c.productPriceMonitorProduct p WHERE c.status = :status")
	List<ProductPriceMonitorProductCheckDo> findByStatusWithProductData(@Param("status") Integer status);

	/**
	 * Find check records by status and job trace UUID with eagerly fetched product monitor data
	 */
	@Query("SELECT c FROM ProductPriceMonitorProductCheckDo c JOIN FETCH c.productPriceMonitorProduct p WHERE c.jobTraceUuid = :jobTraceUuid AND c.status IN :statuses AND p.merchantId = :merchantId")
	List<ProductPriceMonitorProductCheckDo> findByStatusAndJobTraceUuidWithProductData(@Param("statuses") Set<Integer> statuses, @Param("jobTraceUuid") String jobTraceUuid, @Param("merchantId") Integer merchantId);

	@Query(value = "SELECT c FROM ProductPriceMonitorProductCheckDo c JOIN FETCH c.productPriceMonitorProduct p WHERE c.jobTraceUuid = :jobTraceUuid AND c.status IN :statuses AND p.merchantId = :merchantId",
	countQuery = "SELECT count(c) FROM ProductPriceMonitorProductCheckDo c JOIN c.productPriceMonitorProduct p WHERE c.jobTraceUuid = :jobTraceUuid AND c.status IN :statuses AND p.merchantId = :merchantId")
	Page<ProductPriceMonitorProductCheckDo> findPageByStatusAndJobTraceUuidWithProductData(@Param("statuses") Set<Integer> statuses, @Param("jobTraceUuid") String jobTraceUuid, @Param("merchantId") Integer merchantId, Pageable pageable);


	@Query(value = "select count(c) as count " +
		"from ProductPriceMonitorProductCheckDo c JOIN c.productPriceMonitorProduct p WHERE c.jobTraceUuid = :jobTraceUuid AND c.status IN :statuses")
	long countByStatusAndJobTraceUuid(@Param("statuses") Set<Integer> statuses, @Param("jobTraceUuid") String jobTraceUuid);


	@Query("SELECT c FROM ProductPriceMonitorProductCheckDo c JOIN FETCH c.productPriceMonitorProduct p WHERE c.jobTraceUuid = :jobTraceUuid AND c.status IN :statuses")
	List<ProductPriceMonitorProductCheckDo> findByJobTraceUuidAndStatusIn(@Param("jobTraceUuid") String jobTraceUuid, @Param("statuses") Set<Integer> statuses);

	@Query("SELECT c " +
		"FROM ProductPriceMonitorProductCheckDo c " +
		"JOIN FETCH c.productPriceMonitorProduct p " +
		"LEFT JOIN FETCH c.brand b " +
		"WHERE " +
		"c.jobTraceUuid = :jobTraceUuid " +
		"AND c.status IN :statuses")
	List<ProductPriceMonitorProductCheckDo> findJoinBrandByUuidAndStatusIn(@Param("jobTraceUuid") String jobTraceUuid, @Param("statuses") Set<Integer> statuses);
}
