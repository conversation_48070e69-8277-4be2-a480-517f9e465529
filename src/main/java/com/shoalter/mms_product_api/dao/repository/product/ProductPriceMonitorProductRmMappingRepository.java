package com.shoalter.mms_product_api.dao.repository.product;

import com.shoalter.mms_product_api.dao.repository.product.pojo.ProductPriceMonitorProductDo;
import com.shoalter.mms_product_api.dao.repository.product.pojo.ProductPriceMonitorProductRmMappingDo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import javax.transaction.Transactional;
import java.util.List;
import java.util.Set;

public interface ProductPriceMonitorProductRmMappingRepository extends JpaRepository<ProductPriceMonitorProductRmMappingDo, Long> {

	List<ProductPriceMonitorProductRmMappingDo> findByProductPriceMonitorProductId(Integer productPriceMonitorProductId);

	List<ProductPriceMonitorProductRmMappingDo> findByProductPriceMonitorProductIdIn(Set<Integer> productPriceMonitorProductIds);

	@Transactional
	@Modifying
	void deleteByProductPriceMonitorProductIdAndRmCode(Integer productPriceMonitorProductId, String rmCode);

	@Transactional
	@Modifying
	void deleteByProductPriceMonitorProductId(Integer productPriceMonitorProductId);


	@Query(value = "SELECT PPMPRM.* " +
		"FROM PRODUCT_PRICE_MONITOR_PRODUCT_RM_MAPPING PPMPRM " +
		"JOIN PRODUCT_PRICE_MONITOR_PRODUCT PPMP ON PPMPRM.PRODUCT_PRICE_MONITOR_PRODUCT_ID = PPMP.ID " +
		"JOIN PRODUCT_PRICE_MONITOR_PRODUCT_CHECK PPMPC ON PPMP.ID = PPMPC.PRODUCT_PRICE_MONITOR_PRODUCT_ID " +
		"WHERE PPMPC.JOB_TRACE_UUID = :jobTraceUuid " +
		"AND PPMPC.STATUS IN (:statuses)", nativeQuery = true)
	List<ProductPriceMonitorProductRmMappingDo> findRmCodesByJobTraceUuidAndStatuses(@Param("jobTraceUuid") String jobTraceUuid, @Param("statuses") Set<Integer> statuses);
}

