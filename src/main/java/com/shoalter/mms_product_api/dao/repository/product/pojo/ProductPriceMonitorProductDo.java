package com.shoalter.mms_product_api.dao.repository.product.pojo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;


@Entity
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Table(name = "PRODUCT_PRICE_MONITOR_PRODUCT")
public class ProductPriceMonitorProductDo {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private Integer id;

    @Column(name = "ACTIVE_IND", nullable = false)
    private Integer activeInd;

    @Column(name = "PRICE_STATUS", nullable = false)
    private Integer priceStatus;

    @Column(name = "BUS_UNIT_ID", nullable = false)
    private Integer busUnitId;

    @Column(name = "MERCHANT_ID", nullable = false)
    private Integer merchantId;

    @Column(name = "STORE_ID", nullable = false)
    private Integer storeId;

	@Column(name = "STOREFRONT_STORE_CODE")
	private String storefrontStoreCode;

    @Column(name = "STORE_SKU_ID", nullable = false)
    private String storeSkuId;

	@Column(name = "SKU_CODE", nullable = false)
	private String skuCode;

	@Column(name = "SKU_NAME")
	private String skuName;

	@Column(name = "SOURCE_PLATFORM", nullable = false)
	private String sourcePlatform;

	@Column(name = "TARGET_PLATFORM", nullable = false)
	private String targetPlatform;

	@Column(name = "TARGET_PRODUCT_CODE")
	private String targetProductCode;

	@Column(name = "TARGET_SKU_CODE")
	private String targetSkuCode;

	@Column(name = "SOURCE_CURRENCY_CODE", nullable = false)
	private String sourceCurrencyCode;

	@Column(name = "TARGET_CURRENCY_CODE", nullable = false)
	private String targetCurrencyCode;

	@Column(name = "TARGET_ORIGINAL_PRICE")
	private BigDecimal targetOriginalPrice;

	@Column(name = "TARGET_SELLING_PRICE")
	private BigDecimal targetSellingPrice;

	@Column(name = "TARGET_URL")
	private String targetUrl;

	@Column(name = "TARGET_PRICE_UPDATED_DATE")
	private LocalDateTime targetPriceUpdatedDate;

	@Column(name = "CREATED_DATE", nullable = false)
	private LocalDateTime createdDate;

	@Column(name = "CREATED_BY", nullable = false)
	private String createdBy;

	@Column(name = "LAST_UPDATED_DATE", nullable = false)
	private LocalDateTime lastUpdatedDate;

	@Column(name = "LAST_UPDATED_BY", nullable = false)
	private String lastUpdatedBy;


	public String toPrintExPlatformDetail() {
		return "ProductPriceMonitorProductDo{" +
			"targetSkuCode='" + targetSkuCode + '\'' +
			", targetProductCode='" + targetProductCode + '\'' +
			", targetPlatform='" + targetPlatform + '\'' +
			'}';
	}
}
