package com.shoalter.mms_product_api.service.partner_product_price;

import com.shoalter.mms_product_api.dao.repository.product.PartnerProductPriceHistoryRepository;
import com.shoalter.mms_product_api.dao.repository.product.PartnerProductPriceRepository;
import com.shoalter.mms_product_api.dao.repository.product.pojo.PartnerProductPriceDo;
import com.shoalter.mms_product_api.dao.repository.product.pojo.PartnerProductPriceHistoryDo;
import com.shoalter.mms_product_api.dao.repository.system.SysParmRepository;
import com.shoalter.mms_product_api.dao.repository.system.pojo.SysParmDo;
import com.shoalter.mms_product_api.mapper.PartnerProductPriceDataHistoryMapper;
import com.shoalter.mms_product_api.mapper.PartnerProductPriceDataMapper;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.external_system.mms_setting.enums.MmsSettingFunctionEnum;
import com.shoalter.mms_product_api.service.mms_cron_job.helper.MmsCronJobHelper;
import com.shoalter.mms_product_api.service.mms_cron_job.pojo.MmsCronJobData;
import com.shoalter.mms_product_api.service.notification.helper.NotificationHelper;
import com.shoalter.mms_product_api.service.partner_product_price.enums.TooniesStatusEnum;
import com.shoalter.mms_product_api.service.partner_product_price.helper.PartnerProductPriceHelper;
import com.shoalter.mms_product_api.service.partner_product_price.helper.TooniesSkuTemplateHelper;
import com.shoalter.mms_product_api.service.partner_product_price.pojo.PartnerProductPriceChangedReportData;
import com.shoalter.mms_product_api.service.product.helper.ExchangeRateHelper;
import com.shoalter.mms_product_api.service.product.helper.MmsThirdPartySkuHelper;
import com.shoalter.mms_product_api.service.product.helper.ProductMasterHelper;
import com.shoalter.mms_product_api.service.product.pojo.productmaster.response.ProductMasterLightweightProductResponseDto;
import com.shoalter.mms_product_api.service.product.pojo.productmaster.response.ProductMasterLightweightProductsDto;
import com.shoalter.mms_product_api.service.product.pojo.third_party.pojo.TooniesProductDetailResponseDto;
import com.shoalter.mms_product_api.service.product.pojo.third_party.pojo.TooniesProductDetailSkuDto;
import com.shoalter.mms_product_api.util.enums.CurrencyEnum;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.Executor;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.atLeastOnce;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.when;

@Slf4j
@ExtendWith(MockitoExtension.class)
class PartnerProductPriceServiceTest {

	@Mock
	private ExchangeRateHelper exchangeRateHelper;
	@Mock
	private ProductMasterHelper productMasterHelper;
	@Mock
	private MmsThirdPartySkuHelper mmsThirdPartySkuHelper;
	@Mock
	private TooniesSkuTemplateHelper tooniesSkuTemplateHelper;
	@Mock
	private NotificationHelper notificationHelper;
	@Mock
	private PartnerProductPriceHelper partnerProductPriceHelper;
	@Mock
	private MmsCronJobHelper mmsCronJobHelper;
	@Mock
	private SysParmRepository sysParmRepository;
	@Mock
	private PartnerProductPriceRepository partnerProductPriceRepository;
	@Mock
	private PartnerProductPriceHistoryRepository partnerProductPriceHistoryRepository;
	@Mock
	private PartnerProductPriceDataMapper partnerProductPriceDataMapper;
	@Mock
	private PartnerProductPriceDataHistoryMapper partnerProductPriceDataHistoryMapper;
	@Mock
	private Executor partnerProductPriceTaskExecutor;

	@InjectMocks
	private PartnerProductPriceService service;

	private final BigDecimal testExchangeRate = BigDecimal.TEN;


	@Test
	void test_savePartnerProductPriceRecord_success() {
		// mock executor
		doAnswer(invocation -> {
			Runnable r = invocation.getArgument(0);
			r.run();
			return null;
		}).when(partnerProductPriceTaskExecutor).execute(any(Runnable.class));

		// 1. Mock exchange rate
		when(exchangeRateHelper.getExchangeRateByCurrency(MmsSettingFunctionEnum.PCR, CurrencyEnum.RMB)).thenReturn(testExchangeRate);

		// 2. Mock whiteList
		SysParmDo parm = new SysParmDo();
		parm.setParmValue("ST123,ST456");
		when(sysParmRepository.findBySegmentAndCode(anyString(), anyString()))
			.thenReturn(List.of(parm));

		// 3. Mock Product Ids
		when(productMasterHelper.requestProductIds(any(), any()))
			.thenReturn(List.of("P1", "P2"));

		// 4. Mock LightweightProduct
		ProductMasterLightweightProductsDto skuDto = new ProductMasterLightweightProductsDto();
		skuDto.setStoreSkuId("SKU123");
		skuDto.setSkuCode("SKUCODE123");
		ProductMasterLightweightProductResponseDto lightweightResp = new ProductMasterLightweightProductResponseDto();
		lightweightResp.setProductCode("P1");
		lightweightResp.setProducts(List.of(skuDto));
		when(productMasterHelper.requestLightweightProduct(any(), any()))
			.thenReturn(List.of(lightweightResp));

		// 5. Mock MMS Toonies Detail
		TooniesProductDetailSkuDto tooniesSku = new TooniesProductDetailSkuDto();
		tooniesSku.setSkuCode("SKU123");
		tooniesSku.setPrice(new BigDecimal("199"));
		TooniesProductDetailResponseDto tooniesResp = new TooniesProductDetailResponseDto();
		tooniesResp.setProductCode("P1");
		tooniesResp.setSkus(List.of(tooniesSku));
		when(mmsThirdPartySkuHelper.fetchSkuTooniesDetail(anyString()))
			.thenReturn(tooniesResp);

		// 6. Mock PartnerProductPriceRepository
		when(partnerProductPriceRepository.findByBusUnitIdAndStoreSkuId(any(), any()))
			.thenReturn(Optional.empty());

		// mock cronJobHelper
		when(mmsCronJobHelper.canProcessCronJob(any()))
			.thenReturn(MmsCronJobData.builder().isCanProcessCronJob(true).build());
		when(mmsCronJobHelper.createOrUpdateProcessingCronJob(any(), any()))
			.thenReturn(MmsCronJobData.builder().isCanProcessCronJob(true).build());
		doNothing().when(mmsCronJobHelper).updateCronJobStatus(any(), any());

		// Act
		service.savePartnerProductPriceRecord();

		// Assert
		verify(exchangeRateHelper).getExchangeRateByCurrency(MmsSettingFunctionEnum.PCR, CurrencyEnum.RMB);
		verify(sysParmRepository).findBySegmentAndCode(anyString(), anyString());
		verify(productMasterHelper, atLeastOnce()).requestProductIds(any(), any());
	}

	@Test
	void test_savePartnerProductPriceRecord_exchangeRateNull() {
		when(exchangeRateHelper.getExchangeRateByCurrency(MmsSettingFunctionEnum.PCR, CurrencyEnum.RMB)).thenReturn(null);
		// mock cronJobHelper
		when(mmsCronJobHelper.canProcessCronJob(any()))
			.thenReturn(MmsCronJobData.builder().isCanProcessCronJob(true).build());

		service.savePartnerProductPriceRecord();
		verify(exchangeRateHelper).getExchangeRateByCurrency(MmsSettingFunctionEnum.PCR, CurrencyEnum.RMB);
	}

	@Test
	void test_savePartnerProductPriceRecord_supplierStoreWhiteListEmpty() {
		when(exchangeRateHelper.getExchangeRateByCurrency(MmsSettingFunctionEnum.PCR, CurrencyEnum.RMB)).thenReturn(testExchangeRate);
		when(sysParmRepository.findBySegmentAndCode(anyString(), anyString())).thenReturn(Collections.emptyList());
		// mock cronJobHelper
		when(mmsCronJobHelper.canProcessCronJob(any()))
			.thenReturn(MmsCronJobData.builder().isCanProcessCronJob(true).build());

		service.savePartnerProductPriceRecord();

		verify(sysParmRepository).findBySegmentAndCode(anyString(), anyString());
		verifyNoMoreInteractions(productMasterHelper);
	}


	@Test
	void test_generateStorefrontStoreCodeToProductIdsMap() {
		Set<String> storeCodes = Set.of("ST111", "ST222");
		UserDto user = UserDto.generateSystemUserDto();

		when(productMasterHelper.requestProductIds(eq(user), any()))
			.thenReturn(List.of("P1001", "P1002"));

		Map<String, List<String>> result = service.generateStorefrontStoreCodeToProductIdsMap(user, storeCodes);

		assertTrue(result.containsKey("ST111"));
		assertEquals(List.of("P1001", "P1002"), result.get("ST111"));
	}

	@Test
	void test_processSkusData_Db_Empty_TooniesSku_Present() {
		// Arrange
		ProductMasterLightweightProductsDto sku = new ProductMasterLightweightProductsDto("SKU333", "ST3_S_SKU333");
		Map<String, List<ProductMasterLightweightProductsDto>> productCodeSkusMap = Map.of("P3", List.of(sku));
		TooniesProductDetailSkuDto tooniesSku = new TooniesProductDetailSkuDto();
		tooniesSku.setSkuCode("SKU333");
		tooniesSku.setPrice(new BigDecimal("888"));
		Map<String, List<TooniesProductDetailSkuDto>> tooniesProductSkusMap = Map.of("P3", List.of(tooniesSku));

		when(partnerProductPriceRepository.findByBusUnitIdAndStoreSkuId(any(), any())).thenReturn(Optional.empty());

		doNothing().when(partnerProductPriceHelper).savePartnerProductPriceDo(
			any(), any(), any(), any(), any(), any(), any()
		);

		// Act
		service.processSkusData(productCodeSkusMap, tooniesProductSkusMap, testExchangeRate, "ST3");

		// assert save partnerProductPrice data
		ArgumentCaptor<TooniesStatusEnum> statusCaptor = ArgumentCaptor.forClass(TooniesStatusEnum.class);
		ArgumentCaptor<BigDecimal> rmbCaptor = ArgumentCaptor.forClass(BigDecimal.class);
		ArgumentCaptor<String> storeCaptor = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> productCaptor = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> skuCodeCaptor = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<String> storeSkuIdCaptor = ArgumentCaptor.forClass(String.class);
		ArgumentCaptor<BigDecimal> chargePriceCaptor = ArgumentCaptor.forClass(BigDecimal.class);

		verify(partnerProductPriceHelper, times(1)).savePartnerProductPriceDo(
			statusCaptor.capture(),
			rmbCaptor.capture(),
			storeCaptor.capture(),
			productCaptor.capture(),
			skuCodeCaptor.capture(),
			storeSkuIdCaptor.capture(),
			chargePriceCaptor.capture()
		);

		// assert save data
		assertEquals(TooniesStatusEnum.SKU_FOUND, statusCaptor.getValue());
		assertEquals(new BigDecimal("10"), rmbCaptor.getValue());
		assertEquals("ST3", storeCaptor.getValue());
		assertEquals("P3", productCaptor.getValue());
		assertEquals("SKU333", skuCodeCaptor.getValue());
		assertEquals("ST3_S_SKU333", storeSkuIdCaptor.getValue());
		assertEquals(new BigDecimal("888"), chargePriceCaptor.getValue());
	}

	@Test
	void test_processSkusData_Db_Present_TooniesSku_Present_Price_UnChanged() {
		ProductMasterLightweightProductsDto sku = new ProductMasterLightweightProductsDto("SKU444", "SKUCODE444");
		Map<String, List<ProductMasterLightweightProductsDto>> productCodeSkusMap = Map.of("P4", List.of(sku));
		TooniesProductDetailSkuDto tooniesSku = new TooniesProductDetailSkuDto();
		tooniesSku.setSkuCode("SKU444");
		tooniesSku.setPrice(new BigDecimal("123"));
		Map<String, List<TooniesProductDetailSkuDto>> tooniesProductSkusMap = Map.of("P4", List.of(tooniesSku));

		PartnerProductPriceDo priceDo = new PartnerProductPriceDo();
		priceDo.setChargeConverted(new BigDecimal("123.00"));
		priceDo.setStatus(TooniesStatusEnum.SKU_FOUND.getValue());

		when(partnerProductPriceRepository.findByBusUnitIdAndStoreSkuId(any(), any()))
			.thenReturn(Optional.of(priceDo));

		service.processSkusData(productCodeSkusMap, tooniesProductSkusMap, BigDecimal.ONE, "ST4");
		// verify not call partnerProductPriceHelper
		verify(partnerProductPriceHelper, never()).saveHistoryAndUpdatePartnerProductPriceRecord(any(), any(), any(), any());
	}

	@Test
	void test_generateReportDate_ChargeConvertedPrice_Changed() {
		PartnerProductPriceDo curr = new PartnerProductPriceDo();
		curr.setSkuCode("SKU999");
		curr.setStoreSkuId("AAA_S_SKU999");
		curr.setChargePrice(new BigDecimal("100"));
		curr.setChargeConverted(new BigDecimal("100"));
		curr.setServiceFee(BigDecimal.ZERO);

		PartnerProductPriceHistoryDo his = new PartnerProductPriceHistoryDo();
		his.setSkuCode("SKU999");
		his.setStoreSkuId("AAA_S_SKU999");
		his.setChargePrice(new BigDecimal("50"));
		his.setChargeConverted(new BigDecimal("50"));
		his.setServiceFee(BigDecimal.ZERO);
		his.setEndDate(LocalDateTime.now().minusHours(2));

		// latest history
		PartnerProductPriceHistoryDo his2 = new PartnerProductPriceHistoryDo();
		his2.setSkuCode("SKU999");
		his2.setStoreSkuId("AAA_S_SKU999");
		his2.setChargePrice(new BigDecimal("30"));
		his2.setChargeConverted(new BigDecimal("30"));
		his2.setServiceFee(BigDecimal.ZERO);
		his2.setEndDate(LocalDateTime.now().minusHours(1));

		List<PartnerProductPriceChangedReportData> result = service.generateReportDate(List.of(curr), List.of(his, his2));
		assertFalse(result.isEmpty());
		assertEquals(result.get(0).getOriginalChargePrice(), (new BigDecimal("30")));
	}

	@Test
	void test_generateReportDate_Price_Unchanged() {
		PartnerProductPriceDo curr = new PartnerProductPriceDo();
		curr.setSkuCode("SKU999");
		curr.setStoreSkuId("AAA_S_SKU999");
		curr.setChargePrice(new BigDecimal("100"));
		curr.setServiceFee(BigDecimal.ZERO);

		PartnerProductPriceHistoryDo his = new PartnerProductPriceHistoryDo();
		his.setSkuCode("SKU999");
		his.setStoreSkuId("AAA_S_SKU999");
		his.setChargePrice(new BigDecimal("100"));
		his.setServiceFee(BigDecimal.ZERO);
		his.setEndDate(LocalDateTime.now().minusHours(1));

		List<PartnerProductPriceChangedReportData> result = service.generateReportDate(List.of(curr), List.of(his));
		assertTrue(result.isEmpty());
	}
}
