package com.shoalter.mms_product_api.service.base.pojo;

import com.shoalter.mms_product_api.config.product.StatusCodeEnum;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

@DisplayName("SyncResponseDto Test")
class SyncResponseDtoTest {

    private SyncResponseDto<String> dto;

    @BeforeEach
    void setUp() {
        dto = SyncResponseDto.<String>builder().build();
    }

    @Nested
    @DisplayName("Sync Status Operations")
    class SyncStatusOperations {

        @Test
        @DisplayName("Should not allow downgrading from synced to unsynced state")
        void shouldNotAllowDowngradingSyncStatus() {
            // Given
            dto.addSyncStatus(SyncService.HYBRIS, true);

            // When
            dto.addSyncStatus(SyncService.HYBRIS, false);

            // Then
            assertTrue(dto.isSyncedTo(SyncService.HYBRIS));
        }

        @Test
        @DisplayName("Should return unmodifiable sync status map")
        void shouldReturnUnmodifiableMap() {
            // Given
            dto.addSyncStatus(SyncService.HYBRIS, true);
            Map<SyncService, Boolean> statusMap = dto.getSyncedStatusMap();

            // Then
            assertThrows(UnsupportedOperationException.class, () -> statusMap.put(SyncService.HYBRIS, false));
            assertThrows(UnsupportedOperationException.class, statusMap::clear);
        }

        @Test
        @DisplayName("Should correctly check all sync statuses")
        void shouldCorrectlyCheckAllSyncStatuses() {
            // Given empty map
            assertFalse(dto.isAllSynced());

            // When all synced
            dto.addSyncStatus(SyncService.HYBRIS, true);
            assertTrue(dto.isAllSynced());

            // When partially synced
            dto.addSyncStatus(SyncService.OTHER, false);
            assertFalse(dto.isAllSynced());
        }
    }

    @Nested
    @DisplayName("Bulk Operations")
    class BulkOperations {

        @Test
        @DisplayName("Should correctly merge sync statuses from another DTO")
        void shouldMergeSyncStatuses() {
            // Given
            SyncResponseDto<String> sourceDto = SyncResponseDto.<String>builder().build();
            sourceDto.addSyncStatus(SyncService.HYBRIS, true);
            sourceDto.addSyncStatus(SyncService.OTHER, false);

            // When
            dto.addAllSyncStatus(sourceDto.getSyncedStatusMap());

            // Then
            assertTrue(dto.isSyncedTo(SyncService.HYBRIS));
            assertFalse(dto.isSyncedTo(SyncService.OTHER));
        }

        @Test
        @DisplayName("Should correctly merge error messages")
        void shouldMergeErrorMessages() {
            // Given
            List<String> errorMessages = List.of("error1", "error2");

            // When
            dto.addAllErrorMessages(errorMessages);

            // Then
            assertEquals(2, dto.getErrorMessageList().size());
            assertTrue(dto.getErrorMessageList().containsAll(errorMessages));
        }
    }

    @Nested
    @DisplayName("Inheritance Behavior")
    class InheritanceBehavior {

        @Test
        @DisplayName("Should correctly inherit from ResponseDto")
        void shouldInheritResponseDtoProperties() {
            // Given
            String testData = "test data";
            ResponseDto<String> responseDto = ResponseDto.success(testData);

            // When
            SyncResponseDto<String> syncDto = SyncResponseDto.from(responseDto);

            // Then
            assertEquals(StatusCodeEnum.SUCCESS.getCode(), syncDto.getStatus());
            assertEquals(testData, syncDto.getData());
        }
    }

    @Nested
    @DisplayName("Edge Cases")
    class EdgeCases {

        @Test
        @DisplayName("Should handle empty sync status map")
        void shouldHandleEmptySyncStatus() {
            // When
            SyncResponseDto<String> newDto = SyncResponseDto.<String>builder().build();
            newDto.addAllSyncStatus(dto.getSyncedStatusMap());

            // Then
            assertTrue(newDto.getSyncedStatusMap().isEmpty());
        }

        @Test
        @DisplayName("Should handle null error message list")
        void shouldHandleNullErrorMessageList() {
            // When
            dto.addAllErrorMessages(null);

            // Then
            assertNull(dto.getErrorMessageList());
        }

        @Test
        @DisplayName("Should handle null error message list")
        void shouldHandleImmutableListErrorMessageList() {
            // When
            dto.addAllErrorMessages(List.of("error1", "error2"));
            dto.addAllErrorMessages(List.of("error3", "error4"));

            // Then
            assertEquals(4, dto.getErrorMessageList().size());
        }
    }
}
