package com.shoalter.mms_product_api.service.price_alert;

import com.shoalter.mms_product_api.config.product.MonitorProductCheckStatus;
import com.shoalter.mms_product_api.config.type.ConstantType;
import com.shoalter.mms_product_api.dao.mapper.product.ProductStorePromotionMapper;
import com.shoalter.mms_product_api.dao.mapper.product.pojo.ProductStorePromotionDto;
import com.shoalter.mms_product_api.dao.repository.product.ProductPriceMonitorProductCheckRepository;
import com.shoalter.mms_product_api.dao.repository.product.pojo.ProductPriceMonitorProductCheckDo;
import com.shoalter.mms_product_api.dao.repository.product.pojo.ProductPriceMonitorProductDo;
import com.shoalter.mms_product_api.dao.repository.product.pojo.ProductPriceMonitorProductRmMappingDo;
import com.shoalter.mms_product_api.dao.repository.rm.pojo.RmTeamUserInfo;
import com.shoalter.mms_product_api.service.price_alert.pojo.PriceConvertContent;
import com.shoalter.mms_product_api.service.product.pojo.BuProductDto;
import com.shoalter.mms_product_api.service.product.pojo.HktvProductDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterResultDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterStoreSkuIdResponseDto;
import com.shoalter.mms_product_api.service.product.helper.ProductMasterHelper;
import com.shoalter.mms_product_api.config.product.StatusCodeEnum;
import com.shoalter.mms_product_api.util.enums.CurrencyEnum;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ProductPriceMonitorCheckServiceTest {

	@Mock
	private ProductStorePromotionMapper productStorePromotionMapper;

	@Mock
	private ProductPriceMonitorProductCheckRepository productPriceMonitorProductCheckRepository;

	@Mock
	private ProductMasterHelper productMasterHelper;

	@Captor
	private ArgumentCaptor<List<ProductPriceMonitorProductCheckDo>> checkRecordsCaptor;

	@InjectMocks
	private ProductPriceMonitorCheckService service;

	private static final String JOB_TRACE_UUID = "test-job-trace-uuid";
	private static final String BU_CODE = "HKTV";
	private static final Integer MERCHANT_ID = 12345;

	@Test
	@DisplayName("Should return true when source price exceeds target price plus threshold")
	void isSourcePriceOverAlertLimitation_WhenSourcePriceExceedsThreshold_ShouldReturnTrue() {
		// Setup: source price = 100, target price = 80, threshold = 10% (max allowed target price = 88)
		BigDecimal sourcePrice = new BigDecimal("100");
		BigDecimal targetPrice = new BigDecimal("80");
		BigDecimal threshold = new BigDecimal("10");  // 10%

		// Execute target method
		Boolean result = service.isSourcePriceOverAlertLimitation(sourcePrice, targetPrice, threshold);

		// Verify result
		assertEquals(true, result, "Should return true when source price (100) exceeds target price (80) + threshold (10%)");
	}

	@Test
	@DisplayName("Should return false when source price equals target price plus threshold")
	void isSourcePriceOverAlertLimitation_WhenSourcePriceEqualsThreshold_ShouldReturnFalse() {
		// Setup: source price = 88, target price = 80, threshold = 10% (max allowed target price = 88)
		BigDecimal sourcePrice = new BigDecimal("88");
		BigDecimal targetPrice = new BigDecimal("80");
		BigDecimal threshold = new BigDecimal("10");  // 10%

		// Execute target method
		Boolean result = service.isSourcePriceOverAlertLimitation(sourcePrice, targetPrice, threshold);

		// Verify result
		assertEquals(false, result, "Should return false when source price (88) equals target price (80) + threshold (10%)");
	}

	@Test
	@DisplayName("Should return false when source price is below target price plus threshold")
	void isSourcePriceOverAlertLimitation_WhenSourcePriceBelowThreshold_ShouldReturnFalse() {
		// Setup: source price = 85, target price = 80, threshold = 10% (max allowed target price = 88)
		BigDecimal sourcePrice = new BigDecimal("85");
		BigDecimal targetPrice = new BigDecimal("80");
		BigDecimal threshold = new BigDecimal("10");  // 10%

		// Execute target method
		Boolean result = service.isSourcePriceOverAlertLimitation(sourcePrice, targetPrice, threshold);

		// Verify result
		assertEquals(false, result, "Should return false when source price (85) is below target price (80) + threshold (10%)");
	}

	@ParameterizedTest(name = "When source price={0}, target price={1}, threshold={2}%, result should be {3}")
	@CsvSource({
		"110, 100, 5, true",    // Exceeds threshold (100 + 5% = 105)
		"105, 100, 5, false",   // Equals threshold (100 + 5% = 105)
		"104, 100, 5, false",   // Below threshold (100 + 5% = 105)
		"100, 100, 0, false",   // Equal prices, zero threshold
		"0, 100, 10, false",    // Source price is zero
		"100, 0, 10, true",     // Target price is zero
		"0, 0, 10, false"       // Both prices are zero
	})
	@DisplayName("Tests various price combinations against threshold logic")
	void isSourcePriceOverAlertLimitation_WithVariousPriceCombinations(
		String sourcePrice, String targetPrice, String threshold, boolean expected) {

		// Prepare test data
		BigDecimal sourceDecimal = new BigDecimal(sourcePrice);
		BigDecimal targetDecimal = new BigDecimal(targetPrice);
		BigDecimal thresholdDecimal = new BigDecimal(threshold);

		// Execute target method
		Boolean result = service.isSourcePriceOverAlertLimitation(sourceDecimal, targetDecimal, thresholdDecimal);

		// Verify result
		assertEquals(expected, result,
			String.format("Source price=%s, Target price=%s, Threshold=%s%%", sourcePrice, targetPrice, threshold));
	}

	@Test
	@DisplayName("Manual calculation should match method result")
	void isSourcePriceOverAlertLimitation_CalculationShouldMatchManual() {
		// Prepare test data
		BigDecimal sourcePrice = new BigDecimal("123.45");
		BigDecimal targetPrice = new BigDecimal("100");
		BigDecimal threshold = new BigDecimal("20");

		// Calculate expected result manually
		BigDecimal multiplier = BigDecimal.ONE.add(threshold.divide(new BigDecimal("100"), 10, RoundingMode.HALF_UP));
		BigDecimal thresholdPrice = targetPrice.multiply(multiplier);
		boolean manualResult = sourcePrice.compareTo(thresholdPrice) > 0;

		// Execute target method
		Boolean methodResult = service.isSourcePriceOverAlertLimitation(sourcePrice, targetPrice, threshold);

		// Verify method result matches manual calculation
		assertEquals(manualResult, methodResult,
			"Method calculation should match manual calculation");
	}

	// Tests for getSourcePrice method

	@Test
	@DisplayName("Should return discount price when product has promotional discount")
	void getSourcePrice_WithPromotionDiscountPrice_ShouldReturnDiscountPrice() {
		// Create mock objects
		ProductMasterResultDto sourceData = mock(ProductMasterResultDto.class);
		BuProductDto additional = mock(BuProductDto.class);
		HktvProductDto hktv = mock(HktvProductDto.class);

		// Setup mock behavior
		when(sourceData.getAdditional()).thenReturn(additional);
		when(additional.getHktv()).thenReturn(hktv);
		when(hktv.getStoreSkuId()).thenReturn("STORE-SKU-123");

		// Create promotion object with discount price
		ProductStorePromotionDto promotion = new ProductStorePromotionDto();
		promotion.setDiscountPrice(new BigDecimal("80"));

		// Mock productStorePromotionMapper behavior
		when(productStorePromotionMapper.findActivePromotionsByStoreSkuId(
			"STORE-SKU-123", ConstantType.ACTIVE))
			.thenReturn(Collections.singletonList(promotion));

		// Execute target method
		BigDecimal result = service.getSourcePrice(sourceData);

		// Verify result
		assertEquals(new BigDecimal("80"), result, "Should return discount price (80) when product has promotional discount");
	}

	@Test
	@DisplayName("Should return selling price when promotion has no discount price")
	void getSourcePrice_WithPromotionButNoDiscountPrice_ShouldReturnSellingPrice() {
		// Create mock objects
		ProductMasterResultDto sourceData = mock(ProductMasterResultDto.class);
		BuProductDto additional = mock(BuProductDto.class);
		HktvProductDto hktv = mock(HktvProductDto.class);

		// Setup mock behavior
		when(sourceData.getAdditional()).thenReturn(additional);
		when(additional.getHktv()).thenReturn(hktv);
		when(hktv.getStoreSkuId()).thenReturn("STORE-SKU-123");
		when(hktv.getSellingPrice()).thenReturn(new BigDecimal("100"));

		// Create promotion object without discount price
		ProductStorePromotionDto promotion = new ProductStorePromotionDto();
		promotion.setDiscountPrice(null);

		// Mock productStorePromotionMapper behavior
		when(productStorePromotionMapper.findActivePromotionsByStoreSkuId(
			"STORE-SKU-123", ConstantType.ACTIVE))
			.thenReturn(Collections.singletonList(promotion));

		// Execute target method
		BigDecimal result = service.getSourcePrice(sourceData);

		// Verify result
		assertEquals(new BigDecimal("100"), result, "Should return selling price (100) when promotion has no discount price");
	}

	@Test
	@DisplayName("Should return selling price when product has no promotions")
	void getSourcePrice_WithoutPromotion_ShouldReturnSellingPrice() {
		// Create mock objects
		ProductMasterResultDto sourceData = mock(ProductMasterResultDto.class);
		BuProductDto additional = mock(BuProductDto.class);
		HktvProductDto hktv = mock(HktvProductDto.class);

		// Setup mock behavior
		when(sourceData.getAdditional()).thenReturn(additional);
		when(additional.getHktv()).thenReturn(hktv);
		when(hktv.getStoreSkuId()).thenReturn("STORE-SKU-123");
		when(hktv.getSellingPrice()).thenReturn(new BigDecimal("100"));

		// Mock productStorePromotionMapper behavior - return empty list
		when(productStorePromotionMapper.findActivePromotionsByStoreSkuId(
			"STORE-SKU-123", ConstantType.ACTIVE))
			.thenReturn(Collections.emptyList());

		// Execute target method
		BigDecimal result = service.getSourcePrice(sourceData);

		// Verify result
		assertEquals(new BigDecimal("100"), result, "Should return selling price (100) when product has no promotions");
	}

	@Test
	@DisplayName("processProductGroup - HKD scenario - Should convert HKD to RMB when checking threshold")
	void processProductGroup_HkdScenario() {
		 // Use spy to monitor service behavior
		ProductPriceMonitorCheckService testService = spy(service);

		// ===== Key Price Data Setup =====
		// Source price in HKD
		BigDecimal sourceSellingPrice = new BigDecimal("110");
		BigDecimal sourceOriginalPrice = new BigDecimal("110");

		// Target price in RMB
		BigDecimal targetSellingPrice = new BigDecimal("90");
		BigDecimal targetOriginalPrice = new BigDecimal("85");

		// Price Threshold and Exchange Rate Setting
		BigDecimal hkdPercentageThreshold = new BigDecimal("10"); // 10%
		BigDecimal exchangeRate = new BigDecimal("1.1"); // 110HKD / 1.1 = 100RMB

		// ===== Mock Product Data =====
		// Configure API response product data
		ProductMasterStoreSkuIdResponseDto mockResponse = new ProductMasterStoreSkuIdResponseDto();
		mockResponse.setStatus(StatusCodeEnum.SUCCESS.name());

		ProductMasterResultDto mockProduct = new ProductMasterResultDto();
		mockProduct.setOriginalPrice(sourceOriginalPrice);

		BuProductDto mockAdditional = new BuProductDto();
		HktvProductDto mockHktv = new HktvProductDto();
		mockHktv.setStoreSkuId("SKU001");
		mockHktv.setSellingPrice(sourceSellingPrice);
		mockAdditional.setHktv(mockHktv);
		mockProduct.setAdditional(mockAdditional);

		mockResponse.setData(Collections.singletonList(mockProduct));
		when(productMasterHelper.requestProductByStoreSkuId(any(), any())).thenReturn(mockResponse);

		// ===== Create Test Data =====
		// Configure price conversion parameters
		PriceConvertContent priceConvertContent = PriceConvertContent.builder()
			.hkdPercentageThreshold(hkdPercentageThreshold) // Set HKD threshold to 10%
			.rmbToHkdExchangeRate(exchangeRate) // Set exchange rate to 1.1
			.build();

		// Create test product in HKD
		ProductPriceMonitorProductDo product = new ProductPriceMonitorProductDo();
		product.setId(1);
		product.setMerchantId(MERCHANT_ID);
		product.setStoreSkuId("SKU001");
		product.setSourceCurrencyCode(CurrencyEnum.HKD.name());  // HKD source price
		product.setTargetSellingPrice(targetSellingPrice);
		product.setTargetOriginalPrice(targetOriginalPrice);

		// Create price check record
		ProductPriceMonitorProductCheckDo checkRecord = new ProductPriceMonitorProductCheckDo();
		ProductPriceMonitorProductDo priceMonitorProductDo = new ProductPriceMonitorProductDo();
		priceMonitorProductDo.setId(1);
		priceMonitorProductDo.setSourceCurrencyCode(CurrencyEnum.HKD.name());
		checkRecord.setProductPriceMonitorProduct(priceMonitorProductDo);
		checkRecord.setProductPriceMonitorProductId(1);
		checkRecord.setSourceSellingPrice(new BigDecimal("1100"));
		checkRecord.setSourceOriginalPrice(new BigDecimal("1100"));
		checkRecord.setTargetSellingPrice(targetSellingPrice);
		checkRecord.setTargetOriginalPrice(targetOriginalPrice);
		checkRecord.setStatus(MonitorProductCheckStatus.PENDING.getValue());

		when(productPriceMonitorProductCheckRepository.findByProductPriceMonitorProductId(any()))
			.thenReturn(Optional.of(checkRecord));

		// ===== Execute Test =====
		testService.processProductGroup(JOB_TRACE_UUID, Collections.singletonList(product), priceConvertContent, BU_CODE);

		// ===== Verify Results =====
		// 1. Verify HKD price correctly converted to RMB
		assertEquals(0, new BigDecimal("100.0000").compareTo(checkRecord.getSourceSellingPrice()),
				"HKD price should be converted, 110 HKD should be converted to 100.0000 RMB");
		assertEquals(0, new BigDecimal("100.0000").compareTo(checkRecord.getSourceOriginalPrice()),
				"HKD price should be converted, 110 HKD should be converted to 100.0000 RMB");

		// 2.Verify price and threshold comparison logic
		// 計算公式：目標價格 * (1 + 閾值百分比) (Formula: target price * (1 + threshold percentage))
		// 90元 * (1 + 10%) = 99元
		// 源價格100.0000元 > 99元，超過閾值，因此狀態應為PROCESSING (Source price 100.0000 yuan > 99 yuan, exceeding threshold, status should be PROCESSING)
		assertEquals(MonitorProductCheckStatus.PROCESSING.getValue(), checkRecord.getStatus(),
				"Source price (110.0000 yuan) exceeds target price (90 yuan) + threshold (10%) = 99 yuan, status should be PROCESSING");
	}

	@Test
	@DisplayName("processProductGroup - RMB scenario - Should not convert RMB when checking threshold")
	void processProductGroup_RmbScenario() {
		// Use spy to monitor service behavior
		ProductPriceMonitorCheckService testService = spy(service);

		// ===== Key Price Data Setup =====
		// HKTV Source price in (RMB)
		BigDecimal sourceSellingPrice = new BigDecimal("40");
		BigDecimal sourceOriginalPrice = new BigDecimal("45");

		// TMALL Target price in (RMB)
		BigDecimal targetSellingPrice = new BigDecimal("35");
		BigDecimal targetOriginalPrice = new BigDecimal("40");

		// Price Threshold Setting
		BigDecimal rmbPercentageThreshold = new BigDecimal("20"); // 20%

		// ===== Mock Product Data =====
		// Configure API response product data
		ProductMasterStoreSkuIdResponseDto mockResponse = new ProductMasterStoreSkuIdResponseDto();
		mockResponse.setStatus(StatusCodeEnum.SUCCESS.name());

		ProductMasterResultDto mockProduct = new ProductMasterResultDto();
		mockProduct.setOriginalPrice(sourceOriginalPrice);

		BuProductDto mockAdditional = new BuProductDto();
		HktvProductDto mockHktv = new HktvProductDto();
		mockHktv.setStoreSkuId("SKU002");
		mockHktv.setSellingPrice(sourceSellingPrice);
		mockAdditional.setHktv(mockHktv);
		mockProduct.setAdditional(mockAdditional);

		mockResponse.setData(Collections.singletonList(mockProduct));
		when(productMasterHelper.requestProductByStoreSkuId(any(), any())).thenReturn(mockResponse);

		// ===== Create Test Data =====
		// Configure price conversion parameters
		PriceConvertContent priceConvertContent = PriceConvertContent.builder()
			.rmbPercentageThreshold(rmbPercentageThreshold) // Set RMB threshold to 20%
			.build();

		// Create test product in RMB
		ProductPriceMonitorProductDo product = new ProductPriceMonitorProductDo();
		product.setId(2);
		product.setMerchantId(MERCHANT_ID);
		product.setStoreSkuId("SKU002");
		product.setSourceCurrencyCode(CurrencyEnum.RMB.name());  // RMB source price
		product.setTargetSellingPrice(targetSellingPrice);
		product.setTargetOriginalPrice(targetOriginalPrice);

		// Create price check record
		ProductPriceMonitorProductCheckDo checkRecord = new ProductPriceMonitorProductCheckDo();
		checkRecord.setProductPriceMonitorProductId(2);
		checkRecord.setSourceSellingPrice(new BigDecimal("400")); // Will be updated during test
		checkRecord.setSourceOriginalPrice(new BigDecimal("450")); // Will be updated during test
		checkRecord.setTargetSellingPrice(targetSellingPrice);
		checkRecord.setTargetOriginalPrice(targetOriginalPrice);
		checkRecord.setStatus(MonitorProductCheckStatus.PENDING.getValue());

		when(productPriceMonitorProductCheckRepository.findByProductPriceMonitorProductId(any()))
			.thenReturn(Optional.of(checkRecord));

		// ===== Execute Test =====
		testService.processProductGroup(JOB_TRACE_UUID, Collections.singletonList(product), priceConvertContent, BU_CODE);

		// ===== Verify Results =====
		// 1. Verify RMB price not converted - using original price directly
		assertEquals(0, sourceSellingPrice.compareTo(checkRecord.getSourceSellingPrice()),
				"RMB price should not be converted, should remain at 40 yuan)");
		assertEquals(0, sourceOriginalPrice.compareTo(checkRecord.getSourceOriginalPrice()),
				"RMB price should not be converted, should remain at 45 yuan)");

		// 2. Verify price and threshold comparison logic
		// 計算公式：目標價格 * (1 + 閾值百分比) (Formula: target price * (1 + threshold percentage))
		// 35元 * (1 + 20%) = 42元 (35 yuan * (1 + 20%) = 42 yuan)
		// 源價格40元 < 42元，未超過閾值，因此狀態應為CHECKED (Source price 40 yuan < 42 yuan, not exceeding threshold, status should be CHECKED)
		assertEquals(MonitorProductCheckStatus.CHECKED.getValue(), checkRecord.getStatus(),
				"Source price (40 yuan) does not exceed target price (35 yuan) + threshold (20%) = 42 yuan, status should be CHECKED");
	}

	@Test
	@DisplayName("processProductGroup - Empty products list - Should do nothing")
	void processProductGroup_EmptyProductsList_ShouldDoNothing() {
		// Use the original service instance
		// Setup
		List<ProductPriceMonitorProductDo> emptyProducts = Collections.emptyList();
		PriceConvertContent priceConvertContent = PriceConvertContent.builder().build();

		// Call the method
		service.processProductGroup(JOB_TRACE_UUID, emptyProducts, priceConvertContent, BU_CODE);

		// Verify no interactions with repository
		verify(productPriceMonitorProductCheckRepository, never()).saveAll(anyList());
	}

	// ===== groupRmInfoByMonitorId Tests =====

	@Test
	@DisplayName("groupRmInfoByMonitorId - Normal case with valid mapping data")
	void groupRmInfoByMonitorId_WithValidMappingData_ShouldReturnCorrectGrouping() {
		// Setup test data
		List<ProductPriceMonitorProductRmMappingDo> rmMappingData = Arrays.asList(
			ProductPriceMonitorProductRmMappingDo.builder()
				.productPriceMonitorProductId(1)
				.rmCode("RM001")
				.build(),
			ProductPriceMonitorProductRmMappingDo.builder()
				.productPriceMonitorProductId(1)
				.rmCode("RM002")
				.build(),
			ProductPriceMonitorProductRmMappingDo.builder()
				.productPriceMonitorProductId(2)
				.rmCode("RM003")
				.build()
		);

		Map<String, RmTeamUserInfo> rmCodeToInfoMap = new HashMap<>();
		rmCodeToInfoMap.put("RM001", createMockRmTeamUserInfo("RM001", "John Doe", "Jane Smith"));
		rmCodeToInfoMap.put("RM002", createMockRmTeamUserInfo("RM002", "Bob Wilson", "Alice Brown"));
		rmCodeToInfoMap.put("RM003", createMockRmTeamUserInfo("RM003", "Charlie Davis", "Diana Lee"));

		// Execute
		Map<Integer, List<RmTeamUserInfo>> result = service.groupRmInfoByMonitorId(rmMappingData, rmCodeToInfoMap);

		// Verify
		assertNotNull(result);
		assertEquals(2, result.size());

		// Verify monitor ID 1 has 2 RM infos
		assertTrue(result.containsKey(1));
		assertEquals(2, result.get(1).size());
		assertEquals("RM001", result.get(1).get(0).getRmCode());
		assertEquals("RM002", result.get(1).get(1).getRmCode());

		// Verify monitor ID 2 has 1 RM info
		assertTrue(result.containsKey(2));
		assertEquals(1, result.get(2).size());
		assertEquals("RM003", result.get(2).get(0).getRmCode());
	}

	@Test
	@DisplayName("groupRmInfoByMonitorId - Empty mapping data should return empty map")
	void groupRmInfoByMonitorId_WithEmptyMappingData_ShouldReturnEmptyMap() {
		// Setup
		List<ProductPriceMonitorProductRmMappingDo> emptyMappingData = Collections.emptyList();
		Map<String, RmTeamUserInfo> rmCodeToInfoMap = new HashMap<>();
		rmCodeToInfoMap.put("RM001", createMockRmTeamUserInfo("RM001", "John Doe", "Jane Smith"));

		// Execute
		Map<Integer, List<RmTeamUserInfo>> result = service.groupRmInfoByMonitorId(emptyMappingData, rmCodeToInfoMap);

		// Verify
		assertNotNull(result);
		assertTrue(result.isEmpty());
	}

	@Test
	@DisplayName("groupRmInfoByMonitorId - Null mapping data should return empty map")
	void groupRmInfoByMonitorId_WithNullMappingData_ShouldReturnEmptyMap() {
		// Setup
		Map<String, RmTeamUserInfo> rmCodeToInfoMap = new HashMap<>();
		rmCodeToInfoMap.put("RM001", createMockRmTeamUserInfo("RM001", "John Doe", "Jane Smith"));

		// Execute
		Map<Integer, List<RmTeamUserInfo>> result = service.groupRmInfoByMonitorId(null, rmCodeToInfoMap);

		// Verify
		assertNotNull(result);
		assertTrue(result.isEmpty());
	}

	@Test
	@DisplayName("groupRmInfoByMonitorId - Missing RM info should filter out null values")
	void groupRmInfoByMonitorId_WithMissingRmInfo_ShouldFilterOutNullValues() {
		// Setup test data with some RM codes not in the info map
		List<ProductPriceMonitorProductRmMappingDo> rmMappingData = Arrays.asList(
			ProductPriceMonitorProductRmMappingDo.builder()
				.productPriceMonitorProductId(1)
				.rmCode("RM001")
				.build(),
			ProductPriceMonitorProductRmMappingDo.builder()
				.productPriceMonitorProductId(1)
				.rmCode("RM999") // This RM code doesn't exist in info map
				.build(),
			ProductPriceMonitorProductRmMappingDo.builder()
				.productPriceMonitorProductId(2)
				.rmCode("RM002")
				.build()
		);

		Map<String, RmTeamUserInfo> rmCodeToInfoMap = new HashMap<>();
		rmCodeToInfoMap.put("RM001", createMockRmTeamUserInfo("RM001", "John Doe", "Jane Smith"));
		rmCodeToInfoMap.put("RM002", createMockRmTeamUserInfo("RM002", "Bob Wilson", "Alice Brown"));
		// Note: RM999 is not in the map

		// Execute
		Map<Integer, List<RmTeamUserInfo>> result = service.groupRmInfoByMonitorId(rmMappingData, rmCodeToInfoMap);

		// Verify
		assertNotNull(result);
		assertEquals(2, result.size());

		// Verify monitor ID 1 has only 1 RM info (RM999 should be filtered out)
		assertTrue(result.containsKey(1));
		assertEquals(1, result.get(1).size());
		assertEquals("RM001", result.get(1).get(0).getRmCode());

		// Verify monitor ID 2 has 1 RM info
		assertTrue(result.containsKey(2));
		assertEquals(1, result.get(2).size());
		assertEquals("RM002", result.get(2).get(0).getRmCode());
	}

	@Test
	@DisplayName("groupRmInfoByMonitorId - Empty RM info map should return empty lists")
	void groupRmInfoByMonitorId_WithEmptyRmInfoMap_ShouldReturnEmptyLists() {
		// Setup
		List<ProductPriceMonitorProductRmMappingDo> rmMappingData = Arrays.asList(
			ProductPriceMonitorProductRmMappingDo.builder()
				.productPriceMonitorProductId(1)
				.rmCode("RM001")
				.build()
		);

		Map<String, RmTeamUserInfo> emptyRmCodeToInfoMap = Collections.emptyMap();

		// Execute
		Map<Integer, List<RmTeamUserInfo>> result = service.groupRmInfoByMonitorId(rmMappingData, emptyRmCodeToInfoMap);

		// Verify
		assertNotNull(result);
		assertEquals(1, result.size());
		assertTrue(result.containsKey(1));
		assertTrue(result.get(1).isEmpty()); // Should be empty list, not null
	}

	@Test
	@DisplayName("groupRmInfoByMonitorId - Multiple monitor IDs with same RM codes")
	void groupRmInfoByMonitorId_WithMultipleMonitorIdsSameRmCodes_ShouldGroupCorrectly() {
		// Setup test data where different monitor IDs share the same RM codes
		List<ProductPriceMonitorProductRmMappingDo> rmMappingData = Arrays.asList(
			ProductPriceMonitorProductRmMappingDo.builder()
				.productPriceMonitorProductId(1)
				.rmCode("RM001")
				.build(),
			ProductPriceMonitorProductRmMappingDo.builder()
				.productPriceMonitorProductId(2)
				.rmCode("RM001") // Same RM code for different monitor ID
				.build(),
			ProductPriceMonitorProductRmMappingDo.builder()
				.productPriceMonitorProductId(3)
				.rmCode("RM002")
				.build()
		);

		Map<String, RmTeamUserInfo> rmCodeToInfoMap = new HashMap<>();
		RmTeamUserInfo rmInfo1 = createMockRmTeamUserInfo("RM001", "John Doe", "Jane Smith");
		RmTeamUserInfo rmInfo2 = createMockRmTeamUserInfo("RM002", "Bob Wilson", "Alice Brown");
		rmCodeToInfoMap.put("RM001", rmInfo1);
		rmCodeToInfoMap.put("RM002", rmInfo2);

		// Execute
		Map<Integer, List<RmTeamUserInfo>> result = service.groupRmInfoByMonitorId(rmMappingData, rmCodeToInfoMap);

		// Verify
		assertNotNull(result);
		assertEquals(3, result.size());

		// Verify each monitor ID has correct RM info
		assertTrue(result.containsKey(1));
		assertEquals(1, result.get(1).size());
		assertEquals("RM001", result.get(1).get(0).getRmCode());

		assertTrue(result.containsKey(2));
		assertEquals(1, result.get(2).size());
		assertEquals("RM001", result.get(2).get(0).getRmCode());

		assertTrue(result.containsKey(3));
		assertEquals(1, result.get(3).size());
		assertEquals("RM002", result.get(3).get(0).getRmCode());

		// Verify that the same RmTeamUserInfo object is reused
		assertSame(rmInfo1, result.get(1).get(0));
		assertSame(rmInfo1, result.get(2).get(0));
		assertSame(rmInfo2, result.get(3).get(0));
	}

	/**
	 * Helper method to create mock RmTeamUserInfo objects
	 */
	private RmTeamUserInfo createMockRmTeamUserInfo(String rmCode, String rmName, String rmlName) {
		RmTeamUserInfo mockInfo = mock(RmTeamUserInfo.class);
		when(mockInfo.getRmCode()).thenReturn(rmCode);
		when(mockInfo.getRmName()).thenReturn(rmName);
		when(mockInfo.getRmlName()).thenReturn(rmlName);
		return mockInfo;
	}
}

