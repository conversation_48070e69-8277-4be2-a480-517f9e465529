package com.shoalter.mms_product_api.service.product.helper;

import com.google.gson.Gson;
import com.shoalter.mms_product_api.dao.repository.api.SaveProductRecordRowRelationRepository;
import com.shoalter.mms_product_api.dao.repository.api.SaveProductRecordRowRepository;
import com.shoalter.mms_product_api.service.product.pojo.BuProductDto;
import com.shoalter.mms_product_api.service.product.pojo.HktvProductDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterResultDto;
import com.shoalter.mms_product_api.service.product.pojo.SyncVariantCheckDto;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.springframework.context.MessageSource;

import java.util.List;
import java.util.stream.Stream;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

class SyncBaseProductInfoHelperTest {

	private static final String PRIMARY_SKU = "P_SKU";
	private static final String CHILD_SKU1 = "CHILD_SKU1";
	private static final String CHILD_SKU2 = "CHILD_SKU2";
	private static final String DEFAULT_URGENT = "Y";
	private static final String DEFAULT_START_TIME = "2021-01-01T00:00:00";
	private static final String DEFAULT_VOUCHER_TYPE = "voucherType";
	private static final String DEFAULT_VISIBILITY = "visibility";
	private static final String DEFAULT_INVOICE_REMARKS = "invoiceRemarksEn";
	private static final String ERROR_MESSAGE = "Global fields must be the same";

	private SyncBaseProductInfoHelper helperUnderTest;

	@BeforeEach
	void setUp() {
		MessageSource messageSource = mock(MessageSource.class);
		when(messageSource.getMessage("message339", new String[]{SyncVariantCheckDto.FIELD_NAMES.toString()}, null))
			.thenReturn(ERROR_MESSAGE);

		helperUnderTest = new SyncBaseProductInfoHelper(
			new Gson(),
			mock(ProductMasterHelper.class),
			mock(SaveProductRecordRowHelper.class),
			mock(ProductPreProcessingHelper.class),
			mock(SaveProductRecordRowRepository.class),
			mock(SaveProductRecordRowRelationRepository.class),
			messageSource
		);
	}

	@ParameterizedTest(name = "[{index}] [{0}] should return error_message:[{3}]")
	@MethodSource("globalFieldTestCases")
	@DisplayName("Check Variant's GlobalField consistency")
	void checkVariantGlobalField_test(
		String testCaseName,
		List<ProductMasterDto> variants,
		ProductMasterResultDto primarySku,
		String expectedErrorMessage) {

		// when
		String result = helperUnderTest.checkVariantGlobalField(variants, null, primarySku);

		// then
		assertThat(result)
			.as("Should return %s ", expectedErrorMessage != null ? " ErrorMessage " : "null")
			.isEqualTo(expectedErrorMessage);
	}

	private static Stream<Arguments> globalFieldTestCases() {
		return Stream.of(
			Arguments.of(
				"Variants with same global fields",
				createVariantsWithSameGlobalFields(),
				null,
				null
			),
			Arguments.of(
				"Variants with different urgent field",
				createVariantsWithDifferentUrgent(),
				null,
				ERROR_MESSAGE
			),
			Arguments.of(
				"Variants with different voucher type",
				createVariantsWithDifferentVoucherType(),
				null,
				ERROR_MESSAGE
			),
			Arguments.of(
				"Variants with different invoice remarks",
				createVariantsWithDifferentInvoiceRemarks(),
				null,
				ERROR_MESSAGE
			),
			Arguments.of(
				"Variants with different invoice remarks(null and empty)",
				createVariantsWithDifferentInvoiceRemarksWithNullAndEmpty(),
				null,
				null
			),
			Arguments.of(
				"Primary and child SKU with same fields",
				createVariantsWithPrimaryAndChildSkuSameFields(),
				createPrimarySku(),
				null
			),
			Arguments.of(
				"Variants with null field vs empty primary",
				createVariantsSkuWithNullField(),
				createPrimarySkuWithEmptyField(),
				null
			),
			Arguments.of(
				"Primary and child SKU with different fields",
				createVariantsWithPrimaryAndChildSkuDifferentFields(),
				createPrimarySku(),
				ERROR_MESSAGE
			),
			Arguments.of(
				"Variants with null field vs non-null/non-empty primary",
				createVariantsSkuWithNullField(),
				createPrimarySku(),
				ERROR_MESSAGE
			)
		);
	}

	private static List<ProductMasterDto> createVariantsWithSameGlobalFields() {
		return List.of(
			createProductMasterDto(PRIMARY_SKU, DEFAULT_URGENT, DEFAULT_START_TIME, DEFAULT_VOUCHER_TYPE, DEFAULT_VISIBILITY, DEFAULT_INVOICE_REMARKS),
			createProductMasterDto(CHILD_SKU1, DEFAULT_URGENT, DEFAULT_START_TIME, DEFAULT_VOUCHER_TYPE, DEFAULT_VISIBILITY, DEFAULT_INVOICE_REMARKS),
			createProductMasterDto(CHILD_SKU2, DEFAULT_URGENT, DEFAULT_START_TIME, DEFAULT_VOUCHER_TYPE, DEFAULT_VISIBILITY, DEFAULT_INVOICE_REMARKS)
		);
	}

	private static List<ProductMasterDto> createVariantsWithDifferentUrgent() {
		return List.of(
			createProductMasterDto(PRIMARY_SKU, DEFAULT_URGENT, DEFAULT_START_TIME, DEFAULT_VOUCHER_TYPE, DEFAULT_VISIBILITY, DEFAULT_INVOICE_REMARKS),
			createProductMasterDto(CHILD_SKU1, "N", DEFAULT_START_TIME, DEFAULT_VOUCHER_TYPE, DEFAULT_VISIBILITY, DEFAULT_INVOICE_REMARKS)
		);
	}

	private static List<ProductMasterDto> createVariantsWithDifferentVoucherType() {
		return List.of(
			createProductMasterDto(PRIMARY_SKU, DEFAULT_URGENT, DEFAULT_START_TIME, "test_text", DEFAULT_VISIBILITY, DEFAULT_INVOICE_REMARKS),
			createProductMasterDto(CHILD_SKU1, "N", DEFAULT_START_TIME, DEFAULT_VOUCHER_TYPE, DEFAULT_VISIBILITY, DEFAULT_INVOICE_REMARKS)
		);
	}

	private static List<ProductMasterDto> createVariantsWithDifferentInvoiceRemarks() {
		return List.of(
			createProductMasterDto(PRIMARY_SKU, DEFAULT_URGENT, DEFAULT_START_TIME, DEFAULT_VOUCHER_TYPE, DEFAULT_VISIBILITY, "test_text_A"),
			createProductMasterDto(CHILD_SKU1, DEFAULT_URGENT, DEFAULT_START_TIME, DEFAULT_VOUCHER_TYPE, DEFAULT_VISIBILITY, "test_text_B")
		);
	}

	private static List<ProductMasterDto> createVariantsWithDifferentInvoiceRemarksWithNullAndEmpty() {
		return List.of(
			createProductMasterDto(PRIMARY_SKU, DEFAULT_URGENT, DEFAULT_START_TIME, DEFAULT_VOUCHER_TYPE, DEFAULT_VISIBILITY, ""),
			createProductMasterDto(CHILD_SKU1, DEFAULT_URGENT, DEFAULT_START_TIME, DEFAULT_VOUCHER_TYPE, DEFAULT_VISIBILITY, null)
		);
	}

	private static List<ProductMasterDto> createVariantsWithPrimaryAndChildSkuSameFields() {
		return List.of(
			createProductMasterDto(CHILD_SKU1, DEFAULT_URGENT, DEFAULT_START_TIME, DEFAULT_VOUCHER_TYPE, DEFAULT_VISIBILITY, DEFAULT_INVOICE_REMARKS)
		);
	}

	private static List<ProductMasterDto> createVariantsWithPrimaryAndChildSkuDifferentFields() {
		return List.of(
			createProductMasterDto(CHILD_SKU1, DEFAULT_URGENT, "2021-01-02T00:00:00", DEFAULT_VOUCHER_TYPE, DEFAULT_VISIBILITY, DEFAULT_INVOICE_REMARKS)
		);
	}

	private static ProductMasterResultDto createPrimarySku() {
		return createProductMasterResultDto(PRIMARY_SKU, DEFAULT_URGENT, DEFAULT_START_TIME, DEFAULT_VOUCHER_TYPE, DEFAULT_VISIBILITY, DEFAULT_INVOICE_REMARKS);
	}

	private static List<ProductMasterDto> createVariantsSkuWithNullField() {
		return List.of(
			createProductMasterDto(PRIMARY_SKU, DEFAULT_URGENT, DEFAULT_START_TIME, DEFAULT_VOUCHER_TYPE, DEFAULT_VISIBILITY, null)
		);
	}

	private static ProductMasterResultDto createPrimarySkuWithEmptyField() {
		return createProductMasterResultDto(PRIMARY_SKU, DEFAULT_URGENT, DEFAULT_START_TIME, DEFAULT_VOUCHER_TYPE, DEFAULT_VISIBILITY, "");
	}

	private static ProductMasterDto createProductMasterDto(String skuId, String urgent, String featureStartTime, String voucherType, String visibility, String invoiceRemarksEn) {
		ProductMasterDto dto = new ProductMasterDto();
		BuProductDto buProductDto = new BuProductDto();
		HktvProductDto hktv = new HktvProductDto();

		dto.setSkuId(skuId);

		hktv.setFeatureStartTime(featureStartTime);
		hktv.setVoucherType(voucherType);
		hktv.setInvoiceRemarksEn(invoiceRemarksEn);
		hktv.setVisibility(visibility);
		hktv.setUrgent(urgent);

		buProductDto.setHktv(hktv);
		dto.setAdditional(buProductDto);
		return dto;
	}

	private static ProductMasterResultDto createProductMasterResultDto(String skuId, String urgent, String featureStartTime, String voucherType, String visibility, String invoiceRemarksEn) {
		ProductMasterResultDto dto = new ProductMasterResultDto();
		HktvProductDto hktv = new HktvProductDto();
		BuProductDto buProductDto = new BuProductDto();

		dto.setSkuId(skuId);

		hktv.setFeatureStartTime(featureStartTime);
		hktv.setVoucherType(voucherType);
		hktv.setInvoiceRemarksEn(invoiceRemarksEn);
		hktv.setVisibility(visibility);
		hktv.setUrgent(urgent);

		buProductDto.setHktv(hktv);
		dto.setAdditional(buProductDto);
		return dto;
	}
}
