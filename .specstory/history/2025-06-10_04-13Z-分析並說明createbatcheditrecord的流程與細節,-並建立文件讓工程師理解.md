<!-- Generated by SpecStory -->

# 分析並說明createBatchEditRecord的流程與細節, 並建立文件讓工程師理解 (2025-06-10 04:13Z)

_**User**_

分析並說明createBatchEditRecord的流程與細節, 並建立文件讓工程師理解

---

_**Assistant**_

我將分析 `createBatchEditRecord` 方法的流程，並為您創建詳細的技術文件。 現在我來創建一個詳細的技術文件來說明 `createBatchEditRecord` 方法的流程與細節。 我已經為您創建了一份詳細的技術文件，分析了 `createBatchEditRecord` 方法的完整流程。以下是這個方法的核心流程總結：

## 主要流程階段：

1. **數據檢查** - 驗證請求數據有效性和數量限制
2. **店鋪驗證** - 檢查所有店鋪代碼的存在性
3. **重複性檢查** - 防止同一產品被重複處理
4. **數據分類** - 區分有效和無效的產品數據
5. **產品信息查詢** - 獲取產品詳細信息並按商戶分組
6. **權限驗證** - 確保用戶有操作權限
7. **記錄創建** - 分別處理失敗和成功的記錄

## 關鍵特點：

- **泛型設計**：支持多種批量編輯類型
- **商戶隔離**：按商戶分組處理，支持權限控制
- **錯誤處理**：完整的驗證和錯誤收集機制
- **特殊邏輯**：對翻譯類型提供特殊支持
- **異步處理**：創建 PROCESSING 狀態記錄供後續處理

這個方法是批量編輯功能的核心入口，負責數據驗證、權限檢查和記錄創建，為後續的異步處理奠定基礎。文件已保存在 `batch_edit_record_process.md`，工程師可以參考這份文件來理解整個流程的設計和實現細節。

---

_**User**_

分析並說明SingleEditProductService的流程與細節, 並建立文件讓工程師理解

---

_**Assistant**_

我將分析 `SingleEditProductService` 的流程與細節，並創建詳細的技術文件。 現在我來創建詳細的技術文件來說明 `SingleEditProductService` 的流程與細節。 我已經創建了一份詳細的技術文件，全面分析了 `SingleEditProductService` 的流程與細節。以下是這個服務的核心特點總結：

## 主要流程架構：

### 1. **兩階段處理模式**
- **第一階段**: `processing()` - 處理業務邏輯、數據驗證、記錄創建
- **第二階段**: `callProductMasterAndUpdateRecordRow()` - 與 ProductMaster 系統集成

### 2. **核心處理步驟**
1. **權限檢查** - 確保用戶操作權限
2. **變體產品驗證** - 檢查 Matrix 產品的 SKU 存在性
3. **照片格式標準化** - 處理產品照片 URL 格式
4. **記錄創建** - 建立編輯記錄和記錄行
5. **數據檢查與生成** - 最複雜的核心邏輯
6. **變體產品同步** - 處理產品變體關係

### 3. **數據檢查與生成的詳細流程**
- **產品存在性驗證** - 通過 UUID 查詢原始產品數據
- **數據預處理** - HKTV 產品預處理和 IIDS 數據生成
- **促銷活動檢查** - 檢查會員價格活動
- **業務規則檢查** - 根據編輯類型執行不同的檢查邏輯
- **審批流程處理** - 處理需要審批的變更（如佣金率）

## 關鍵設計特點：

### 1. **多種編輯類型支持**
- 一般產品編輯
- 促銷合同編輯（特殊邏輯）
- 變體產品編輯

### 2. **完整的錯誤處理**
- 早期驗證和快速失敗
- 階段性檢查和詳細錯誤訊息
- 事務回滾保證數據一致性

### 3. **異步處理模式**
- ProductMaster 調用採用異步模式
- 通過記錄狀態追踪處理結果

### 4. **業務邏輯集成**
- 價格監控自動觸發
- 審批流程自動檢查
- 匯率計算支持
- 促銷活動檢查

### 5. **狀態管理**
- 完整的狀態流轉：WAIT_START → PROCESSING → REQUESTING_PM → SUCCESS/FAIL
- 每個狀態都有對應的處理邏輯

這個服務是產品管理系統中的核心組件，提供了完整的單產品編輯解決方案，支援複雜的業務邏輯和多系統集成。文件已保存在 `single_edit_product_service_process.md`，工程師可以參考這份文件來理解整個服務的設計思路和實現細節。

---

_**User**_

建立一份新的文件來探討callProductMasterAndUpdateRecordRow之後兩個部分:
- 收到PM(product master) Message Queue (ProductMasterSingleProductHandler)


來完整呈現如何edit product的完整流程
- 建立record
- 同步給PM(product master)
- 收到PM的 Message Queue
- 更新相關db資料/同步給hybris
- 回應PM的 Message Queue


---

_**Assistant**_

我將創建一份完整的文件來探討 edit product 的完整流程，特別是 `callProductMasterAndUpdateRecordRow` 之後的 Message Queue 處理部分。 

---

_**User**_

@agent Try Again

---

_**Assistant**_

我已經創建了一份完整的技術文件，詳細說明了 edit product 的完整流程，特別關注 `callProductMasterAndUpdateRecordRow` 之後的兩個核心部分：

## 文件重點內容：

### 1. **ProductMaster Message Queue 接收處理**
- `ProductMasterSingleProductHandler` 的 MQ 監聽機制
- 手動 ACK 確保消息可靠性
- 按 action 類型分類處理（CREATE/UPDATE/OFFLINE）

### 2. **數據庫更新與 Hybris 同步**
- `SaveProductHelper` 的核心處理邏輯
- 並行處理變體 SKU 組合
- 同步數據到 Hybris 電商系統

### 3. **完整流程架構**
包含六個主要階段：
1. **建立記錄** - SingleEditProductService 處理
2. **同步給 ProductMaster** - CheckRequestPMRecordProductTask
3. **ProductMaster 處理與 MQ** - 中央系統處理
4. **MQ 處理** - ProductMasterSingleProductHandler
5. **數據庫更新與 Hybris 同步** - SaveProductHelper
6. **回應 ProductMaster** - 結果回饋

### 4. **關鍵技術特點**
- **異步並行處理** - 使用 CompletableFuture 和線程池
- **分組批次處理** - 按變體 SKU 分組處理
- **事務管理** - 確保數據一致性
- **錯誤處理** - 多層次錯誤處理和恢復機制
- **性能優化** - 緩存策略和批量操作

### 5. **狀態流轉管理**
從 `WAIT_START` → `PROCESSING` → `REQUESTING_PM` → `CHECKING_PM` → `SUCCESS/FAIL` 的完整狀態機

這份文件提供了完整的系統理解，幫助工程師掌握整個產品編輯流程的設計思路和實現細節。文件已保存在 `complete_edit_product_flow.md`。

---

_**User**_

根據專案內容與文件, 請建立一份說明文件, 說明edit product的完整流程, 以及特別說明相關隱藏的邏輯與流程, 讓工程師透過文件可以完整理解並開發

---

_**Assistant**_

我來創建一份面向工程師的綜合說明文件，特別強調隱藏邏輯與開發注意事項。這份文件將整合現有的技術分析，並加入更多開發實踐的細節。 我來基於已有的分析文件創建一份全面的說明文件，特別注重隱藏邏輯和工程師開發所需的細節。 

---

