<!-- Generated by SpecStory -->

# Merging commit messages for product category handling (2025-03-31 09:33Z)

_**User**_

merge following commit msg into 1:

MS-7356 refactor: improve product category handling for offline SKUs

- Enhance BuProductCategoryHelper with more explicit logic for inactive categories
- Add comprehensive unit tests for both ProductCheckDto and BuProductCategoryHelper


MS-7356 refactor: Extract product category validation logic to BuProductCategoryHelper class

- Create BuProductCategoryHelper class to centralize product category validation logic
- Use Optional for return values to enhance code robustness
- Eliminate code duplication between two classes, improving maintainability


MS-7356 feat: skip category checks for offline products

- Modified product validation logic to skip category checks when product is offline or will be offline
- Added dto methods to determine product offline status

---

_**Assistant**_

Here's a merged commit message that combines all three of your provided messages:

```
MS-7356 refactor: Improve product category handling with offline SKU support

- Skip category validation checks for offline/soon-to-be-offline products
- Extract validation logic to new BuProductCategoryHelper class for better maintainability
- <PERSON><PERSON><PERSON> helper with more explicit handling of inactive categories
- Use Optional for return values to improve code robustness
- Eliminate code duplication between classes
- Add comprehensive unit tests for ProductCheckDto and BuProductCategoryHelper
```

The merged message captures the key changes across all three original commits while maintaining a concise format that shows both the refactoring work and the feature addition for offline products.

---

